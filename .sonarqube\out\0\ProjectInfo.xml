<?xml version="1.0" encoding="utf-8"?>
<ProjectInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.sonarsource.com/msbuild/integration/2015/1">
  <ProjectName>ESCPOS.Printer</ProjectName>
  <ProjectLanguage>C#</ProjectLanguage>
  <ProjectType>Product</ProjectType>
  <ProjectGuid>82c5360c-ec89-4621-a783-a39337f7a3e0</ProjectGuid>
  <FullPath>C:\Users\<USER>\Documents\POSVueChrome\ESCPOS.Printer\ESCPOS.Printer.csproj</FullPath>
  <IsExcluded>false</IsExcluded>
  <AnalysisResults>
    <AnalysisResult Id="FilesToAnalyze" Location="C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\out\0\FilesToAnalyze.txt" />
  </AnalysisResults>
  <AnalysisSettings>
    <Property Name="sonar.cs.roslyn.reportFilePath">C:\Users\<USER>\Documents\POSVueChrome\bin\x86\Debug\ESCPOS.Printer.dll.RoslynCA.json|C:\Users\<USER>\Documents\POSVueChrome\bin\x86\Debug\ESCPOS.Printer.dll.RoslynCA.json</Property>
    <Property Name="sonar.cs.analyzer.projectOutPath">C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\out\0</Property>
  </AnalysisSettings>
  <Configuration>Debug</Configuration>
  <Platform>AnyCPU</Platform>
</ProjectInfo>