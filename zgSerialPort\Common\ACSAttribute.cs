﻿using System;

namespace zgSerialPort.Common
{
    [AttributeUsage(AttributeTargets.Class)]
    public class ACSAttribute : Attribute
    {
        /// <summary>
        /// 设备名称，厂家
        /// </summary>
        public string ModelName { get; set; }

        /// <summary>
        /// 设备型号
        /// </summary>
        public string ModelNumber { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsRelease { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        public int Index { get; set; } = 0;
    }
}
