﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{67A64F08-4244-4C0D-9961-8DDCCCB00580}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>zgBalance</RootNamespace>
    <AssemblyName>zgBalance</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\out\Programs\Libs\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\out\Programs\Libs\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CefSharp">
      <HintPath>..\out\Programs\cefLib\CefSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.6.0.8\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="zgBalance.DH.TM\BalanceDHTMF.cs" />
    <Compile Include="zgBalance.DH.TM\DHPLU.cs" />
    <Compile Include="zgBalanceCommon\BalanceExtension.cs" />
    <Compile Include="zgBalanceCommon\Balance\BalanceAbstract.cs" />
    <Compile Include="zgBalanceCommon\Model\BasePLU.cs" />
    <Compile Include="zgBalanceCommon\Model\Enum.cs" />
    <Compile Include="zgBalanceCommon\ZGBarCode.cs" />
    <Compile Include="zgBalanceFactory.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="zgBalanceCommon\bin\Release\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="zgBalanceCommon\bin\Debug\BalanceCommon.dll" />
    <Content Include="zgBalanceCommon\bin\Debug\BalanceCommon.pdb" />
    <Content Include="zgBalanceCommon\bin\Debug\zgBalanceCommon.dll" />
    <Content Include="zgBalanceCommon\bin\Debug\zgBalanceCommon.pdb" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>