﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Drawing;

namespace zgPrinter.Model
{
    public class PrintElementGoodsDataTable: PrintElementTable
    {
       
        public PrintElementGoodsDataTable(int index, DataTable table, bool printHead = true, Font headFont = null, EnumTextAlign headTextAlign = EnumTextAlign.Center, Font bodyFont = null, EnumTextAlign bodyTextAlign = EnumTextAlign.Left)
            :base(index, table, printHead, headFont, headTextAlign, bodyFont, bodyTextAlign)
        {
            this.ContentType = EnumContentType.GoodsTable;
        }

        public PrintElementGoodsDataTable(int index, DataTable table, DataTable dataStruct, bool printHead = true, Font headFont = null, EnumTextAlign headTextAlign = EnumTextAlign.Center, Font bodyFont = null, EnumTextAlign bodyTextAlign = EnumTextAlign.Left)
            : base(index, table, printHead, headFont, headTextAlign, bodyFont, bodyTextAlign)
        {
            this.DataStruct = DataStruct;
            this.ContentType = EnumContentType.GoodsTable;
        }


        /// <summary>
        /// 单独一行打印的列名
        /// </summary>
        public string SpanColumnName { get; set; }

        public override JObject ToJObject()
        {
            var jsonObject = new JObject();
            jsonObject.Add("index", this.Index);
            jsonObject.Add("contenttype", (int)this.ContentType);
            jsonObject.Add("headtextalign", (int)this.HeadTextAlign);
            jsonObject.Add("bodytextalign", (int)this.BodyTextAlign);
            jsonObject.Add("datamember", this.DataMember);
            jsonObject.Add("printhead", this.PrintHead);
            jsonObject.Add("printseparatorline", this.PrintSeparatorLine);
            jsonObject.Add("headfont", JsonConvert.SerializeObject(this.HeadFont));
            jsonObject.Add("bodyfont", JsonConvert.SerializeObject(this.BodyFont));
            jsonObject.Add("config", JsonConvert.SerializeObject(this.DataStruct));
            
            return jsonObject;
        }
    }
}
