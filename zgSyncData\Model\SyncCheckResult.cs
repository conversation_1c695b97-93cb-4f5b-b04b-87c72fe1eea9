﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgSyncData.Model
{
    public class SyncCheckResult
    {
        [JsonProperty(propertyName: "createAt")]
        public string CreateAt { get; set; }

        [JsonProperty(propertyName: "id")]
        public int ID { get; set; }

        [JsonProperty(propertyName: "modify")]
        public bool Modify { get; set; }

        [JsonProperty(propertyName: "syncedAt")]
        public string SyncedAt { get; set; }
    }
}
