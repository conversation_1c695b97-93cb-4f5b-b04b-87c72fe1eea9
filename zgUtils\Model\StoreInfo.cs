﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgUtils.Model
{
    public class StoreInfo
    {
        public long id { get; set; }
        public string guid { get; set; }
        public string name { get; set; }
        public string addr { get; set; }
        public string industry { get; set; }
        public string contacter { get; set; }
        public string tel { get; set; }
        public string phone { get; set; }
        public string qq { get; set; }
        public string wechat { get; set; }
        [JsonProperty("createAt")]
        public Nullable<System.DateTime> create_at { get; set; }
        [JsonProperty("reviseAt")]
        public Nullable<System.DateTime> revise_at { get; set; }
        public Nullable<long> is_deleted { get; set; }
        public long is_synced { get; set; }
        public string fingerprint { get; set; }
        public string discount_settings { get; set; }
        public string settings { get; set; }
    }
}
