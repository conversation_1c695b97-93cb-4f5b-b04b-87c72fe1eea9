﻿using Newtonsoft.Json.Linq;
using System.Collections.Generic;

namespace zgPrinter.Model
{
    /// <summary>
    /// 小票打印元素-指令，用于打印分割线、空白行等
    /// </summary>
    public class PrintElementCommand : PrintElement
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="index">序号</param>
        /// <param name="cmd">命令</param>
        /// <param name="param">命令参数</param>
        public PrintElementCommand(int index, EnumPrintCommand cmd, Dictionary<string, string> param = null)
        {
            this.Index = index;
            this.Content = cmd;
            this.CommandParameter = new Dictionary<string, string>();
            this.ContentType = EnumContentType.Command;
        }

        public PrintElementCommand() { 
        }
        public new EnumPrintCommand Content { get; set; }

        /// <summary>
        /// 命令参数
        /// </summary>
        public Dictionary<string, string> CommandParameter { get; set; }

        public override JObject ToJObject()
        {
            var jsonObject = new JObject();
            jsonObject.Add("index", this.Index);
            jsonObject.Add("contenttype", (int)this.ContentType);
            jsonObject.Add("textalign", (int)this.TextAlign);
            jsonObject.Add("content", (int)this.Content);
            return jsonObject;
        }
    }
}
