﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgPrinter.Model.PrintElment
{
    public class LabelElement
    {
        public int Index { get; set; }

        public string DataMember { get; set; }

        public string TemplateStr { get; set; }

        public string  TagName { get; set; }

        public int X { get; set; }

        public int Y { get; set; }

        public string CreateTsplContent(params string[] values) {
            var result = string.Empty;
            if ("TEXT".Equals(TagName.ToUpper()))
            {
                var content = string.Format(TemplateStr, values[0]);
                result = $"TEXT {content}";
            }
            else if ("BARCODE".Equals(TagName.ToUpper()))
            {
                var content = string.Format(TemplateStr, values[0]);
                result = $"BARCODE {content}";
            }
            else if ("CMD".Equals(TagName.ToUpper())) {
                result = "";
            }

            return result;
        }
    }
}
