﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace zgUtils.Model
{
    public class ResponseBase
    {
        [JsonProperty("code")]
        public int Code { get; set; }

        [JsonProperty("msg")]
        public string Message { get; set; }
        public string Status { get; set; }
        public string To<PERSON>son(object targert)
        {
            return JsonConvert.SerializeObject(targert);
        }

        public string ToJson()
        {
            return JsonConvert.SerializeObject(this);
        }
    }
    public class ResponseBase<TReponse> : ResponseBase where TReponse : new()
    {
        [JsonProperty("data")]
        public TReponse data { get; set; }

        public ResponseBase()
        {
            this.data = Activator.CreateInstance<TReponse>();
        }
    }
    public class ResponseDict : ResponseBase<Dictionary<string, object>> {}
    public class ResponseArray : ResponseBase<List<Dictionary<string, object>>> {}
    public class ResponseLogin : ResponseBase<LoginDataResponse> { }
    public class ResponseJsonStr : ResponseBase { public string data { get; set; } }
}
