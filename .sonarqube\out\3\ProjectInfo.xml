<?xml version="1.0" encoding="utf-8"?>
<ProjectInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.sonarsource.com/msbuild/integration/2015/1">
  <ProjectName>Plugin</ProjectName>
  <ProjectLanguage>C#</ProjectLanguage>
  <ProjectType>Product</ProjectType>
  <ProjectGuid>827f565e-942b-4002-ae87-e4191012e1b2</ProjectGuid>
  <FullPath>C:\Users\<USER>\Documents\POSVueChrome\SQLitePlugin\Plugin.csproj</FullPath>
  <IsExcluded>false</IsExcluded>
  <AnalysisResults>
    <AnalysisResult Id="FilesToAnalyze" Location="C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\out\3\FilesToAnalyze.txt" />
  </AnalysisResults>
  <AnalysisSettings>
    <Property Name="sonar.cs.roslyn.reportFilePath">C:\Users\<USER>\Documents\POSVueChrome\SQLitePlugin\bin\Debug\Plugin.dll.RoslynCA.json|C:\Users\<USER>\Documents\POSVueChrome\SQLitePlugin\bin\Debug\Plugin.dll.RoslynCA.json</Property>
    <Property Name="sonar.cs.analyzer.projectOutPath">C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\out\3</Property>
  </AnalysisSettings>
  <Configuration>Debug</Configuration>
  <Platform>AnyCPU</Platform>
</ProjectInfo>