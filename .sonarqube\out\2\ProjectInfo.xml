<?xml version="1.0" encoding="utf-8"?>
<ProjectInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.sonarsource.com/msbuild/integration/2015/1">
  <ProjectName>XorPay.SDK</ProjectName>
  <ProjectLanguage>C#</ProjectLanguage>
  <ProjectType>Product</ProjectType>
  <ProjectGuid>8fd02983-b60c-4d40-80ef-7f373bc8e50b</ProjectGuid>
  <FullPath>C:\Users\<USER>\Documents\POSVueChrome\XorPay.SDK\XorPay.SDK.csproj</FullPath>
  <IsExcluded>false</IsExcluded>
  <AnalysisResults>
    <AnalysisResult Id="FilesToAnalyze" Location="C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\out\2\FilesToAnalyze.txt" />
  </AnalysisResults>
  <AnalysisSettings>
    <Property Name="sonar.cs.roslyn.reportFilePath">C:\Users\<USER>\Documents\POSVueChrome\XorPay.SDK\bin\Debug\XorPay.SDK.dll.RoslynCA.json|C:\Users\<USER>\Documents\POSVueChrome\XorPay.SDK\bin\Debug\XorPay.SDK.dll.RoslynCA.json</Property>
    <Property Name="sonar.cs.analyzer.projectOutPath">C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\out\2</Property>
  </AnalysisSettings>
  <Configuration>Debug</Configuration>
  <Platform>AnyCPU</Platform>
</ProjectInfo>