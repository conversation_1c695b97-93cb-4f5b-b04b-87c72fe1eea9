﻿using Newtonsoft.Json;
using System;

namespace zgAdvert.Network
{
    public class ResponseBase
    {
        [JsonProperty("code")]
        public int Code { get; set; }

        [JsonProperty("msg")]
        public string Message { get; set; }
        public string status { get; set; }
    }
    public class ResponseBase<TReponse> : ResponseBase where TReponse : new()
    {
        [JsonProperty("data")]
        public TReponse Body { get; set; }

        public ResponseBase()
        {
            this.Body = Activator.CreateInstance<TReponse>();
        }
    }
}
