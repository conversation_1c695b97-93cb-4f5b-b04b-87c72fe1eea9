﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using zgPrinter.Printer.DrawImg;

namespace zgPrinter
{
    /// <summary>
    /// 图片处理
    /// </summary>
    public class ImageProcess
    {
        public const double PAPER_SIZE_58 = 58.0;
        public const double PAPER_SIZE_80 = 80;
        public const double PAPER_SIZE_QRCODE = 40;
        public void Process(string filePath)
        {
            var savePath = Path.GetDirectoryName(filePath);
            var fileName = Path.GetFileNameWithoutExtension(filePath);
            var fileExt = Path.GetExtension(filePath);
            var newBitMap = new Bitmap(filePath);
            newBitMap.MakeTransparent(Color.White);
            var img58 = ConvertToDestSize(newBitMap, PAPER_SIZE_58);
            var img80 = ConvertToDestSize(newBitMap, PAPER_SIZE_80);

            img58 = BitMap2Gray(img58);
            img58 = BitMap2Binary(img58);
            img80 = BitMap2Gray(img80);
            img80 = BitMap2Binary(img80);

            img58.Save($"{savePath}{fileName}_58{fileExt}");
            img80.Save($"{savePath}{fileName}_80{fileExt}");
        }

        public Bitmap Process(Bitmap img, double paperSize)
        {
            var newBitMap = new Bitmap(img.Width,img.Height);
            Graphics g = Graphics.FromImage(newBitMap);
            g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
            g.Clear(System.Drawing.Color.White);
            g.DrawImage(img, new RectangleF(0, 0, img.Width, img.Height), new RectangleF(0, 0, img.Width, img.Height), GraphicsUnit.Pixel);
            g.Save();
            var imgResult = ConvertToDestSize(newBitMap, paperSize);
            imgResult = BitMap2Gray(imgResult);
            imgResult = BitMap2Binary(imgResult);
            return imgResult;
        }

        private Bitmap ConvertToDestSize(Bitmap img,double paperSize)
        {
            Bitmap result;
            var widthPixel = 400;
            if (widthPixel < img.Width)
            {
                var bil = widthPixel/(Double)img.Width ;
                result = GetThumbnail(img, Convert.ToInt32(img.Width * bil), Convert.ToInt32(img.Height * bil));
                return result;
            }
            else
            {
                return img;
            }
        }

        /// <summary>
        /// 二值化
        /// </summary>
        /// <param name="img"></param>
        /// <returns></returns>
        public static Bitmap BitMap2Binary(Bitmap img)
        {
            int w = img.Width;
            int h = img.Height;
            Bitmap bmp = new Bitmap(w, h, PixelFormat.Format1bppIndexed);
            var data = bmp.LockBits(new Rectangle(0, 0, w, h), ImageLockMode.ReadWrite, PixelFormat.Format1bppIndexed);
            try
            {
                for (int y = 0; y < h; y++)
                {
                    byte[] scan = new byte[(w + 7) / 8];
                    for (int x = 0; x < w; x++)
                    {
                        Color c = img.GetPixel(x, y);
                        if (c.GetBrightness() > 0.5) scan[x / 8] |= (byte)(0x80 >> (x % 8));
                    }
                    Marshal.Copy(scan, 0, (IntPtr)((int)data.Scan0 + data.Stride * y), scan.Length);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                bmp.UnlockBits(data);
            }
            return bmp;
        }

        /// <summary>
        /// 灰度化
        /// </summary>
        /// <param name="bmp"></param>
        /// <returns></returns>
        public static Bitmap BitMap2Gray(Bitmap bmp)
        {

            for (int i = 0; i < bmp.Width; i++)
            {
                for (int j = 0; j < bmp.Height; j++)
                {
                    //获取该点的像素的RGB的颜色
                    Color color = bmp.GetPixel(i, j);
                    //利用公式计算灰度值
                    int gray = (int)(color.R * 0.3 + color.G * 0.59 + color.B * 0.11);
                    Color newColor = Color.FromArgb(gray, gray, gray);
                    bmp.SetPixel(i, j, newColor);
                }
            }
            return bmp;
        }

        /// <summary>
        /// 图片缩放
        /// </summary>
        /// <param name="bmp">图片</param>
        /// <param name="width">目标宽度，若为0，表示宽度按比例缩放</param>
        /// <param name="height">目标长度，若为0，表示长度按比例缩放</param>
        public static Bitmap GetThumbnail(Bitmap bmp, int width, int height)
        {
            if (width == 0 && height == 0)
            {
                width = bmp.Width;
                height = bmp.Height;
            }
            else
            {
                if (width == 0)
                {
                    width = height * bmp.Width / bmp.Height;
                }
                if (height == 0)
                {
                    height = width * bmp.Height / bmp.Width;
                }
            }

            Image imgSource = bmp;
            Bitmap outBmp = new Bitmap(width, height);
            Graphics g = Graphics.FromImage(outBmp);
            g.Clear(Color.Transparent);
            // 设置画布的描绘质量     
            g.CompositingQuality = CompositingQuality.Default;
            g.SmoothingMode = SmoothingMode.Default;
            g.InterpolationMode = InterpolationMode.Default;
            g.DrawImage(imgSource, new Rectangle(0, 0, width, height + 1), 0, 0, imgSource.Width, imgSource.Height, GraphicsUnit.Pixel);
            g.Dispose();
            imgSource.Dispose();
            bmp.Dispose();
            return outBmp;
        }

        [DllImport("gdi32.dll")]
        private static extern int GetDeviceCaps(IntPtr hdc, int Index);
    }
}
