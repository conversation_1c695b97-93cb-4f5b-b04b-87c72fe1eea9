﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using zgAdvert.Extensions;
namespace zgAdvert.Network
{
    public abstract class RequestInfoBase
    {
        public abstract string BaseUrl { get; }

        public abstract string SysUid { get; set; }

        public abstract string AppKey { get; set; }

        public abstract string Version { get; }

        private string CreateSign(string requestBodyJson, string sequence, string timestamp, string uuid)
        {
            return string.Format("appkey={0}&requestBodyJson={1}&sequence={2}&timestamp={3}&uuid={4}&version={5}", new object[]
            {
                this.AppKey,
                requestBodyJson,
                sequence,
                timestamp,
                uuid,
                this.Version
            }).GetMd5Hash();
        }

        public string CreateURl(string requestBodyJson, string sequence, string uuid = "")
        {
            string timestamp = DateTime.Now.GetTimestamp();
            string text = this.CreateSign(requestBodyJson, sequence, timestamp, uuid);
            return Uri.EscapeUriString(string.Format("sequence={0}&timestamp={1}&uuid={2}&version={3}&sign={4}", new object[]
            {
                sequence,
                timestamp,
                uuid,
                this.Version,
                text
            }));
        }
    }
}
