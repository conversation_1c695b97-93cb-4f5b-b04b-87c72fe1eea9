﻿using System.Windows.Forms;

using zgpos.ProgramTemplate;

namespace DefaultPorgrams
{
    public class ZoneIsolationClass : ProgramTemplateClass
    {
        public ZoneIsolationClass()
        {
            this.Name = "母婴版";
            this.Description = "母婴版 [via leon]";
            this.Icon = DefaultProgramResource.ZoneIsolationIcon;
        }

        public override string FileName => System.Reflection.Assembly.GetExecutingAssembly().ManifestModule.ScopeName;
        protected override Form CreateProgramForm()
        {
            return new DefaultProgramForm(
                this.Name,
                this.Icon,
                DefaultProgramResource.ZoneIsolation
                );
        }
    }
}
