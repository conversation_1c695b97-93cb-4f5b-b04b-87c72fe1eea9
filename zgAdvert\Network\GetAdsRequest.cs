﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using zgUtils.Model;

namespace zgAdvert.Network
{
	//public class GetAdsRequest : RequestBase<PackageBase>
	//{
 //       public override string ActionUrl => "/zgzn-advert/advertisements/getCommonAdv?";
 //   }
	//public class GetCustomerAdsRequest : RequestBase<PackageBase>
	//{

	//	public override string ActionParam
	//	{
	//		get
	//		{
	//			return "/zgzn-advert/advertisements/getCustomerAdv?";
	//		}
	//	}
	//}
}
