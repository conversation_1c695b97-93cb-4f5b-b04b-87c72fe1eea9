﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgSyncData.Model
{
    public class SyncDownloadParameter
    {
        /// <summary>
        /// 当前上传页
        /// </summary>
        [JsonProperty(propertyName: "currentPage")]
        public int CurrentPage { get; set; }

        /// <summary>
        /// 每页条数
        /// </summary>
        [JsonProperty(propertyName: "pageSize")]
        public int PageSize { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "sys_uid")]
        public string SysUid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "sys_sid")]
        public string SysSid { get; set; }

        /// <summary>
        /// 拉取数据期间的起始时间
        /// </summary>
        [JsonProperty(propertyName: "start_sync_at")]
        public string StartSyncAt { get; set; }

        /// <summary>
        /// 拉取数据期间的结束时间
        /// </summary>
        [JsonProperty(propertyName: "end_sync_at")]
        public string EndSyncAt { get; set; }
    }
}
