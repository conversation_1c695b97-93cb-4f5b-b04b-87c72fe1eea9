﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace zgpos.ProgramTemplate
{
    public abstract class ProgramTemplateClass : IDisposable
    {
        /* TODO: 右键菜单对象(可为空)，系统桌面图标被右击时弹出
         * 固定菜单项：
         *      新建窗口
         *      ————————
         *      关闭所有已打开窗口
         */
        /// <summary>
        /// 异步锁芯
        /// </summary>
        private readonly object LockSeed = new object();
        /// <summary>
        /// 窗口集合 (允许插件打开多个程序窗口)
        /// </summary>
        public FormCollection ProgramForms { get; } = new FormCollection();

        private Image _icon = zgCommon.Properties.Resources.DefaultProgramIcon;
        /// <summary>
        /// 图标
        /// </summary>
        public Image Icon
        {
            get => this._icon;
            set
            {
                if (value == null)
                {
                    this._icon = zgCommon.Properties.Resources.DefaultProgramIcon;
                }
                else
                {
                    this._icon = new Bitmap(value, new Size(99, 99));
                }
            }
        }
        /// <summary>
        /// token
        /// </summary>
        public virtual string Authorization { get; protected set; } = "";
        /// <summary>
        /// 名称
        /// </summary>
        public virtual string Name { get; protected set; } = "程序";
        /// <summary>
        /// 描述
        /// </summary>
        public virtual string Description { get; protected set; } = "程序描述";

        /// <summary>
        /// 程序集所在文件名称
        /// </summary>
        public abstract string FileName { get; }
        /// <summary>
        /// 程序集所在文件路径
        /// </summary>
        public string FilePath { get; set; }
        public string ClassName { get; set; } = "MainClass";

        /// <summary>
        /// 构造程序窗口
        /// </summary>
        /// <returns>程序窗口</returns>
        public virtual Form GetNewProgramForm()
        {
            Form NewProgramForm = this.CreateProgramForm();
            if (NewProgramForm == null)
            {
                NewProgramForm = new Form();
            }
            //将新窗口添加至程序窗口集合
            this.ProgramForms.Add(NewProgramForm);
            System.Diagnostics.Debug.Print("增加新窗口：{0}，窗口列表总数：{1}", NewProgramForm.GetHashCode().ToString("X"), this.ProgramForms.Count);
            //窗口关闭后从集合移除
            NewProgramForm.FormClosed += new FormClosedEventHandler(
                (s, e) => {
                    this.ProgramForms.Remove(s as Form);
                    System.Diagnostics.Debug.Print("关闭窗口：{0}，窗口列表总数：{1}", (s as Form).GetHashCode().ToString("X"), this.ProgramForms.Count);
                });

            return NewProgramForm;
        }

        protected abstract Form CreateProgramForm();

        ~ProgramTemplateClass()
        {
            (this as IDisposable).Dispose();
        }

        void IDisposable.Dispose()
        {
            System.Diagnostics.Debug.Print("Dispose Program : {0}", this.Name);
            //TODO : 需要测试
            foreach(Form programForm in this.ProgramForms)
            {
                programForm.Close();
            }
            GC.SuppressFinalize(this);
        }
        // 使用 volatile 关键字，防止多线程对对象造成不可预期的影响
        private volatile Form _programForm = null;

        /// <summary>
        /// 启动窗口
        /// </summary>
        public Form ProgramForm
        {
            /* 
             * 不要在子类的构造函数里 new 出 Form，否则会自动创建出 Form 占用很多内存
             * 只需要实现 CreateXXXForm() 方法，第一次访问 Form 时创建对象，不用时 Dispose 掉，防止过多占用内存；
             */
            get
            {
                //线程安全 单实例
                if (this._programForm == null)
                    lock (this.LockSeed)
                        if (this._programForm == null)
                        {
                            this._programForm = this.CreateProgramForm();
                            //this._programForm.Icon = ProgramIcon;
                        }

                return this._programForm;
            }
            set => this._programForm = value;
        }

        public string SetupPackge { get; set; }

        /// <summary>
        /// 启动完成事件（用于系统订阅）
        /// </summary>
        public event EventHandler<EventArgs> ProgramFinished; //{add{}remove{}}

        /// <summary>
        /// 触发启动完成事件
        /// </summary>
        /// <param name="e"></param>
        public void OnProgramFinished(EventArgs e)
        {
            this?.ProgramFinished?.Invoke(this, e);

            //启动完成后自动释放启动画面内存；
            this._programForm?.Dispose();
            this.ProgramForm = null;
        }

    }
}
