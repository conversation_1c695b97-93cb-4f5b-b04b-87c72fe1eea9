﻿using CefSharp;
using System;
using System.Drawing;
using System.Windows.Forms;
using zgUtils;
using zgUtils.Controls;

namespace zgpos
{
    public partial class FormBack : Form
    {
        private int width = 1366;
        private int height = 768;
        Screen screen;
        Boolean isLoad = false;
        
        public FormBack()
        {
            //CheckForIllegalCrossThreadCalls = false;
            InitializeComponent();
            zgAdvert.AdvertFactory.back_bg = zgCommon.Properties.Resources.back_bg;
            zgAdvert.AdvertFactory.htmlstr = zgCommon.Properties.Resources.Advert;
            this.splitContainer1.BackgroundImage = zgCommon.Properties.Resources.top;
            this.splitContainer1.Panel2.BackgroundImage = zgCommon.Properties.Resources.top;
            
        }
        public FormBack(Screen screen) :this()
        {   
            //CheckForIllegalCrossThreadCalls = false;
            this.screen = screen;
            
        }

        private void setScreenBounds() {
            width = screen.Bounds.Width;
            height = screen.Bounds.Height;
            Point point = new Point(screen.Bounds.Location.X, screen.Bounds.Location.Y);
            Location = point;
            Size = new Size(width, height);//设置窗体大小;
            
        }

        private void FormBack_Load(object sender, EventArgs e)
        {
            isLoad = true;
            setScreenBounds();
            try
            {
                this.InvokeOnUiThreadIfRequired(() =>
                {
                    App.CreateBrowserBack(this);
                    this.splitContainer1.Panel2Collapsed = true;
                });

            }
            catch (Exception ex)
            {
                zgLogging.Log.WriterExceptionLog(ex.Message);
            }


            //WindowState = FormWindowState.Maximized;
        }

        private void FormBack_LocationChanged(object sender, EventArgs e)
        {
            if (isLoad)
            {
                setScreenBounds();
            }
        }

        private void FormBack_Shown(object sender, EventArgs e)
        {
            
            
        }
    }
}
