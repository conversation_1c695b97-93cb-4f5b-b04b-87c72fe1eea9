﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace zgPrinter.Model.ZgLabel
{
    [XmlRoot("TCONFIG")]
    public class ZgLabel
    {
        /// <summary>
        /// 0:水平打印，90：垂直打印
        /// </summary>
        public int Rotation { get; set; } = 0;
        /// <summary>
        /// 条码纸宽度，mm
        /// </summary>
        [XmlAttribute("WIDTH")]
        public int Width { get; set; }

        /// <summary>
        /// 条码纸高度mm
        /// </summary>
        [XmlAttribute("HEIGHT")]
        public int Height { get; set; }

        /// <summary>
        /// 两个条码纸之间的距离，mm
        /// </summary>
        [XmlAttribute("GAPM"), DefaultValue(2)]
        public int GapM { get; set; } = 2;

        [XmlIgnore]
        public int PrintCount { get; set; } = 1;

        [XmlIgnore]
        public string PrinterName { get; set; }

        /// <summary>
        /// 元素起始点水平浮动dots
        /// </summary>
        [XmlAttribute("OFFSET_X"), DefaultValue(0)]
        public int XOffset { get; set; } = 0;

        /// <summary>
        /// 元素起始点垂直浮动dots
        /// </summary>
        [XmlAttribute("OFFSET_Y"), DefaultValue(0)]
        public int YOffset { get; set; } = 0;

        /// <summary>
        /// 是否镜面反射
        /// </summary>
        [XmlAttribute("DIRECTION"), DefaultValue(1)]
        public int Direction { get; set; } = 1;

        /// <summary>
        /// 条码高度
        /// </summary>
        [XmlAttribute("BARCODE_HEIHT"), DefaultValue(40)]
        public int BarcodeHeiht { get; set; } = 40;

        [XmlIgnore]
        [JsonProperty("Items")]
        public List<ZgLabelItem> Items { get; set; } = new List<ZgLabelItem>();
    }
}
