﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using ZXing;
using ZXing.Common;
using ZXing.QrCode;
using ZXing.QrCode.Internal;
using ZXing.Rendering;

namespace PrintCore
{
    /// <summary>
    /// 描述：条形码和二维码帮助类
    /// 时间：2018-02-18
    /// </summary>
    public class BarcodeHelper
    {
        /// <summary>
        /// 生成二维码
        /// </summary>
        /// <param name="text">内容</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <returns></returns>
        public static Bitmap Generate1(string text, Double width, Double height)
        {
            BarcodeWriter writer = new BarcodeWriter();
            writer.Format = BarcodeFormat.QR_CODE;
            QrCodeEncodingOptions options = new QrCodeEncodingOptions()
            {
                DisableECI = true,//设置内容编码
                CharacterSet = "UTF-8",  //设置二维码的宽度和高度
                Width = Convert.ToInt32(Math.Ceiling(width * 3.937)),
                Height = Convert.ToInt32(Math.Ceiling(height * 3.937)),
                Margin = 1//设置二维码的边距,单位不是固定像素
            };

            writer.Options = options;
            Bitmap map = writer.Write(text);
            return map;
        }

        /// <summary>
        /// 生成一个条码图片
        /// </summary>
        /// <param name="text">条码文本</param>
        /// <param name="widthMM">条码宽度(毫米)</param>
        /// <param name="heightMM">条码高度(毫米)</param>
        /// <returns></returns>
        public static Bitmap GenerateBarcodeBitmap(string text, int widthMM, int heightMM)
        {
            try
            {
                var barcodeFromat = BarcodeFormat.CODE_128;
                var barcodeStrLength = text.Length;

                //处理13位条码，如果校验位不通过，转换成code128
                if (barcodeStrLength == 13)
                {
                    try
                    {
                        barcodeFromat = BarcodeFormat.EAN_13;
                        var checkCode = GetEAN13CheckCode(text);
                        if (!text.Last().ToString().Equals(checkCode))
                        {
                            barcodeFromat = BarcodeFormat.CODE_128;
                        }
                    }
                    catch (Exception)
                    {
                        barcodeFromat = BarcodeFormat.CODE_128;
                    }
                }
                else if (barcodeStrLength <= 8)
                {
                    barcodeFromat = BarcodeFormat.CODE_93;
                }
                else
                {
                    barcodeFromat = BarcodeFormat.CODE_128;
                }

                var scalingFactor = 1f; //缩放倍数
                var fontSizeMM = 3.0f; //文字大小，毫米
                using (Font font = getBarCodeFont(fontSizeMM, out scalingFactor))
                {
                    var barcodeWidth = getBarCodeWidth(widthMM * 0.9f); //条码宽度
                    var barcodeHeight = (float)heightMM;

                    BarcodeWriter writer = new BarcodeWriter()
                    {
                        Renderer = new BitmapRenderer
                        {
                            TextFont = font
                        },
                        Format = barcodeFromat,
                        Options = new EncodingOptions()
                        {
                            Width = (int)barcodeWidth,
                            Height = (int)Math.Ceiling(barcodeHeight),
                            Margin = 0
                        }
                    };
                    Bitmap map = writer.Write(text);
                    return map;
                }
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
        ///  获取EAN13条码的校验位
        /// </summary>
        /// <param name="barcode"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static string GetEAN13CheckCode(string barcode)
        {
            if (barcode.Length != 13)
            {
                throw new Exception("条码长度不是13位");
            }
            var charArray = barcode.ToCharArray();
            Array.Reverse(charArray);
            double sum = 0;
            for (int i = 1; i < 13; i++)
            {
                sum += i % 2 == 0 ? Convert.ToInt32(charArray[i].ToString()) : Convert.ToInt32(charArray[i].ToString()) * 3;
            }

            var result = Math.Ceiling(sum / 10d) * 10 - sum;
            return result.ToString();
        }

        /// <summary>
        /// 条码宽度转像素
        /// </summary>
        /// <param name="width">条码宽度MM</param>
        /// <returns></returns>
        private static float getBarCodeWidth(float width)
        {
            Graphics graphics = Graphics.FromHwnd(IntPtr.Zero);
            float dpiX = graphics.DpiX;
            float inch = width / 25.4f;
            float px = inch * dpiX;
            return px;
        }

        /// <summary>
        /// 获取桌面缩放比例
        /// </summary>
        /// <returns></returns>
        private static float GetScreenScalingFactor()
        {
            var g = Graphics.FromHwnd(IntPtr.Zero);
            IntPtr desktop = g.GetHdc();
            var physicalScreenHeight = GetDeviceCaps(desktop, (int)DeviceCap.DESKTOPVERTRES);

            var screenScalingFactor =
                (float)physicalScreenHeight / Screen.PrimaryScreen.Bounds.Height;
            //SystemParameters.PrimaryScreenHeight;

            return screenScalingFactor;
        }

        /// 生成一维条形码
        /// </summary>
        /// <param name="text">内容</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <returns></returns>
        public static Bitmap Generate2(string text, int width, int height)
        {
            BarcodeWriter writer = new BarcodeWriter();
            //使用ITF 格式，不能被现在常用的支付宝、微信扫出来
            //如果想生成可识别的可以使用 CODE_128 格式
            //writer.Format = BarcodeFormat.ITF;
            writer.Format = text.Length <= 8 ? ZXing.BarcodeFormat.CODE_93 : text.Length == 13 ? ZXing.BarcodeFormat.EAN_13 : ZXing.BarcodeFormat.CODE_128;
            EncodingOptions options = new EncodingOptions()
            {
                Width = width,
                Height = height,
                Margin = 2
            };
            writer.Options = options;
            Bitmap map = writer.Write(text);
            return map;
        }
        public static Bitmap Generate2_Format(string text, int width, int height, BarcodeFormat format)
        {
            BarcodeWriter writer = new BarcodeWriter();
            //使用ITF 格式，不能被现在常用的支付宝、微信扫出来
            //如果想生成可识别的可以使用 CODE_128 格式
            //writer.Format = BarcodeFormat.ITF;
            writer.Format = format;
            EncodingOptions options = new EncodingOptions()
            {
                Width = width,
                Height = height,
                Margin = 0
            };
            writer.Options = options;
            Bitmap map = writer.Write(text);
            return map;
        }
        /// <summary>
        /// 生成带Logo的二维码
        /// </summary>
        /// <param name="text">内容</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        public static Bitmap Generate3(string text, Double _width, Double _height, string _logopath = "")
        {
            int logo_Width = 0;
            int logo_Height = 0;
            Bitmap logo = null;
            int width = Convert.ToInt32(Math.Ceiling(_width * 3.937));
            int height = Convert.ToInt32(Math.Ceiling(_height * 3.937));
            if (!string.IsNullOrEmpty(_logopath))
            {
                //Logo 图片
                string logoPath = System.AppDomain.CurrentDomain.BaseDirectory + _logopath;// @"\img\logo.png";
                logo = new Bitmap(logoPath);
                logo_Width = logo.Width;
                logo_Height = logo.Height;
            }

            //构造二维码写码器
            MultiFormatWriter writer = new MultiFormatWriter();
            Dictionary<EncodeHintType, object> hint = new Dictionary<EncodeHintType, object>();
            hint.Add(EncodeHintType.CHARACTER_SET, "UTF-8");
            hint.Add(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            //hint.Add(EncodeHintType.MARGIN, 2);//旧版本不起作用，需要手动去除白边

            //生成二维码 
            BitMatrix bm = writer.encode(text, BarcodeFormat.QR_CODE, width + 30, height + 30, hint);
            bm = deleteWhite(bm);
            BarcodeWriter barcodeWriter = new BarcodeWriter();
            Bitmap map = barcodeWriter.Write(bm);

            //获取二维码实际尺寸（去掉二维码两边空白后的实际尺寸）
            int[] rectangle = bm.getEnclosingRectangle();

            //计算插入图片的大小和位置
            int middleW = Math.Min((int)(rectangle[2] / 3), logo_Width);
            int middleH = Math.Min((int)(rectangle[3] / 3), logo_Height);
            int middleL = (map.Width - middleW) / 2;
            int middleT = (map.Height - middleH) / 2;

            Bitmap bmpimg = new Bitmap(map.Width, map.Height, PixelFormat.Format32bppArgb);
            using (Graphics g = Graphics.FromImage(bmpimg))
            {
                g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                g.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                g.DrawImage(map, 0, 0, width, height);
                //白底将二维码插入图片
                g.FillRectangle(Brushes.White, middleL, middleT, middleW, middleH);
                if (logo != null) g.DrawImage(logo, middleL, middleT, middleW, middleH);
            }
            return bmpimg;
        }

        /// <summary>
        /// 删除默认对应的空白
        /// </summary>
        /// <param name="matrix"></param>
        /// <returns></returns>
        private static BitMatrix deleteWhite(BitMatrix matrix)
        {
            int[] rec = matrix.getEnclosingRectangle();
            int resWidth = rec[2] + 1;
            int resHeight = rec[3] + 1;

            BitMatrix resMatrix = new BitMatrix(resWidth, resHeight);
            resMatrix.clear();
            for (int i = 0; i < resWidth; i++)
            {
                for (int j = 0; j < resHeight; j++)
                {
                    if (matrix[i + rec[0], j + rec[1]])
                        resMatrix[i, j] = true;
                }
            }
            return resMatrix;
        }

        /// <summary>
        /// 条码字体毫米转像素，并返回缩放比例
        /// </summary>
        /// <param name="scalingFactor"></param>
        /// <returns></returns>
        private static Font getBarCodeFont(float fontSzieMM, out float scalingFactor)
        {
            scalingFactor = GetScreenScalingFactor();
            Graphics graphics = Graphics.FromHwnd(IntPtr.Zero);
            float dpiX = graphics.DpiX;
            float inch = fontSzieMM / 25.4f;
            float px = inch * dpiX / scalingFactor;
            Font font = new Font("Arial", px * 0.9f);
            return font;
        }

        [DllImport("gdi32.dll")]
        private static extern int GetDeviceCaps(
           IntPtr hdc, // handle to DC
           int nIndex // index of capability
        );

        enum DeviceCap
        {
            VERTRES = 10,
            PHYSICALWIDTH = 110,
            SCALINGFACTORX = 114,
            DESKTOPVERTRES = 117,
            // http://pinvoke.net/default.aspx/gdi32/GetDeviceCaps.html
        }
    }
}