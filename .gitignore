*.suo
*.user
*.userosscache
*.sln.docstates
*.userprefs
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
[Xx]64/
[Xx]86/
[Bb]uild/ 
bld/
[Bb]in/ 
[0o]bj/
[Ll]og/
.vscode/
.vs/
Generated¥ Files/
[Tt]est[Rr]esult*/ 
[Bb]uild[Ll]og.*
*.VisualState.xml 
TestResult.xml
[Dd]ebugPS/
[Rr]eleasePS/
dlldata.c
BenchmarkDotNet.Artifacts/
project.lock.json 
project.fragment.lock.json 
artifacts/
StyleCopReport.xml
*_i.c
*_p.c
*_i.h
*_h.h 
*.ilk 
*.meta 
*.obj 
*.iobj 
*.pch 
*.pdb
*.ipdb 
*.pgc
*.pgd
*.rsp 
*.sbr 
*.tlb 
*.tli 
*.tlh 
*.tmp 
*.tmp_proj 
*.log
*_wpftmp.csproj
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc
_Chutzpah*
ipch/
*.aps
*.ncb
*.opendb
*.opensdf
*.sdf
*.cachefile
*.VC.db
*.VC.VC.opendb
*.psess
*.vsp
*.vspx
*.sap
*.e2e
$tf/
*.gpState
_ReSharper*/
*.[Rr]e[Ss]harper 
*.DotSettings.user
.JustCode
_TeamCity*
*.dotCover
.axoCover/*
!.axoCover/settings.json
*.coverage
*.coveragexml
_NCrunch_*
.*crunch*.local.xml
nCrunchTemp_*
*.mm.*
AutoTest.Net/
.sass-cache/
[Ee]xpress/
DocProject/buildhelp/
DocProject/Help/*.HxT 
DocProject/Help/*.HxC 
DocProject/Help/*.hhc 
DocProject/Help/*.hhk 
DocProject/Help/*.hhp 
DocProject/Help/Htm12 
DocProject/Help/html
publish/
*.[Pp]ublish.xml 
*.azurePubxml
*.pubxml 
*.publishproj
PublishScripts/
*.nupkg
**/[Pp]ackages/*
!**/[Pp]ackages/build/
*.nuget.props
*.nuget.targets
csx/
*.build.csdef
ecf/
rcf/
AppPackages/
BundleArtifacts/
Package.StoreAssociation.xml
_pkginfo.txt
*.appx
*.[Cc]ache
!*.[Cc]ache/
ClientBin/
[Ss]tyle[Cc]op.*
~$*
*~
*.dbmdl
*.dbproj.schemaview
*.jfm 
*.pfx
*.publishsettings 
node_modules/ 
orleans.codegen.cs 
Generated_Code/
_UpgradeReport_Files/
Backup*/
UpgradeLog*.XML 
UpgradeLog*.htm 
ServiceFabricBackup/
*.rptproj.bak 
*.mdf 
*.ldf 
*.ndf
*.rdl.data 
*.bim.layout 
*.bim_*.settings 
*.rptproj.rsuser 
FakesAssemblies/
*.GhostDoc.xml 
.ntvs_analysis.dat 
node_modules/
*.plg
*.opt
*.vbw
**/*.HTMLClient/GeneratedArtifacts 
**/*.DesktopClient/GeneratedArtifacts 
**/*.DesktopClient/ModelManifest.xml
**/*.Server/GeneratedArtifacts 
**/*.Server/ModelManifest.xml
_Pvt_Extensions 
GeneratedArtifacts/
ModelManifest.xml 
.paket/paket.exe 
paket-files/
.fake/
bin/
obj/
packages/
.vs/
.nuget/
.idea/
*.sln.iml 
.cr/
__pycache__/
*.pyc
*.tss
*.jmconfig
*.btp.cs
*.btm.cs
*.odx.cs
*.xsd.cs
OpenCover/
ASALocalRun/
*.binlog 
*.nvuser 
.mfractor/
.localhistory/
Log/
/Application/Source/P0S4UFramework.sln 
/Application/all.proj
/Application/Database/00_Database/DropCreateDatabase.sql
/Application/Tools/EPOSEventSender/SaveJSON 
/Application/Tools/EPOSEventSender/Test/Resu1t
/Application/bin 
/Application/bin2
/install/setup_test
/install/setup
