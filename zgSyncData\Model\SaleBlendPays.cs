﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgSyncData.Model
{
    public class SaleBlendPays
    {
        public static string SyncSaleBlendPays
        {
            get {
                var sql = "replace into sale_blend_pays(id, pay_amt, acct_id, acct_fingerprint, sale_id, sale_fingerprint, fingerprint, remark)"
                        +" select blend.id, tmp.pay_amt, tmp.acct_id, tmp.acct_fingerprint, sales.id as sale_id, tmp.sale_fingerprint, tmp.fingerprint, tmp.remark"
                        +" from tmp_sale_blend_pays as tmp"
                        + " left join sale_blend_pays as blend"
                        + " on tmp.fingerprint = blend.fingerprint"
                        + " left join sales"
                        + " on tmp.sale_fingerprint = sales.fingerprint"
                        + " where sales.is_synced = 1; ";
                return sql;
            }
        }
    }
}
