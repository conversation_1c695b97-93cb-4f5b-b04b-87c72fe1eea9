﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using zgPrinter.Model;

namespace zgPrinter
{
	public static class SocketPool
	{
		// Token: 0x06000167 RID: 359 RVA: 0x0000D5BC File Offset: 0x0000B7BC
		private static void CheckTimerStarted()
		{
			bool flag = false;
			SocketPool.socketLock.AcquireReaderLock(-1);
			try
			{
				bool flag2 = SocketPool.dicSockets.Count > 0;
				if (flag2)
				{
					flag = (SocketPool.checkTimer == null);
				}
				else
				{
					flag = (SocketPool.checkTimer != null);
				}
			}
			finally
			{
				SocketPool.socketLock.ReleaseReaderLock();
			}
			bool flag3 = flag;
			if (flag3)
			{
				SocketPool.socketLock.AcquireWriterLock(-1);
				try
				{
					bool flag4 = SocketPool.dicSockets.Count > 0;
					if (flag4)
					{
						bool flag5 = SocketPool.checkTimer == null;
						if (flag5)
						{
							SocketPool.checkTimer = new Timer(SocketPool.checkCallback);
							SocketPool.checkTimer.Change(250, 250);
						}
					}
					else
					{
						bool flag6 = SocketPool.checkTimer != null;
						if (flag6)
						{
							Timer timer = SocketPool.checkTimer;
							SocketPool.checkTimer = null;
							timer.Dispose();
						}
					}
				}
				finally
				{
					SocketPool.socketLock.ReleaseWriterLock();
				}
			}
		}

		// Token: 0x06000168 RID: 360 RVA: 0x0000D6C4 File Offset: 0x0000B8C4
		private static void OnTimerCallback(object state)
		{
			bool flag = Monitor.TryEnter(SocketPool.checkTimerLock);
			if (flag)
			{
				try
				{
					List<KeyValuePair<string, SocketWrapper>> list = null;
					int tickCount = Environment.TickCount;
					SocketPool.socketLock.AcquireWriterLock(-1);
					try
					{
						foreach (KeyValuePair<string, SocketWrapper> item in SocketPool.dicSockets)
						{
							bool flag2 = item.Value.CheckFree(tickCount, 1000);
							if (flag2)
							{
								bool flag3 = list == null;
								if (flag3)
								{
									list = new List<KeyValuePair<string, SocketWrapper>>();
								}
								list.Add(item);
							}
						}
						bool flag4 = list != null;
						if (flag4)
						{
							foreach (KeyValuePair<string, SocketWrapper> keyValuePair in list)
							{
								SocketPool.dicSockets.Remove(keyValuePair.Key);
							}
						}
					}
					finally
					{
						SocketPool.socketLock.ReleaseWriterLock();
					}
					bool flag5 = list != null;
					if (flag5)
					{
						foreach (KeyValuePair<string, SocketWrapper> keyValuePair2 in list)
						{
							try
							{
								keyValuePair2.Value.ReleaseSocket(false);
								keyValuePair2.Value.Notify();
							}
							catch
							{
							}
						}
						SocketPool.CheckTimerStarted();
					}
				}
				finally
				{
					Monitor.Exit(SocketPool.checkTimerLock);
				}
			}
		}

		// Token: 0x06000169 RID: 361 RVA: 0x0000D8C8 File Offset: 0x0000BAC8
		public static SocketWrapper GetSocket(string address, int port, out string msg)
		{
			msg = "";
			bool flag = Thread.VolatileRead(ref SocketPool.finished) != 0;
			SocketWrapper result;
			if (flag)
			{
				result = SocketPool.InvalidSocket;
			}
			else
			{
				string text = string.Format("{0}:{1}", address, port);
				bool flag2 = false;
				SocketWrapper socketWrapper;
				try
				{
					int num = 0;
					do
					{
						bool flag3 = num == 1;
						if (flag3)
						{
							num = 2;
						}
						bool flag4 = false;
						SocketPool.socketLock.AcquireReaderLock(-1);
						try
						{
							bool flag5 = !SocketPool.dicSockets.TryGetValue(text, out socketWrapper);
							if (flag5)
							{
								LockCookie lockCookie = SocketPool.socketLock.UpgradeToWriterLock(-1);
								try
								{
									bool flag6 = !SocketPool.dicSockets.TryGetValue(text, out socketWrapper);
									if (flag6)
									{
										socketWrapper = (SocketPool.dicSockets[text] = new SocketWrapper());
										flag4 = (flag2 = true);
									}
								}
								finally
								{
									SocketPool.socketLock.DowngradeFromWriterLock(ref lockCookie);
								}
							}
						}
						finally
						{
							SocketPool.socketLock.ReleaseReaderLock();
						}
						bool flag7 = false;
						bool flag8 = flag4;
						if (flag8)
						{
							flag7 = !socketWrapper.ConnectForUsed(address, port, 5000);
						}
						else
						{
							switch (socketWrapper.WaitForUsed())
							{
								case -1:
									flag7 = true;
									break;
								case 0:
									msg = string.Format("{0}网络打印出错：获取连线超时。", text);
									//LoggerFactory.GetLogger().ErrorAsync(msg);
									socketWrapper = SocketPool.InvalidSocket;
									break;
								case 2:
									socketWrapper = SocketPool.InvalidSocket;
									break;
							}
						}
						bool flag9 = flag7;
						if (flag9)
						{
							SocketPool.socketLock.AcquireWriterLock(-1);
							try
							{
								SocketWrapper socketWrapper2;
								bool flag10 = SocketPool.dicSockets.TryGetValue(text, out socketWrapper2) && socketWrapper2 == socketWrapper;
								if (flag10)
								{
									SocketPool.dicSockets.Remove(text);
									flag2 = false;
								}
							}
							finally
							{
								SocketPool.socketLock.ReleaseWriterLock();
							}
							socketWrapper.ReleaseSocket(false);
							socketWrapper.Notify();
							bool flag11 = num == 0;
							if (flag11)
							{
								num = 1;
							}
							else
							{
								msg = string.Format("{0}网络打印出错：无法获取到有效的连线。", text);
								//LoggerFactory.GetLogger().ErrorAsync(msg);
							}
						}
					}
					while (num == 1);
				}
				catch (Exception ex)
				{
					msg = "网络打印出错：获取连线异常" + ((ex != null) ? ex.ToString() : null);
					//LoggerFactory.GetLogger().ErrorAsync(msg);
					socketWrapper = SocketPool.InvalidSocket;
				}
				finally
				{
					bool flag12 = flag2;
					if (flag12)
					{
						SocketPool.CheckTimerStarted();
					}
				}
				result = socketWrapper;
			}
			return result;
		}

		// Token: 0x0600016A RID: 362 RVA: 0x0000DB90 File Offset: 0x0000BD90
		public static void ReleasePool()
		{
			Interlocked.Increment(ref SocketPool.finished);
			List<SocketWrapper> list = new List<SocketWrapper>();
			SocketPool.socketLock.AcquireWriterLock(-1);
			try
			{
				Timer timer = SocketPool.checkTimer;
				bool flag = timer != null;
				if (flag)
				{
					SocketPool.checkTimer = null;
					timer.Dispose();
				}
				foreach (KeyValuePair<string, SocketWrapper> keyValuePair in SocketPool.dicSockets)
				{
					list.Add(keyValuePair.Value);
				}
			}
			finally
			{
				SocketPool.socketLock.ReleaseWriterLock();
			}
			foreach (SocketWrapper socketWrapper in list)
			{
				socketWrapper.ReleaseSocket(true);
			}
		}

		// Token: 0x04000123 RID: 291
		private static Dictionary<string, SocketWrapper> dicSockets = new Dictionary<string, SocketWrapper>(StringComparer.OrdinalIgnoreCase);

		// Token: 0x04000124 RID: 292
		private static ReaderWriterLock socketLock = new ReaderWriterLock();

		// Token: 0x04000125 RID: 293
		private static Timer checkTimer;

		// Token: 0x04000126 RID: 294
		private static TimerCallback checkCallback = new TimerCallback(SocketPool.OnTimerCallback);

		// Token: 0x04000127 RID: 295
		private static object checkTimerLock = new object();

		// Token: 0x04000128 RID: 296
		private const int ReleaseTimeout = 1000;

		// Token: 0x04000129 RID: 297
		private const int TimerInterval = 250;

		// Token: 0x0400012A RID: 298
		private const int ConnectTimeout = 5000;

		// Token: 0x0400012B RID: 299
		private static readonly SocketWrapper InvalidSocket = new SocketWrapper();

		// Token: 0x0400012C RID: 300
		private static int finished = 0;
	}
}
