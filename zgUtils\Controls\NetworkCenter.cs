﻿using Newtonsoft.Json;
using System;
using System.Collections.Specialized;
using System.Configuration;
using System.IO;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Security;
using System.Runtime.InteropServices;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using zgLogging;
using zgUtils.Model;
using zgUtils.Security;
using static System.Net.Mime.MediaTypeNames;

namespace zgUtils.Controls
{
    public class NetworkCenter
    {
        #region Public
        
        private static string IpOrDomainName = "www.zhangguizhinang.com";

        //默认的编码
        private Encoding encoding = Encoding.Default;
        //Post数据编码
        private Encoding postencoding = Encoding.Default;

        //通过IsNetworkAlive方法，来获取电脑的联网状态
        [DllImport("sensapi.dll", SetLastError = true)]
        private static extern bool IsNetworkAlive(out int connectionDescription);

        //通过InternetGetConnectedState方法，来获取电脑的联网状态
        [DllImport("winInet.dll")]
        private static extern bool InternetGetConnectedState(ref IntPtr dwFlag, int dwReserved);

        /// <summary>
        /// IsNetworkAlive函数输出值1-连接局域网
        /// </summary>
        private const int LanNetworkConnectedFlag = 1;
        /// <summary>
        /// 网络是否连接
        /// </summary>
        public static bool IsConnected
        {
            get
            {
                var isNetworkConnected = false;

                try
                {
                    isNetworkConnected = IsNetworkAlive(out int flags);
                    int errCode = Marshal.GetLastWin32Error();
                    if (errCode != 0)
                    {
                        isNetworkConnected = false;
                        Log.WriterNormalLog($"通过{nameof(IsNetworkAlive)}非托管DLL函数，获取网络状态时，遇到异常！");
                    }

                    //IsNetworkAlive检测到是局域网连上网络，则使用InternetGetConnectedState重新确认是否有网
                    if (isNetworkConnected && flags == LanNetworkConnectedFlag)
                    {
                        var dwFlag = new IntPtr();
                        isNetworkConnected = InternetGetConnectedState(ref dwFlag, 0);
                        errCode = Marshal.GetLastWin32Error();
                        if (errCode != 0)
                        {
                            isNetworkConnected = false;
                            Log.WriterNormalLog($"通过{nameof(InternetGetConnectedState)}非托管DLL函数，获取网络状态时，遇到异常！");
                        }
                    }
                    if (!isNetworkConnected)
                    {
                        isNetworkConnected = PingIpOrDomainName(IpOrDomainName);
                    }
                }
                catch
                {

                }


                return isNetworkConnected;
            }
        }
        /// <summary>
        /// 用于检查IP地址或域名是否可以使用TCP/IP协议访问(使用Ping命令),true表示Ping成功,false表示Ping失败!
        /// <para>请自己验证IP格式的正确性</para>
        /// </summary>
        /// <param name="IpOrDomainName">输入参数,表示IP地址或域名</param>
        /// <returns></returns>
        public static bool PingIpOrDomainName(string IpOrDomainName)
        {
            Ping objPingSender = null;
            try
            {
                objPingSender = new Ping();
                int intTimeout = 1000;
                PingReply objPinReply = objPingSender.Send(IpOrDomainName, intTimeout);

                if (objPinReply == null)
                    return false;
                return objPinReply.Status == IPStatus.Success;
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(IpOrDomainName + Environment.NewLine + ex.Message + Environment.NewLine + ex.StackTrace);
                return false;
            }
            finally
            {
                if (objPingSender != null)
                {
                    // 2.0 下ping 的一个bug，需要显示转型后释放
                    IDisposable disposable = objPingSender;
                    disposable.Dispose();
                    objPingSender.Dispose();
                }
            }
        }

        public byte[] GetLocalDb(string Version)
        {
            //string UpdateUrl = CommonApp.Config.UpdateUrl;
            var UpdateUrl = string.Format(CommonApp.Config.ServerUrl.UPDATEURL, Version);
            UpdateUrl = UpdateUrl.Substring(0, UpdateUrl.LastIndexOf("/") + 1) + "data.db";
            Log.WriterNormalLog("GetLocalDb:" + UpdateUrl);
            byte[] fileBytes = null;
            try
            {
                using (var webClient = new WebClient())
                {
                    //webClient.Headers.Add("Authorization", "xxxx");
                    
                    fileBytes = webClient.DownloadData(UpdateUrl);
                }
            }
            catch {
                try
                {
                    using (var webClient = new WebClient())
                    {
                        //webClient.Headers.Add("Authorization", "xxxx");

                        UpdateUrl = UpdateUrl.Substring(0, UpdateUrl.LastIndexOf("/ver/") + 1) + "data.db";
                        Log.WriterNormalLog(UpdateUrl);
                        fileBytes = webClient.DownloadData(UpdateUrl);
                    }
                }
                catch (Exception ex) {
                    Log.WriterExceptionLog(UpdateUrl + " GetLocalDb:" + ex.Message);
                }
                
            }
            
            
            return fileBytes;
        }

        /// <summary>
        /// HttpUploadFile
        /// </summary>
        /// <param name="url"></param>
        /// <param name="file"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public string HttpUploadFile(string url, string file)
        {
            return HttpUploadFile(url, file, null, encoding);
        }

        /// <summary>
        /// HttpUploadFile
        /// </summary>
        /// <param name="url"></param>
        /// <param name="file"></param>
        /// <param name="data"></param>
        /// <param name="encoding"></param>
        /// <returns></returns>
        public string HttpUploadFile(string url, string file, NameValueCollection data, Encoding encoding)
        {
            return HttpUploadFile(url, new string[] { file }, data, encoding);
        }

        internal void AddLoginActionLog()
        {
            try
            {
                LoginLogRequest loginLogParam = new LoginLogRequest("login", JsonConvert.SerializeObject(CommonApp.settings?.setting));
                Log.Info(string.Format("请求远程记录登录LOG：{0}", loginLogParam.ToJson()));
                string url = string.Format("{0}/{1}", CommonApp.Config.ServerUrl.SETTINGURL, "/logs/add?sysUid=" + loginLogParam.sysUid);
                RequestBase<LoginLogRequest> request = new RequestBase<LoginLogRequest>(url, "", loginLogParam);
                //LoginLogRequest request = new LoginLogRequest(url, "", "{\"deviceType\": \"pc\",\"action\": \"login\",\"info\": \"{" + JsonConvert.SerializeObject( loginParam )+ "\"}}");
                NetworkCenter.Instance.SendRequest<RequestBase<LoginLogRequest>, ResponseJsonStr>(request);
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(string.Format("记录登录LOG失败：{0}", ex.Message));
            }


        }

        /// <summary>
        /// HttpUploadFile
        /// </summary>
        /// <param name="url"></param>
        /// <param name="files"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        //public string HttpUploadFile(string url, string[] files, NameValueCollection data)
        //{
        //    return HttpUploadFile(url, files, data, encoding);
        //}

        /// <summary>
        /// HttpUploadFile
        /// </summary>
        /// <param name="url"></param>
        /// <param name="files"></param>
        /// <param name="data"></param>
        /// <param name="encoding"></param>
        /// <returns></returns>
        public string HttpUploadFile(string url, string[] files, NameValueCollection data, Encoding encoding)
        {
            string boundary = "---------------------------" + DateTime.Now.Ticks.ToString("x");
            byte[] boundarybytes = Encoding.ASCII.GetBytes("\r\n--" + boundary + "\r\n");
            byte[] endbytes = Encoding.ASCII.GetBytes("\r\n--" + boundary + "--\r\n");

            //1.HttpWebRequest
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Headers.Add("Authorization", CommonApp.Authorization); 
            request.ContentType = "multipart/form-data; boundary=" + boundary;
            request.Method = "POST";
            request.KeepAlive = true;
            request.Credentials = CredentialCache.DefaultCredentials;
            request.Timeout = 60000;
            using (Stream stream = request.GetRequestStream())
            {
                //1.1 key/value
                string formdataTemplate = "Content-Disposition: form-data; name=\"{0}\"\r\n\r\n{1}";
                if (data != null)
                {
                    foreach (string key in data.Keys)
                    {
                        stream.Write(boundarybytes, 0, boundarybytes.Length);
                        string formitem = string.Format(formdataTemplate, key, data[key]);
                        byte[] formitembytes = encoding.GetBytes(formitem);
                        stream.Write(formitembytes, 0, formitembytes.Length);
                    }
                }

                //1.2 file
                string headerTemplate = "Content-Disposition: form-data; name=\"{0}\"; filename=\"{1}\"\r\nContent-Type: application/octet-stream\r\n\r\n";
                for (int i = 0; i < files.Length; i++)
                {
                    stream.Write(boundarybytes, 0, boundarybytes.Length);
                    string header = string.Format(headerTemplate, "files", Path.GetFileName(files[i]));
                    byte[] headerbytes = encoding.GetBytes(header);
                    stream.Write(headerbytes, 0, headerbytes.Length);
                    using (FileStream fileStream = File.Open(files[i], FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                    {
                        byte[] bytes = new byte[fileStream.Length];
                        fileStream.Read(bytes, 0, bytes.Length);
                        fileStream.Seek(0, SeekOrigin.Begin);
                        stream.Write(bytes, 0, bytes.Length);
                    }

                }

                //1.3 form end
                stream.Write(endbytes, 0, endbytes.Length);
            }
            //2.WebResponse
            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            using (StreamReader stream = new StreamReader(response.GetResponseStream()))
            {
                return stream.ReadToEnd();
            }
        }
        #endregion

        public static NetworkCenter Instance
        {
            get
            {
                if (NetworkCenter.instance == null)
                {
                    NetworkCenter.instance = new NetworkCenter();
                }
                return NetworkCenter.instance;
            }
        }

        //private HttpWebRequest GetRequest<TRequest>(TRequest request) where TRequest : RequestBase
        //{
            
        //    var url = request.CreateURL();
        //    Log.WriterNormalLog(string.Format("win客户端发起请求-->HTTP URL:{0}", url));
        //    HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(url);
        //    httpWebRequest.Method = request.Method??"Post";
        //    httpWebRequest.ContentType = "application/json; charset=UTF-8";
        //    httpWebRequest.Headers.Add("Authorization", CommonApp.Authorization);
        //    if(CommonApp.userinfo?.uid>0)httpWebRequest.Headers.Add("uid", CommonApp.userinfo.uid.ToString());
        //    httpWebRequest.Timeout = 10000;
        //    if (httpWebRequest.Method.ToLower().Equals("post")) {
        //        Stream requestStream = httpWebRequest.GetRequestStream();
        //        string text2 = request.PostContent();
        //        Log.Debug(text2);
        //        byte[] bytes = Encoding.UTF8.GetBytes(text2);
        //        if (bytes != null)
        //        {
        //            requestStream.Write(bytes, 0, bytes.Length);
        //        }
        //        requestStream.Close();
        //    }           
            
        //    return httpWebRequest;
            
        //}

        //private TResponse InitResponse<TResponse>(Stream streamResponse, TResponse responseResult) where TResponse : ResponseBase, new()
        //{
        //    try
        //    {
        //        using (MemoryStream memoryStream = new MemoryStream())
        //        {
        //            streamResponse.CopyTo(memoryStream);
        //            byte[] bytes = memoryStream.ToArray();
        //            string @string = Encoding.UTF8.GetString(bytes);
        //            if (!string.IsNullOrWhiteSpace(@string))
        //            {
        //                Log.Debug(string.Format("请求成功服务端回复{0}", @string));
        //                responseResult = JsonConvert.DeserializeObject<TResponse>(@string);
        //            }
        //            else
        //            {
        //                responseResult.Message = "返回空";
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.WriterExceptionLog(string.Format("请求失败信息:" + ex.Message, new object[0]));
        //        responseResult.Message = ex.Message;
        //    }
        //    return responseResult;
        //}
        ////2.0 https
        //public bool CheckValidationResult(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors errors)
        //{
        //    return true;
        //}
        public void SendRequest<TRequest, TResponse>(TRequest request, Action<TResponse> response) where TRequest : RequestBase where TResponse : ResponseBase, new()
        {
            TResponse responseResult = Activator.CreateInstance<TResponse>();
            responseResult.Code = -801;
            try
            {
                Req<string> req = new Req<string>(request.CreateURL(), request.PostContent());
                response(ApiHelper.PostAsJsonAsync<string, TResponse>(req).Result.RespData);

                //ServicePointManager.ServerCertificateValidationCallback = new System.Net.Security.RemoteCertificateValidationCallback(CheckValidationResult);
                //System.Net.ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
                //HttpWebRequest webRequest = this.GetRequest<TRequest>(request);
                //webRequest.BeginGetResponse(delegate (IAsyncResult result)
                //{
                //    try
                //    {
                //        Stream responseStream = ((HttpWebResponse)webRequest.EndGetResponse(result)).GetResponseStream();
                //        responseResult = this.InitResponse<TResponse>(responseStream, responseResult);
                //        response(responseResult);
                //        webRequest.Abort();
                //    }
                //    catch (Exception ex2)
                //    {
                //        webRequest.Abort();
                //        Log.WriterExceptionLog(string.Format("请求失败信息:" + ex2.Message, new object[0]));
                //        responseResult.Message = ex2.Message;
                //        response(responseResult);
                //    }
                //}, webRequest);
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(JsonConvert.SerializeObject(request));
                Log.WriterExceptionLog(JsonConvert.SerializeObject(responseResult));
                responseResult.Message = ex.Message;
                response(responseResult);
            }
        }

        public TResponse SendRequest<TRequest, TResponse>(TRequest request) where TRequest : RequestBase where TResponse : ResponseBase, new()
        {
            TResponse tresponse = Activator.CreateInstance<TResponse>();
            tresponse.Code = -801;
            try
            {
                Req<string> req = new Req<string>(request.CreateURL(), request.PostContent());
                tresponse = ApiHelper.PostAsJsonAsync<string, TResponse>(req).Result.RespData;
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
                tresponse.Message = ex.Message;
            }
            //HttpWebRequest httpWebRequest = null;
            //try
            //{
            //    httpWebRequest = this.GetRequest<TRequest>(request);
            //    Stream responseStream = ((HttpWebResponse)httpWebRequest.GetResponse()).GetResponseStream();
            //    tresponse = this.InitResponse<TResponse>(responseStream, tresponse);
            //}
            //catch (Exception ex)
            //{
            //    Log.WriterExceptionLog(ex.Message);
            //    tresponse.Message = ex.Message;
            //}
            //if (httpWebRequest != null)
            //{
            //    httpWebRequest.Abort();
            //}
            return tresponse;
        }

        private static NetworkCenter instance;
    }
}
