﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgPrinter.Model
{
    [Serializable]
    public class PrintConfig
    {
        /// <summary>
        /// 打印模板名称
        /// </summary>
        [JsonProperty("template")]
        public string Template { get; set; }

        /// <summary>
        /// 打印机
        /// </summary>
        [JsonProperty("printer")]
        public string Printer { get; set; }

        /// <summary>
        /// 打印类型，ip，usb
        /// </summary>
        [JsonProperty("printtype")]
        public string PrintType { get; set; }

        /// <summary>
        /// 打印份数
        /// </summary>
        [JsonProperty("printcount")]
        public int PrintCount { get; set; } = 1;

        /// <summary>
        /// 行高
        /// </summary>
        [JsonProperty("lineheight")]
        public int LineHeight { get; set; } = 60;


        /// <summary>
        /// ip地址
        /// </summary>
        [JsonProperty("ip")]
        public string IP { get; set; }

        /// <summary>
        /// 打印机端口
        /// </summary>
        [JsonProperty("port")]
        public int Port { get; set; } = 9100;

        /// <summary>
        /// 打印完成后走纸行数
        /// </summary>
        [JsonProperty("spaceline")]
        public int SpaceLine { get; set; } = 5;

        /// <summary>
        /// 打印间隔毫秒数
        /// </summary>
        [JsonProperty("interval")]
        public int IntervalTime { get; set; } = 100;
    }
}
