﻿using System;
using System.Windows.Forms;
using CefSharp;
using CefSharp.WinForms;
using zgLogging;
using zgpos.Browser;
using zgUtils;
using zgUtils.Controls;

namespace zgpos.EventHandlers
{
    public static class ChromiumWebBrowserLoadHandler
    {
        public static void DoLoadingStateChanged(object sender, LoadingStateChangedEventArgs e)
        {
            Log.WriterNormalLog($"{nameof(ChromiumWebBrowserLoadHandler)}: {nameof(DoLoadingStateChanged)} => {e.Browser.MainFrame?.Url} {(e.IsLoading ? "is" : "not")} loading");
        }

        public static void DoFrameLoadStart(object sender, FrameLoadStartEventArgs e)
        {
            Log.WriterNormalLog($"{nameof(ChromiumWebBrowserLoadHandler)}: {nameof(DoFrameLoadStart)} => {e.Url} {e.TransitionType}");
        }

        public static void DoFrameLoadEnd(object sender, FrameLoadEndEventArgs e)
        {
            FormMain fm = (FormMain)CommonApp.mainform;
            
            fm.BeginInvoke(new Action(() =>
            {

                try
                {

                    if (!CommonApp.ISPRODCT)
                    {
                        e.Browser.ShowDevTools();
                        System.Threading.Thread.Sleep(100);
                    }
                    fm.CefShowed();
                    Log.WriterNormalLog("ShowDialog:start.......................................");
                }
                catch (Exception ex)
                {
                    Log.WriterNormalLog("DoFrameLoadEnd:ShowDialog---" + ex.Message);
                }
                

            }));

            Log.WriterNormalLog($"{nameof(ChromiumWebBrowserLoadHandler)}: {nameof(DoFrameLoadEnd)} => {e.Url} {e.HttpStatusCode}");
        }

        public static void DoLoadError(object sender, LoadErrorEventArgs e)
        {
            if (e.ErrorCode == CefErrorCode.Aborted) return;

            Log.WriterNormalLog($"{nameof(ChromiumWebBrowserLoadHandler)}: {nameof(DoLoadError)} => {e.FailedUrl} {e.ErrorCode} {e.ErrorText}");
            // 加载失败时使用静态页面显示错误信息
            var htmlContent = ChromiumRegisterResourceHandler.GetErrorPageHtml($"Load Error. (Code = {e.ErrorCode})", $"{e.FailedUrl}<br/>{e.ErrorText}");
            e.Frame.LoadHtml(htmlContent, true);
        }
    }
}
