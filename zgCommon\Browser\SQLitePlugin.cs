﻿using CefSharp;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using zgLogging;
using zgUtils;
using zgUtils.Controls;

namespace zgpos.Browser
{
    public class SQLitePlugin
    {
        /// <summary>
        /// 查询SQL，返回Json字符串
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public async Task<string> ExecuteQuery(string sql, IJavascriptCallback successBack = null, IJavascriptCallback errorBack = null)
        {
            CommonApp.ClearMemory(sql);
            var results = "[]";
            if (string.IsNullOrEmpty(CommonApp.sysUid)) return results;
            try
            {
                DataTable dt = SQLiteHelper.ExecuteDataTable(sql, SQLiteHelper.writeConnection);
                results = JsonConvert.SerializeObject(dt);
                if (successBack != null)
                {
                    using (successBack)
                    {
                        await successBack.ExecuteAsync(results);
                    }
                }
            }
            catch (Exception ex)
            {
                results = "{\"code\":\"" + ex.HResult + "\",\"message\":\"" + ex.Message + "\"}";
                results = results.Replace("\r\n", "");
                if (errorBack != null)
                {
                    using (errorBack)
                    {
                        await errorBack.ExecuteAsync(results);
                    }
                }
                Log.Error(ex.Message);
                throw ex;
            }
            finally
            {
                Log.Debug(results.Length > 500 ? results.Substring(0, 500) : results);
            }
            return results;
        }

        public async Task<string> ExecuteRead(string sql, IJavascriptCallback successBack = null, IJavascriptCallback errorBack = null)
        {
            CommonApp.ClearMemory(sql);
            var results = "[]";
            if (string.IsNullOrEmpty(CommonApp.sysUid)) return results;
            try
            {
                DataTable dt = SQLiteHelper.ExecuteDataTable(sql);
                results = JsonConvert.SerializeObject(dt);
                if (successBack != null)
                {
                    using (successBack)
                    {
                        await successBack.ExecuteAsync(results);
                    }
                }
            }
            catch (Exception ex)
            {
                results = "{\"code\":\"" + ex.HResult + "\",\"message\":\"" + ex.Message + "\"}";
                results = results.Replace("\r\n", "");
                if (errorBack != null)
                {
                    using (errorBack)
                    {
                        await errorBack.ExecuteAsync(results);
                    }
                }
                Log.Error(ex.Message);
                throw ex;
            }
            finally
            {
                Log.Debug(results.Length > 500 ? results.Substring(0, 500) : results);
            }
            return results;
        }

        public async Task<string> SyncExecuteQuery(string sql, IJavascriptCallback successBack = null, IJavascriptCallback errorBack = null)
        {
            CommonApp.ClearMemory(sql);
            var results = "[]";
            if (string.IsNullOrEmpty(CommonApp.sysUid)) return results;
            try
            {
                SQLiteHelper.InitSyncReadConnection();
                DataTable dt = SQLiteHelper.ExecuteDataTable(sql, SQLiteHelper.syncReadConnection);
                results = JsonConvert.SerializeObject(dt);
                if (successBack != null)
                {
                    using (successBack)
                    {
                        await successBack.ExecuteAsync(results);
                    }
                }
            }
            catch (Exception ex)
            {
                results = "{\"code\":\"" + ex.HResult + "\",\"message\":\"" + ex.Message + "\"}";
                results = results.Replace("\r\n", "");
                if (errorBack != null)
                {
                    using (errorBack)
                    {
                        await errorBack.ExecuteAsync(results);
                    }
                }
                Log.Error(ex.Message);
                throw ex;
            }
            finally
            {
                Log.Debug(results.Length > 500 ? results.Substring(0, 500) : results);
            }
            return results;
        }

        /// <summary>
        /// 对SQLite数据库执行增删改操作，返回受影响行数
        /// </summary>
        /// <param name="sql"></param>
        /// <returns>受影响行数</returns>
        public async Task<int> ExecuteSql(string sql, IJavascriptCallback successBack = null, IJavascriptCallback errorBack = null)
        {
            CommonApp.ClearMemory(sql);
            if (string.IsNullOrEmpty(CommonApp.sysUid)) return 0;
            try
            {
                int count = SQLiteHelper.ExecuteNonQuery(sql);
                if (successBack != null)
                {
                    using (successBack)
                    {
                        await successBack.ExecuteAsync(count);
                    }
                }
                return count;
            }
            catch (Exception ex)
            {
                if (errorBack != null)
                {
                    using (errorBack)
                    {
                        string results = "{\"code\":\"" + ex.HResult + "\",\"message\":\"" + ex.Message + "\"}";
                        results = results.Replace("\r\n", "");
                        await errorBack.ExecuteAsync(results);
                    }
                }

                throw ex;
            }
        }

        /// <summary>
        /// 批量执行SQL，成功返回1，失败返回0
        /// </summary>
        /// <param name="sqls"></param>
        /// <param name="successBack"></param>
        /// <param name="errorBack"></param>
        /// <returns></returns>
        public async Task<int> ExecuteSqlBatch_sqls(string sqls, IJavascriptCallback successBack = null, IJavascriptCallback errorBack = null)
        {
            if (string.IsNullOrEmpty(CommonApp.sysUid)) return 0;
            CommonApp.ClearMemory(sqls);

            sqls = sqls.Trim();

            if (string.IsNullOrEmpty(sqls))
            {
                if (successBack != null)
                {
                    using (successBack)
                    {
                        await successBack.ExecuteAsync(1);
                    }
                }
            }

            if (sqls.EndsWith(";"))
            {
                sqls = sqls.Substring(0, sqls.Length - 1);
            }

            List<string> sqlList = sqls.Split(';').ToList();
            return await ExecuteSqlBatch(sqlList, successBack, errorBack);
        }

        /// <summary>
        /// 批量执行SQL，成功返回1，失败返回0
        /// </summary>
        /// <param name="sqlList"></param>
        /// <param name="successBack"></param>
        /// <param name="errorBack"></param>
        /// <returns></returns>
        public async Task<int> ExecuteSqlBatch(List<string> sqlList, IJavascriptCallback successBack = null, IJavascriptCallback errorBack = null)
        {
            if (string.IsNullOrEmpty(CommonApp.sysUid)) return 0;
            if (sqlList == null || sqlList.Count <= 0)
            {
                if (successBack != null)
                {
                    using (successBack)
                    {
                        await successBack.ExecuteAsync(1);
                    }
                }
                return 1;
            }
            try
            {
                SQLiteHelper.ExecuteNonQueryBatch(sqlList);
                if (successBack != null)
                {
                    using (successBack)
                    {
                        await successBack.ExecuteAsync(1);
                    }
                }
                return 1;
            }
            catch (Exception ex)
            {
                if (errorBack != null)
                {
                    using (errorBack)
                    {
                        string results = "{\"code\":\"" + ex.HResult + "\",\"message\":\"" + ex.Message + "\"}";
                        results = results.Replace("\r\n", "");
                        await errorBack.ExecuteAsync(results);
                    }
                }

                throw ex;
            }
        }

        public async Task<int> Decompression(string directoryName, string fileName,
            IJavascriptCallback successBack = null, IJavascriptCallback errorBack = null)
        {
            try
            {
                string fileDirectory = string.Format(@"{0}Resources\sync-csv\{1}", CommonApp.BaseDirectory, directoryName);
                string filePath = string.Format(@"{0}\{1}", fileDirectory, fileName);
                FileInfo fileInfo = new FileInfo(filePath);
                int res = 0;
                if (fileInfo.Exists && fileInfo.Length > 0)
                {
                    string exePath = @"..\..\sqlite3.exe";
                    string arguments = string.Format(@"/C cd {0} && {1} -Axf {2}", fileDirectory, exePath, filePath);

                    ProcessStartInfo startInfo = new ProcessStartInfo("cmd.exe", arguments)
                    {
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        Verb = "runas"
                    };
                    using (Process process = new Process
                    {
                        StartInfo = startInfo
                    })
                    {
                        process.Start();
                        process.WaitForExit();
                    }

                    res = 1;
                }

                if (successBack != null)
                {
                    using (successBack)
                    {
                        await successBack.ExecuteAsync(res);
                    }
                }
                return res;
            }
            catch (Exception ex)
            {
                if (errorBack != null)
                {
                    using (errorBack)
                    {
                        string results = "{\"code\":\"" + ex.HResult + "\",\"message\":\"" + ex.Message + "\"}";
                        await errorBack.ExecuteAsync(results);
                    }
                }

                throw ex;
            }
        }

        public async Task<int> ExecuteImport(string directoryName, string fileName, string[] tables,
            IJavascriptCallback successBack = null, IJavascriptCallback errorBack = null)
        {
            try 
            {
                string fileDirectory = string.Format(@"{0}Resources\sync-csv\{1}", CommonApp.BaseDirectory, directoryName);
                string filePath = string.Format(@"{0}\{1}", fileDirectory, fileName);
                FileInfo fileInfo = new FileInfo(filePath);
                int res = 0;
                if (fileInfo.Exists && fileInfo.Length > 0 && tables != null && tables.Length > 0)
                {
                    string options = @"-separator "",""";
                    string exePath = @"..\..\sqlite3.exe";
                    string dbPath = string.Format(@"..\..\..\{0}-{1}-{2}\data\{0}.db", CommonApp.SubDirectory, CommonApp.sysUid, CommonApp.sysSid);
                    string arguments = string.Format(@"/C cd {0}", fileDirectory);

                    foreach (string tableName in tables)
                    {
                        filePath = string.Format(@"{0}\{1}.{2}", fileDirectory, tableName, "csv");
                        arguments = string.Format(@"{0} && {1} {2} {3} "".import {4}.csv tmp_{4}""", arguments, exePath, options, dbPath, tableName);
                    }
                    arguments = string.Format(@"{0} && cd ../ && rd /s /q ""{1}""", arguments, directoryName);

                    ProcessStartInfo startInfo = new ProcessStartInfo("cmd.exe", arguments)
                    {
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        Verb = "runas"
                    };
                    using (Process process = new Process {
                        StartInfo = startInfo
                    }) 
                    {
                        process.Start();
                        process.WaitForExit();
                    }

                    res = 1;
                }

                if (successBack != null)
                {
                    using (successBack)
                    {
                        await successBack.ExecuteAsync(res);
                    }
                }
                return res;
            }
            catch (Exception ex)
            {
                if (errorBack != null)
                {
                    using (errorBack)
                    {
                        string results = "{\"code\":\"" + ex.HResult + "\",\"message\":\"" + ex.Message + "\"}";
                        await errorBack.ExecuteAsync(results);
                    }
                }

                throw ex;
            }
        }

        public async Task<int> ExecuteExport(string tableName, string sql = "", IJavascriptCallback successBack = null, IJavascriptCallback errorBack = null)
        {
            try
            {
                int res = 0;
                if (sql == null || "".Equals(sql.Trim())) 
                {
                    if (successBack != null)
                    {
                        using (successBack)
                        {
                            await successBack.ExecuteAsync(res);
                        }
                    }
                    return res;
                }

                string options = "-noheader -csv";
                string exePath = string.Format(@"{0}Resources\sqlite3.exe", CommonApp.BaseDirectory);
                string dbPath = string.Format(@"{0}{1}-{2}-{3}\data\{1}.db", CommonApp.BaseDirectory, CommonApp.SubDirectory, CommonApp.sysUid, CommonApp.sysSid);
                string csvPath = string.Format(@"{0}Resources\sync-csv\{1}.csv", CommonApp.BaseDirectory, tableName);
                string arguments = string.Format("/C {0} {1} {2} \"{3}\" > {4}", exePath, options, dbPath, sql, csvPath);
                ProcessStartInfo startInfo = new ProcessStartInfo("cmd.exe", arguments)
                {
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    Verb = "runas"
                };
                using (Process process = new Process {
                    StartInfo = startInfo
                }) 
                {
                    process.Start();
                    process.WaitForExit();
                }

                FileInfo csvFile = new FileInfo(csvPath);
                if (csvFile.Exists && csvFile.Length > 0) 
                {
                    res = 1;
                }

                if (successBack != null)
                {
                    using (successBack)
                    {
                        await successBack.ExecuteAsync(res);
                    }
                }
                return res;
            }
            catch (Exception ex)
            {
                if (errorBack != null)
                {
                    using (errorBack)
                    {
                        string results = "{\"code\":\"" + ex.HResult + "\",\"message\":\"" + ex.Message + "\"}";
                        results = results.Replace("\r\n", "");
                        await errorBack.ExecuteAsync(results);
                    }
                }

                throw ex;
            }
        }
    }
}
