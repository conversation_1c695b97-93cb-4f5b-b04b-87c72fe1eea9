﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgSyncData.Model
{
    [Serializable]
    public class Sales
    {
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "acctId")]
        public int AcctId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "acctSyncG")]
        public string AcctSyncG { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "billAmt")]
        public decimal BillAmt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "blendPays")]
        public List<string> BlendPays { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "changeAmt")]
        public decimal ChangeAmt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "code")]
        public string Code { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "companyId")]
        public int CompanyId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "companySyncG")]
        public string CompanySyncG { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "createAt")]
        public string CreateAt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "deductionAmt")]
        public decimal DeductionAmt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "disc")]
        public int Disc { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "discAmt")]
        public decimal DiscAmt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "expressAmt")]
        public decimal ExpressAmt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "id")]
        public int Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "info1")]
        public string Info1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "info2")]
        public string Info2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "isCalc")]
        public int IsCalc { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "isDel")]
        public int IsDel { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "isNew")]
        public int IsNew { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "isSync")]
        public int IsSync { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "items")]
        public List<SaleItem> Items { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "optOn")]
        public DateTime OptOn { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "oweAmt")]
        public decimal OweAmt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "payAmt")]
        public decimal PayAmt { get; set; }


        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "remark")]
        public string Remark { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "reviseAt")]
        public string ReviseAt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "sorderId")]
        public int SorderId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "sorderSyncG")]
        public string SorderSyncG { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "syncAt")]
        public string SyncAt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "syncG")]
        public string SyncG { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "syncN")]
        public int SyncN { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "syncU")]
        public string SyncU { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "syncV")]
        public string SyncV { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "sysSid")]
        public int sysSid { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "sysUid")]
        public string SysUid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "tradeSrc")]
        public int TradeSrc { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "tradeType")]
        public int TradeType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "tye")]
        public int Tye { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "uid")]
        public int Uid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "userId")]
        public int UserId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "userSyncG")]
        public string UserSyncG { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "username")]
        public string Username { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "vipid")]
        public int VipID { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "vipmobile")]
        public string VipMobile { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "vipname")]
        public string VipName { get; set; }



        public string ToInsertSql()
        {
            var sql = "insert into dataCopy.tmp_sales(uid, opt_date, code, in_out, supplier_fingerprint, bill_amt, disc_amt, disc, pay_amt, owe_amt, change_amt, remark,account_fingerprint, createtime, revisetime, is_deleted, fingerprint, vipid, vipname, vipmobile) values ";
            sql += $"({Uid}, '{OptOn}', '{Code}', {Tye}, '{CompanySyncG}', {BillAmt}, {DiscAmt}, {Disc}, {PayAmt}, {OweAmt}, {ChangeAmt}, '{Remark}'," +
                $"'{ AcctSyncG}', '{ CreateAt}', '{ SyncAt}', { IsDel},  '{ SyncG}', { VipID}, '{ VipName}', '{ VipMobile}')";
            return sql;
        }

        public static string SyncSalesInsert{
            get
            {
                var sql = "insert into sales(uid, opt_date, code, in_out, supplier_id, bill_amt, disc_amt, disc, pay_amt, owe_amt, change_amt, remark, account_id,"
                 + " createtime, revisetime, is_deleted, is_synced, fingerprint, vipid, vipname, vipmobile)"
                 + " select tmp.uid, tmp.opt_date, tmp.code, tmp.in_out, suppliers.id, tmp.bill_amt, tmp.disc_amt, tmp.disc, tmp.pay_amt, tmp.owe_amt, tmp.change_amt,"
                 + " tmp.remark, accounts.id, tmp.createtime, tmp.revisetime, tmp.is_deleted, 1, tmp.fingerprint, tmp.vipid, tmp.vipname, tmp.vipmobile"
                 + " from dataCopy.tmp_sales tmp"
                 + " left join sales"
                 + " on tmp.fingerprint = sales.fingerprint"
                 + " left join suppliers"
                 + " on tmp.supplier_fingerprint = suppliers.fingerprint"
                 + " left join accounts"
                 + " on tmp.account_fingerprint = accounts.fingerprint"
                 + " where sales.fingerprint is null; ";
                return sql;
            }
        }

        public static string SyncSalesUpdate
        {
            get {
                var sql = "update sales set"
                  + " is_deleted = (select is_deleted from dataCopy.tmp_sales where fingerprint = sales.fingerprint),"
                  + " revisetime = (select revisetime from dataCopy.tmp_sales where fingerprint = sales.fingerprint)"
                  + " where is_synced = 1"
                  + " and exists(select 1 from dataCopy.tmp_sales where fingerprint = sales.fingerprint); ";

                return sql;
            }
        }
    }

}
