﻿using CefSharp;
using Newtonsoft.Json;
using System;
using System.Data;
using System.Data.SQLite;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using zgLogging;
using zgUtils;
using zgUtils.Controls;
using zgUtils.Model;

namespace zgpos
{
    public class ActionLog
    {
        public static string uuid { get; set; }
        public static string PostUrl = "";
        private static string filepath
        {
            get
            {
                return CommonApp.Directory + "data\\log.db";
            }
        }

        public static string ConnectionString { get { return "Data source=" + filepath + ";Version=3;Integrated Security=True;Max Pool Size=10;"; } }

        /// <summary>
        /// 查询SQL，返回Json字符串
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public void post(object parms, IJavascriptCallback successback = null, IJavascriptCallback errorBack = null)
        {
            var results = "{}";
            var jsonStr = "{}";
            if (parms.GetType().Name.Equals("String"))
            {
                string pattern = @"(\\[^bfrnt\\/'\""])";
                jsonStr = System.Text.RegularExpressions.Regex.Replace(Convert.ToString(parms), pattern, "\\$1");
            }
            else
            {
                jsonStr = JsonConvert.SerializeObject(parms);
            }

            try
            {
                ActionLogClass actionLogClass = JsonConvert.DeserializeObject<ActionLogClass>(jsonStr);
                if ("register".Equals(actionLogClass.action))
                {

                    if (ActionLogPost(actionLogClass.ToDataTable()))
                    {
                        results = "{\"code\":\"0\",\"message\":\"注册成功。\"}";
                    }
                    else
                    {
                        using (errorBack)
                        {
                            results = "{\"code\":\"-1\",\"message\":\"远程服务器写入Log失败。\"}";
                            errorBack.ExecuteAsync(results);
                            return;
                        }
                    }
                }
                else
                {
                    string sql = "";
                    if (!File.Exists(filepath))
                    {
                        SQLiteHelper.OpenDatabase("1.0", filepath);
                        SQLiteConnection.CreateFile(filepath);
                        sql += "CREATE TABLE IF NOT EXISTS \"actionlog\" (" +
                            "  \"time\" text," +
                            "  \"uuid\" text," +
                            "  \"uid\" text," +
                            "  \"page\" text," +
                            "  \"action\" text," +
                            "  \"description\" text," +
                            "  \"info\" text" +
                            "); ";
                    }

                    sql += string.Format("insert into actionlog values('{0}','{1}','{2}','{3}','{4}','{5}','{6}');",
                            actionLogClass.time, actionLogClass.uuid, actionLogClass.uid, actionLogClass.page, actionLogClass.action, actionLogClass.description, actionLogClass.info?.ToString() ?? "{}");
                    string sql1 = string.Format("select rowid, * from actionlog limit {0};", actionLogClass.maxUploadRows);

                    bool logout = "logout".Equals(actionLogClass.action);
                    bool logUp = false;
                    bool logUpRes = false;
                    DataTable dt;
                    using (SQLiteConnection connection = new SQLiteConnection(ConnectionString)) 
                    {
                        connection.Open();
                        SQLiteHelper.ExecuteNonQuery(sql, connection);

                        dt = SQLiteHelper.ExecuteDataTable(sql1, connection);
                        logUp = dt?.Rows.Count >= actionLogClass.maxUploadRows || logout;
                        if (logUp)
                        {
                            logUpRes = ActionLogPost(dt);
                            if (logUpRes)
                            {
                                SQLiteHelper.ExecuteNonQuery(string.Format("delete from actionlog where rowid in(select rowid from actionlog limit {0});", actionLogClass.maxUploadRows), connection);
                            }
                        }
                    }

                    if (logUp && !logUpRes && logout && errorBack != null) 
                    {
                        using (errorBack)
                        {
                            results = "{\"code\":\"-1\",\"message\":\"远程服务器写入Log失败。\"}";
                            errorBack.ExecuteAsync(results);
                            return;
                        }
                    }

                    results = JsonConvert.SerializeObject(dt);
                }

                if (successback != null)
                {
                    using (successback)
                    {
                        successback.ExecuteAsync(results);
                    }
                }
            }
            catch (Exception ex)
            {
                results = "{\"code\":\"" + ex.HResult + "\",\"message\":\"" + ex.Message + "\"}";
                results = results.Replace("\r\n", "");
                //if (errorBack != null)
                //{
                //    using (errorBack)
                //    {
                //        errorBack.ExecuteAsync(results);
                //    }
                //}
                Log.WriterExceptionLog(results);

            }
            //finally
            //{
            //    Log.WriterNormalLog(jsonStr);
            //}
        }

        private bool ActionLogPost(DataTable dt)
        {
            bool result = false;
            try
            {
                if (string.IsNullOrEmpty(PostUrl))
                {
                    PostUrl = string.Format("{0}/zgzn-logs/logs/action?device={1}&ver={2}", CommonApp.Config?.ServerUrl.ACTIONLOGURL, CommonApp.DeviceId, CommonApp.Assembly?.GetName().Version.ToString());
                }
                string param = JsonConvert.SerializeObject(dt);
                RequestJsonStr request = new RequestJsonStr(PostUrl, "", param);
                ResponseBase res = NetworkCenter.Instance.SendRequest<RequestJsonStr, ResponseBase>(request);
                if (res?.Code == 200)
                {
                    Log.WriterNormalLog(string.Format("{0}:{1} response:{2}", PostUrl, dt?.Rows.Count, JsonConvert.SerializeObject(res)));
                    result = true;
                }
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }
            return result;
        }

        private class ActionLogClass
        {
            public long uid { get { return CommonApp.userinfo?.uid ?? 1; } }
            public string page { get; set; }
            public string action { get; set; }
            public string description { get; set; }
            public string deviceType { get; set; } = "pc";
            public SystemInfo info { get; set; }
            public string token { get { return CommonApp.userinfo?.Accesstoken; } }
            public string uuid
            {
                get
                {

                    if ("login".Equals(this.action) || string.IsNullOrEmpty(ActionLog.uuid))
                    {
                        ActionLog.uuid = EncryptWithMD5(this.uid + CommonApp.DeviceId);
                    }
                    string uuid = ActionLog.uuid;
                    if ("logout".Equals(this.action))
                    {
                        ActionLog.uuid = "";
                    }
                    return uuid;
                }
            }
            public string time { get; } = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss FFFF");
            public int maxUploadRows { get; set; } = 10;
            public string partitionId { get; set; }
            internal DataTable ToDataTable()
            {
                DataTable dt = new DataTable();
                dt.Columns.Add("time");
                dt.Columns.Add("uuid");
                dt.Columns.Add("uid");
                dt.Columns.Add("page");
                dt.Columns.Add("action");
                dt.Columns.Add("description");
                dt.Columns.Add("info");
                dt.Columns.Add("partitionId");

                dt.Rows.Add();
                dt.Rows[0]["time"] = this.time;
                dt.Rows[0]["uuid"] = this.uuid;
                dt.Rows[0]["uid"] = this.uid;
                dt.Rows[0]["page"] = this.page;
                dt.Rows[0]["action"] = this.action;
                dt.Rows[0]["description"] = this.description;
                dt.Rows[0]["info"] = this.info;
                dt.Rows[0]["partitionId"] = this.partitionId;

                return dt;
            }

            private string EncryptWithMD5(string source)
            {
                byte[] sor = Encoding.UTF8.GetBytes(source);
                MD5 md5 = MD5.Create();
                byte[] result = md5.ComputeHash(sor);
                StringBuilder strbul = new StringBuilder(40);
                for (int i = 0; i < result.Length; i++)
                {
                    strbul.Append(result[i].ToString("x2"));//加密结果"x2"结果为32位,"x3"结果为48位,"x4"结果为64位

                }
                return strbul.ToString();
            }
        }
        /// <summary>
        /// "mac":"6C4B90CD0240","version":"2.5.0.0214(体验版)","cip":"**************","cid":"370600","cname":"山东省烟台市"
        /// </summary>
        private class SystemInfo
        {
            public string SetupPackge { get { return CommonApp.SetupPackge; } }

            public string mac { get; set; }
            public string version { get; set; }
            public string cip { get; set; }
            public string cid { get; set; }
            public string cname { get; set; }
            public SysProp system
            {
                get
                {
                    try
                    {
                        return SysProp.Instance;
                    }
                    catch
                    {
                        return null;
                    }

                }
            }
            public override string ToString()
            {
                return JsonConvert.SerializeObject(this);
            }
        }
    }

}
