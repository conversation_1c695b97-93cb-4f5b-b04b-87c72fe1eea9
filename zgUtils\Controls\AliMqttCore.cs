﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using uPLibrary.Networking.M2Mqtt;
using uPLibrary.Networking.M2Mqtt.Messages;
using zgLogging;
using zgUtils.Model;

namespace zgUtils.Controls
{
    public class AliMqttCore
    {
        //private Task task;

        private bool isDisposable;

        public bool isconnected;

        private static AliMqttCore _instance;

        private MqttClient client;

        private string[] topics;
        private byte[] qos;

        public delegate void PublishReceivedDelegate(MqttMessage message);

        public delegate void ConnectedDelegate();

        private MqttOptions mqttOptions;

        Action<MqttMsgPublishEventArgs> ReceivedMessage;
        private Task task;
        private readonly int millisecondsTimeout=15000;

        public event AliMqttCore.ConnectedDelegate Connected;
        private string TopicUrl { get; set; }
        private string ClientId { get; set; }

        private string Topic_user_name { get; set; }

        private string Topic_password { get; set; }
        private string Topic_sub_command { get; set; }
        public static AliMqttCore Instance
        {
            get
            {
                if (AliMqttCore._instance == null)
                {
                    AliMqttCore._instance = new AliMqttCore();
                    Log.WriterNormalLog("AliMqttCore-初始化MQTT");
                }
                return AliMqttCore._instance;
            }
        }
        public AliMqttCore()
        {
            this.mqttOptions = CommonApp.Config.mqttOptions;
            Log.WriterNormalLog("AliMqttCore-监听系统网络状态");
            this.task = new Task(new Action(this.WaiteConnect));
            Log.WriterNormalLog("AliMqttCore-重连线程初始化完毕");
        }
        private bool IsConnected
        {
            get
            {
                return this.client != null && (this.client.IsConnected && this.client.IsRunning && this.isconnected);
            }
        }
        private void WaiteConnect()
        {
            while (!this.isDisposable)
            {
                Thread.Sleep(millisecondsTimeout);
                if (!this.IsConnected && !this.isDisposable)
                {
                    this.NewConnect();
                }
            }
        }
        private void NewConnect()
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(this.TopicUrl))
                {
                    if (this.client != null)
                    {
                        this.client.MqttMsgPublishReceived -= this.ClientMqttMsgPublishReceived;
                        this.client.MqttMsgUnsubscribed -= this.ClientMqttMsgUnsubscribed;
                        this.client.MqttMsgSubscribed -= this.ClientMqttMsgSubscribed;
                        this.client.ConnectionClosed -= this.ClientConnectionClosed;
                        this.isconnected = false;
                        this.client = null;
                    }
                    this.client = new MqttClient(this.TopicUrl);
                    this.client.MqttMsgPublishReceived += this.ClientMqttMsgPublishReceived;
                    this.client.MqttMsgUnsubscribed += this.ClientMqttMsgUnsubscribed;
                    this.client.MqttMsgSubscribed += this.ClientMqttMsgSubscribed;
                    this.client.ConnectionClosed += this.ClientConnectionClosed;
                    this.Connect();
                }
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog("Reconnect:" + ex.Message);
            }
            finally
            {
                if (this.task.Status != TaskStatus.Running)
                {
                    this.task.Start();
                }
            }
        }
        private void ClientConnectionClosed(object sender, EventArgs e)
        {
            this.isconnected = false;
            if (!this.isDisposable)
            {
                Log.WriterNormalLog(string.Format("MQTT连接中断,{0}秒后尝试重连", millisecondsTimeout/1000));
            }
            else
            {
                Log.WriterNormalLog("MQTT连接中断, 程序已经退出,不进行重连");
            }
        }

        private void ClientMqttMsgPublishReceived(object sender, MqttMsgPublishEventArgs e)
        {
            string @string = Encoding.UTF8.GetString(e.Message);
            try
            {
               
                Log.WriterNormalLog(String.Format("Received = {0} on topic = {1};{2}",@string, e.Topic,e.DupFlag?"（重复发送）":""));
                this.ReceivedMessage(e);
            }
            catch {
                Log.Error("Received = " + @string + " on topic =" + e.Topic);
            }
            
        }
        public void P2PCallback(MqttReceivedMessage p2pClient, string Topic,string message)
        {
            Log.WriterNormalLog(string.Format("新订单{0}来自{1}", p2pClient.orderData, p2pClient.clientId));
            if (!string.IsNullOrEmpty(p2pClient.clientId))
            {
                string newclientId = string.Format("{0}/p2p/{1}", mqttOptions.parentTopic, p2pClient.clientId);
                client.Publish(newclientId, Encoding.UTF8.GetBytes(message), this.mqttOptions.qos, false);
            }
                

        }

        private void ClientMqttMsgUnsubscribed(object sender, MqttMsgUnsubscribedEventArgs e)
        {
            Log.WriterNormalLog("Unsubscribed for id = " + e.MessageId);
        }

        private void ClientMqttMsgSubscribed(object sender, MqttMsgSubscribedEventArgs e)
        {
            Log.WriterNormalLog("Subscribed for id = " + e.MessageId);
        }

        public void Dispose()
        {
            this.isDisposable = true;
            if (this.client != null)
            {
                try
                {
                    this.client.MqttMsgPublishReceived -= this.ClientMqttMsgPublishReceived;
                    this.client.MqttMsgUnsubscribed -= this.ClientMqttMsgUnsubscribed;
                    this.client.ConnectionClosed -= this.ClientConnectionClosed;
                    this.client.MqttMsgSubscribed -= this.ClientMqttMsgSubscribed;
                    this.client.Unsubscribe(this.topics);
                    this.client.Disconnect();
                    this.isconnected = false;
                    this.client = null;
                }
                catch (Exception ex)
                {
                    Log.WriterExceptionLog(ex.Message);
                }
            }
        }

        
        private bool Connect()
        {
            try
            {
                if (this.client.Connect(this.ClientId, this.Topic_user_name, this.Topic_password, false, this.mqttOptions.qos, false, null, null, this.mqttOptions.cleanSession, 60) != 0)
                {
                    Log.WriterNormalLog("AliMQTTCore-未能连接到MQTT");
                    return false;
                }
                
                this.client.Subscribe(this.topics,this.qos );
                Log.WriterNormalLog("AliMQTTCore-设备连接物联网成功");
                this.isconnected = true;
                AliMqttCore.ConnectedDelegate connected = this.Connected;
                if (connected != null)
                {
                    connected();
                }
            }
            catch (Exception ex)
            {
                this.isconnected = false;
                if (this.isDisposable)
                {
                    return false;
                }
                Log.WriterNormalLog("AliMQTTCore-连接物联网失败:" + ex.Message.ToString());
                return false;
            }
            return true;
        }
        public bool Subscribe(Action<MqttMsgPublishEventArgs> receivedMessage,bool orderConnect,bool defaultConnect)
        {
            try
            {
                if (!orderConnect && !defaultConnect) return false;
                this.ReceivedMessage = receivedMessage;
                this.TopicUrl = mqttOptions.brokerUrl;
                this.ClientId = string.Format("{0}@@@{1}-{2}-{3}", mqttOptions.groupId, CommonApp.sysUid, CommonApp.sysSid,CommonApp.DeviceId?.Replace("-",""));//NetworkConnection.GetMac();
                if (this.ClientId.Length > 64)
                {
                    this.ClientId = this.ClientId.Substring(0, 64);
                }
                this.Topic_user_name = string.Format("Signature|{0}|{1}",mqttOptions.accessKey, mqttOptions.instanceId);
                this.Topic_password = HMACSHA1(mqttOptions.secretKey, this.ClientId);
                List<string> _topics = new List<string>();
                List<byte> _qos = new List<byte>();
                //餐饮版 小程序点餐 订单提醒
                if (orderConnect && !String.IsNullOrEmpty(mqttOptions.parentTopic)) {
                    _topics.Add(string.Format("{0}/{1}-{2}", mqttOptions.parentTopic, CommonApp.sysUid, CommonApp.sysSid));
                    _qos.Add(this.mqttOptions.qos);
                    _topics.Add(string.Format("{0}/{1}-{2}-{3}", mqttOptions.parentTopic, CommonApp.Config?.Industry?.schema ?? CommonApp.Config?.Industry?.subName, CommonApp.sysUid, CommonApp.sysSid));
                    _qos.Add(this.mqttOptions.qos);
                }
                //连接mqtt 订阅 注销 等功能 其他设备提醒
                if (defaultConnect && !String.IsNullOrEmpty(mqttOptions.defaultTopic)) {
                    _topics.Add(string.Format("{0}/{1}-{2}-{3}", mqttOptions.defaultTopic, CommonApp.Config?.Industry?.schema ?? CommonApp.Config?.Industry?.subName, CommonApp.sysUid, CommonApp.sysSid));
                    _qos.Add(this.mqttOptions.qos);
                }
                //下单等
                if (!String.IsNullOrEmpty(mqttOptions.commonTopic))
                {
                    _topics.Add(string.Format("{0}/{1}-{2}-{3}", mqttOptions.commonTopic, CommonApp.Config?.Industry?.schema ?? CommonApp.Config?.Industry?.subName, CommonApp.sysUid, CommonApp.sysSid));
                    _qos.Add(this.mqttOptions.qos);
                }
               
                this.topics = _topics.ToArray();
                this.qos = _qos.ToArray();
                //this.Topic_report_command = config.TopicSubReport;
                Log.WriterNormalLog("AliMQTTCore-开始新的连接:"+ this.ClientId);
                Log.WriterNormalLog("订阅:" + string.Join(";",_topics.ToArray()));
                this.NewConnect();
                Log.WriterNormalLog("AliMQTTCore-连接执行完毕");
            }
            catch (Exception ex) {
                Log.WriterExceptionLog(ex.Message);
            }
            
            return this.IsConnected;
        }
        /// <summary>
        /// 发送消息
        /// </summary>
        /// <param name="message"></param>
        public void publishSynced(string message)
        {
            try
            {
                if (this.client == null)
                {
                    NewConnect();
                }
            
                if (this.client != null && !String.IsNullOrEmpty(mqttOptions.commonTopic))
                {
                    string Topic = string.Format("{0}/{1}-{2}-{3}", mqttOptions.commonTopic, CommonApp.Config?.Industry?.schema ?? CommonApp.Config?.Industry?.subName, CommonApp.sysUid, CommonApp.sysSid);
                    Log.Info(String.Format("Topic:{0},message:{1}",topics,message));
                    byte[] bytes = Encoding.UTF8.GetBytes(message);
                    this.client.Publish(Topic, bytes);
                }
            }
            catch (Exception ex) {
                Log.WriterExceptionLog(ex.Message);
            }
}
        static void client_recvMsg(object sender, MqttMsgPublishEventArgs e)
        {
            // access data bytes throug e.Message
            Console.WriteLine("Recv Msg : Topic is " + e.Topic + " ,Body is " + Encoding.UTF8.GetString(e.Message));
        }
        static void client_publishSuccess(object sender, MqttMsgPublishedEventArgs e)
        {
            // access data bytes throug e.Message
            Console.WriteLine("Publish Msg  Success");
        }
        static void client_connectLose(object sender, EventArgs e)
        {
            // access data bytes throug e.Message
            Console.WriteLine("Connect Lost,Try Reconnect");
        }
        public static string HMACSHA1(string key, string dataToSign)
        {
            Byte[] secretBytes = UTF8Encoding.UTF8.GetBytes(key);
            HMACSHA1 hmac = new HMACSHA1(secretBytes);
            Byte[] dataBytes = UTF8Encoding.UTF8.GetBytes(dataToSign);
            Byte[] calcHash = hmac.ComputeHash(dataBytes);
            String calcHashString = Convert.ToBase64String(calcHash);
            return calcHashString;
        }
    }
}
