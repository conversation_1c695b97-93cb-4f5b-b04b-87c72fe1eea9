﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgUtils.Model
{
    public class MyResponse
    {
        public int code { get; set; }
        public Dictionary<string,object> data { get; set; }
        public string msg { get; set; }
    }
    public class MyResponseArray
    {
        public int code { get; set; }
        public List<Dictionary<string, object>> data { get; set; }
        public string msg { get; set; }
    }
    public class DeviceResponse
    {
        public int code { get; set; }
        public string data { get; set; }
        public string msg { get; set; }
        
    }
    
}
