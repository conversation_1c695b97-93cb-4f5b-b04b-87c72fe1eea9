﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.IO;
using zgLogging;
using zgUtils.Controls;
using zgUtils.Security;

namespace zgUtils.Model
{
    public class ConfigManager
    {
        public static string DeviceURL { get; internal set; } = "/device/getConfigInfo";
        private static ConfigManager instance;

        public static ConfigManager Instance
        {
            get
            {
                if (ConfigManager.instance == null)
                {
                    ConfigManager.instance = new ConfigManager();

                }
                string configFileUrl = null;
                ConfigInfo configInfo_ini = null;
                ConfigManager configManager = null;
                ConfigData configData_remote = null;
                ConfigData configData = null;

                try
                {
                    CurrentUserConfig currentUserConfig = new CurrentUserConfig();
                    currentUserConfig.data = FileManager.GetConfig("CurrentUserConfig", CommonApp.ProgramFile);
                    var currentUser = currentUserConfig.CurrentUser;
                    string currentUserDirectory = CommonApp.BaseDirectory + CommonApp.SubDirectory + "-" + currentUser?.sysUid + "-" + currentUser?.sysSid + "\\";


                    if (CommonApp.step == CommonApp.Setp.Step_1)
                    {//登录按下
                        if (!System.IO.Directory.Exists(currentUserDirectory))
                        {
                            Log.WriterNormalLog("用户信息未找到，重新创建......");
                            System.IO.Directory.CreateDirectory(currentUserDirectory);
                        }
                    }
                    if (string.IsNullOrEmpty(currentUser?.sysUid) || !System.IO.Directory.Exists(currentUserDirectory))
                    {
                        Log.WriterNormalLog("初次登录,运程用户配置信息取得开始......");
                        configData_remote = getConfig();
                        Log.Debug("初次登录,运程用户配置信息取得完成：" + configData_remote?.getConfigInfo());
                    }
                    else
                    {//登录过||登录按钮按下
                        Log.WriterNormalLog("非初次登录,本地用户配置信息取得开始......");
                        CommonApp.sysUid = currentUser?.sysUid;
                        CommonApp.sysSid = currentUser?.sysSid;
                        if (!string.IsNullOrEmpty(currentUser?.Accesstoken))
                        {
                            CommonApp.Authorization = currentUser.Accesstoken;
                        }
                        configFileUrl = Path.Combine(currentUserDirectory, "config.ini");
                        if (CommonApp.step == CommonApp.Setp.Step_0 && File.Exists(configFileUrl))
                        {//登录前
                            configInfo_ini = FileManager.ReadIniData<ConfigInfo>(configFileUrl);
                            if (!string.IsNullOrEmpty(configInfo_ini?.LocalConfig?.config?.configUrl))
                            {
                                CommonApp.DefaultConfigURL = configInfo_ini.LocalConfig.config.configUrl;
                            }
                        }


                        if (CommonApp.step == CommonApp.Setp.Step_1)
                        {//登录按下


                        }
                        Log.WriterNormalLog("非初次登录,远程用户配置信息取得开始......");
                        configData_remote = getConfig();

                    }
                    configData = configData_remote ?? configInfo_ini?.LocalConfig?.config;
                    ConfigManager.instance.Base.Clear();
                    configManager = JsonConvert.DeserializeObject<ConfigManager>(configData?.getConfigInfo() ?? "");
                    if (configManager != null)
                    {
                        ConfigManager.instance.systemName = configData?.systemName;
                        ConfigManager.instance.subCode = configData?.subCode;
                        ConfigManager.instance.subName = configData?.subName;
                        ConfigManager.instance.Base = configManager?.Base;
                        ConfigManager.instance.ServerUrl = configManager?.ServerUrl;
                        ConfigManager.instance.Mqtt= configManager.Mqtt;
                        ConfigManager.instance.mqttOptions = configManager.mqttOptions;
                        ConfigManager.instance.Industry = IndustryClass.GetIndustry(configManager.Industry);
                        ConfigManager.instance.syncOptions = configManager.syncOptions;
                        CommonApp.Mqtt = configManager.Mqtt;
                        if (ConfigManager.instance.Industry != null && !String.IsNullOrEmpty(ConfigManager.instance.Industry.name)) {
                            CommonApp.systemTitle = CommonApp.systemTitle.Split('-')[0]+"-"+ConfigManager.instance.Industry.name;
                        }

                        if (!string.IsNullOrEmpty(CommonApp.sysUid))
                        {
                            Log.WriterNormalLog("用户设备ID取得开始......");
                            DeviceInfo deviceInfo = getDeviceInfo(configManager.ServerUrl.DeviceUrl)?? configInfo_ini?.LocalConfig?.device;
                            ConfigManager.instance.deviceCode = deviceInfo.deviceCode;
                            ConfigManager.instance.deviceId = deviceInfo.deviceId;
                            ConfigManager.instance.allowKitchenPrint = deviceInfo.info2.allowKitchenPrint;

                            Log.WriterNormalLog("用户激活码信息取得开始......");
                            ActivationCode activationCode = getActivationCode(configManager.ServerUrl.AgentUrl) ?? configInfo_ini?.LocalConfig.activationCode;
                            ConfigManager.instance.activationCode = activationCode;
                            if (configData_remote != null)
                            {
                                //configData_remote.activationCode = deviceInfo.activationCode;
                                //configData_remote.deviceCode = deviceInfo.deviceCode;
                                //configData_remote.deviceId = deviceInfo.deviceId;

                                if (CommonApp.step == CommonApp.Setp.Step_1 && !string.IsNullOrEmpty(configFileUrl))
                                {
                                    Log.WriterNormalLog("远程用户配置信息保存到本地......");
                                    FileManager.WriteIniData(configFileUrl, new ConfigInfo(configData_remote, deviceInfo, activationCode).data);
                                }


                            }


                        }

                    }
                    else
                    {
                        Log.WriterExceptionLog("远程服务器调用失败，配置文件无法加载。首次登录必须联网。");
                    }
                }
                catch (Exception ex)
                {
                    Log.WriterExceptionLog("ConfigManager:" + ex.Message);
                    //throw ex;
                }
                return ConfigManager.instance;
            }
        }

        public static void LoadConfig()
        {
            CommonApp.Config = Instance;
            
            if (!string.IsNullOrEmpty(CommonApp.sysUid))
            {
                Log.WriterNormalLog("用户本地数据库配置开始......");
                CommonApp.CreateStore();
                CommonApp.UpdateDataBase("");
                CommonApp.InitData();
                Log.WriterNormalLog("用户本地数据实例化结束：");
            }
        }

        private static ConfigData getConfig()
        {
            ConfigData result = null;
            try
            {
                RequestBase request = new RequestBase(CommonApp.DefaultConfigURL, "/config/getConfigInfo");

                NetworkCenter.Instance.SendRequest(request, delegate (ResponseBase<ConfigData> reply)
                {
                    try
                    {
                        if (reply.Code == 200)
                        {
                            result = reply.data;
                        }
                        Log.WriterNormalLog("远程用户配置信息取得成功。");
                    }
                    catch (Exception ex)
                    {
                        Log.WriterExceptionLog("getConfig:" + ex.Message);
                    }
                });
            }
            catch (Exception ex)
            {
                Log.WriterNormalLog("服务器配置信息取得失败。" + ex.Message);
            }

            return result;
        }
        private static DeviceInfo getDeviceInfo(string DeviceURL)
        {
            DeviceInfo deviceInfo = null;
            try
            {
                RequestDeviceInfo request = new RequestDeviceInfo(DeviceURL);
                Log.WriterNormalLog(DeviceURL);
                NetworkCenter.Instance.SendRequest(request, delegate (ResponseBase<DeviceInfo> reply)
                {
                    try
                    {
                        if (reply.Code == 200)
                        {
                            deviceInfo = reply.data;
                            Log.WriterNormalLog(JsonConvert.SerializeObject(deviceInfo));
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.WriterExceptionLog(ex.Message);
                    }
                });
            }
            catch (Exception ex) {
                Log.WriterExceptionLog("getDeviceInfo:" + ex.Message);
            }
            
            return deviceInfo;
        }
        private static ActivationCode getActivationCode(string BaseURL)
        {
            ActivationCode activationCode = null;
            try
            {
                RequestJsonStr request = new RequestJsonStr(BaseURL + "/activationcodes/getHighestVersion", "", "{\"phone\": \"" + CommonApp.sysUid + "\"}");
                NetworkCenter.Instance.SendRequest(request, delegate (ResponseBase<ActivationCode> reply)
                {
                    try
                    {
                        if (reply.Code == 200)
                        {
                            activationCode = reply.data;
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.WriterExceptionLog(ex.Message);
                    }
                });
            }
            catch (Exception ex) {
                Log.WriterExceptionLog(ex.Message);
            }

            return activationCode;
        }

        public ConfigManager()
        {
        }
        public ConfigBase Base { get; set; } = ConfigBase.Instance;
        public ConfigServerUrl ServerUrl { get; set; } = new ConfigServerUrl();
        public int deviceCode { get; set; }
        public string deviceId { get; set; }
        public bool allowKitchenPrint { get; set; }
        public ActivationCode activationCode { get; internal set; }
        public int Mqtt { get; set; } = -1;
        public MqttOptions mqttOptions { get; set; }
        public IndustryClass Industry { get; set; }
        public SyncOptions syncOptions { get; set; } = new SyncOptions();
        public string systemName { get; private set; }
        public string subCode { get; private set; }
        public string token { get { return CommonApp.Authorization; } }
        public string subName { get; internal set; }
        public bool IndustryUpdated { get; internal set; }

        //public string UpdateUrl { get; private set; }

        internal static void Clear()
        {
            instance = null;
        }
    }

    public class CurrentUserConfig
    {
        /// <summary>
        /// 
        /// </summary>
        public CurrentUserConfig()
        {
            
        }
        /// <summary>
        /// 
        /// </summary>
        public CurrentUserConfig(UserInfo currentUser)
        {
            this.data = AESHelper.StringEncoding(JsonConvert.SerializeObject(currentUser));
        }
        public string data { get; set; }
        private string _StringDecoding = null;
        public string StringDecoding
        {
            get
            {
                if (_StringDecoding == null)
                {
                    _StringDecoding = AESHelper.StringDecoding(data);
                }
                return _StringDecoding;
            }
        }
        private UserInfo _currentUser = null;
        public UserInfo CurrentUser
        {
            get
            {
                if (_currentUser == null&&!string.IsNullOrEmpty(data))
                {
                    _currentUser = JsonConvert.DeserializeObject<UserInfo>(StringDecoding);
                }
                return _currentUser;
            }
        }

    }
    public class ConfigBaseInfo
    {
        public string data { get; set; }
    }
    public class ConfigInfo : ConfigBaseInfo
    {
        private string _StringDecoding = null;
        private string StringDecoding
        {
            get
            {
                if (_StringDecoding == null)
                {
                    _StringDecoding = AESHelper.StringDecoding(data);
                }
                return _StringDecoding;
            }
        }
        public ConfigInfo()
        {
        }
        public ConfigInfo(ConfigData _configData, DeviceInfo _deviceInfo, ActivationCode activationCode)
        {
            if (_configData == null) return;

            this.data = AESHelper.StringEncoding(JsonConvert.SerializeObject(new Config(_configData, _deviceInfo, activationCode)));

        }
        private Config _config = null;
        public Config LocalConfig
        {
            get
            {
                if (_config == null)
                {
                    _config = JsonConvert.DeserializeObject<Config>(StringDecoding);
                }
                return _config;
            }
        }
        //private ConfigData _configData = null;
        //public ConfigData configData
        //{
        //    get
        //    {
        //        if (_config == null) {
        //            _config = JsonConvert.DeserializeObject<Config>(StringDecoding);
        //        }
        //        if (_configData == null)
        //        {
        //            _configData = _config.config;

        //        }
        //        return _configData;
        //    }
        //}
        //private DeviceInfo _deviceData = null;
        //public DeviceInfo deviceData
        //{
        //    get
        //    {
        //        if (_config == null)
        //        {
        //            _config = JsonConvert.DeserializeObject<Config>(StringDecoding);
        //        }
        //        if (_deviceData == null)
        //        {
        //            _deviceData = _config.device;

        //        }
        //        if (_deviceData?.deviceCode == 0) _deviceData = null;
        //        return _deviceData;
        //    }
        //}
        public class Config
        {
            public ConfigData config { get; set; }
            public DeviceInfo device { get; set; }
            public ActivationCode activationCode { get; set; }
            public Config() { }
            public Config(ConfigData configData1, DeviceInfo deviceInfo, ActivationCode activationCode)
            {
                this.config = configData1;
                if (!string.IsNullOrEmpty(deviceInfo.deviceId)) this.device = deviceInfo;
                this.activationCode = activationCode;
            }
        }
    }

    public class ConfigData
    {

        public string configUrl { get; set; }
        public string configInfo { get; set; }
        public string userConfigInfo { get; set; }
        public int id { get; set; }
        public string systemName { get; set; } = "zgzn";
        public string subCode { get; set; }
        public string subName { get; set; }

        internal string getConfigInfo()
        {
            string config = configInfo;
            if (!string.IsNullOrEmpty(userConfigInfo))
            {
                JObject _config1 = JObject.Parse(configInfo);
                JObject _config2 = JObject.Parse(userConfigInfo);
                _config1.Merge(_config2);
                config = JsonConvert.SerializeObject(_config1);
            }
            return config;
        }

    }
    public class DeviceInfo
    {

        //public ActivationCode activationCode { get; set; }
        public string deviceId { get; set; }
        public int deviceCode { get; set; }
        public Info2 info2 { get; set; } = new Info2();

        public class Info2 {
            public bool allowKitchenPrint { get; set; } = false;
        }

    }
    public class ActivationCode
    {
        public string endDate { get; set; }
        public int period { get; set; }
        public int remainDay { get; set; }
        public bool? ultimate { get; set; }

    }
    public class ConfigServerUrl
    {
        public ConfigServerUrl()
        {

        }
        public string UPDATEURL { get; set; }// = @"https://dev.zhangguizhinang.com/POS/";
        public string AUTOUPDATEURL { get; set; }// = @"https://dev.pos.zhangguizhinang.com/POSAPI/upload/getUpdFlgByUid";
        public string AdvertBaseUrl { get; set; }// = "http://*************:7004/zgzn-advert";
        public string HttpUrl { get; set; }// = @"https://dev.zhangguizhinang.com";
        public string POSURL { get; set; }// = @"https://dev.pos.zhangguizhinang.com/POSAPI";
        public string VIPURL { get; set; }// = @"https://dev.vip.zhangguizhinang.com/VIPAPI";
        public string PUSHURL { get; set; }// = @"https://dev.push.zhangguizhinang.com/push";
        public string ONLINEURL { get; set; }// = @"https://dev.bmp.zhangguizhinang.com/";
        public string WEBSOCKETURL { get; set; }// = @"https://dev.mp.zhangguizhinang.com/";
        public string PAYURL { get; set; }// = @"https://dev.pay.zhangguizhinang.com/spring-boot-pay";
        public string BILLURL { get; set; }
        public string TopicUrl { get; set; }// = @"dev.zhangguizhinang.com";
        public string USERURL { get; set; }// = @"http://*************:7001/zgzn-users";
        public string SETTINGURL { get; set; }
        public string LogonUrl { get; set; }// = @"/user/login";
        public string PRODUCTURL { get; set; }
        public string AgentUrl { get; set; }
        public string DeviceUrl { get; set; }// = "http://*************:7001/zgzn-users/device/getConfigInfo";
        public string NETWORKCHECKURL { get; set; }
        public string userModuleUrl { get; set; }
        public string settingModuleUrl { get; set; }
        public string productModuleUrl { get; set; }
        public string posModuleUrl { get; set; }
        public string advertModuleUrl { get; set; }
        public string vipModuleUrl { get; set; }
        public string nginxModuleUrl { get; set; }

        public string weixinModuleUrl { get; set; }

        public string weixinUrl { get; set; }
        public object ACTIONLOGURL { get; set; }
    }
    public class ConfigBase
    {
        private static ConfigBase instance;

        public ConfigBase()
        {

        }
        public static ConfigBase Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new ConfigBase();
                }
                return instance;
            }
        }
        public void Clear() {
            ConfigBase.instance = new ConfigBase();
        }
        //public string DeviceId { get { return FingerPrint.Value(); } }
        //public int? DeviceCode { get; set; }

        public string AdvertLocalUrl { get { return Scheme + @"://" + DomainName + @"/advert/cache/"; } }
        public string Scheme { get; set; } = "https";
        public string DomainName { get; set; } = "www.zgpos.com";
        public string LogoDir { get; set; } = Path.Combine("local", "logo");
        public string MainPageUrl { get { return Scheme + @"://" + DomainName; } }

        private string _otherOptions;
        
        public object OtherOptions { get { return _otherOptions==null? null:JsonConvert.DeserializeObject(_otherOptions); } 
            
            set {
                _otherOptions = value.GetType().Name.Equals("String")?value.ToString():JsonConvert.SerializeObject(value);
            } }
    }

}
