﻿using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using zgLogging;

namespace zgUtils.Controls
{
    public class NetworkConnection
    {
        //通过IsNetworkAlive方法，来获取电脑的联网状态
        [DllImport("sensapi.dll", SetLastError = true)]
        private static extern bool IsNetworkAlive(out int connectionDescription);

        //通过InternetGetConnectedState方法，来获取电脑的联网状态
        [DllImport("winInet.dll")]
        private static extern bool InternetGetConnectedState(ref IntPtr dwFlag, int dwReserved);

        /// <summary>
        /// IsNetworkAlive函数输出值1-连接局域网
        /// </summary>
        private const int LanNetworkConnectedFlag = 1;
        /// <summary>
        /// 网络是否连接
        /// </summary>
        public static bool IsConnected
        {
            get
            {
                var isNetworkConnected = IsNetworkAlive(out int flags);
                try
                {
                    int errCode = Marshal.GetLastWin32Error();
                    if (errCode != 0)
                    {
                        Log.WriterNormalLog($"通过{nameof(IsNetworkAlive)}非托管DLL函数，获取网络状态时，遇到异常！");
                    }

                    //IsNetworkAlive检测到是局域网连上网络，则使用InternetGetConnectedState重新确认是否有网
                    if (isNetworkConnected && flags == LanNetworkConnectedFlag)
                    {
                        var dwFlag = new IntPtr();
                        isNetworkConnected = InternetGetConnectedState(ref dwFlag, 0);
                        errCode = Marshal.GetLastWin32Error();
                        if (errCode != 0)
                        {
                            Log.WriterNormalLog($"通过{nameof(InternetGetConnectedState)}非托管DLL函数，获取网络状态时，遇到异常！");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.WriterExceptionLog(ex.Message);
                }
                return isNetworkConnected;
            }
        }

    }
}
