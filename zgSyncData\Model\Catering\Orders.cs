﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace zgSyncData.Model.Catering
{
    [Serializable]
    public class Orders
    {
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "accountId")]
        public int AccountId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "backAccountId")]
        public int BackAccountId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "backAt")]
        public string BackAt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "backBy")]
        public string BackBy { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "backMoney")]
        public decimal BackMoney { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "backRemark")]
        public string BackRemark { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "blendPays")]
        public string BlendPays { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "changeMoney")]
        public decimal ChangeMoney { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "closeAt")]
        public string CloseAt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "completeAt")]
        public string CompleteAt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "createAt")]
        public string CreateAt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "createBy")]
        public string CreateBy { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "customerId")]
        public int CustomerId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "deposit")]
        public decimal Deposit { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "depositType")]
        public int DepositType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "dinerNum")]
        public int DinerNum { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "discount")]
        public string Discount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "discountMoney")]
        public decimal DiscountMoney { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "fingerprint")]
        public string Fingerprint { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "id")]
        public int Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "info1")]
        public string Info1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "info2")]
        public string Info2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "isDel")]
        public int IsDel { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "orderNo")]
        public string OrderNo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "payMoney")]
        public decimal PayMoney { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "payOrderNo")]
        public string PayOrderNo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "paySerialNo")]
        public string PaySerialNo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "remark")]
        public string Remark { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "reviseAt")]
        public string ReviseAt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "reviseBy")]
        public string ReviseBy { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "shouldMoney")]
        public decimal ShouldMoney { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "status")]
        public int Status { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "syncAt")]
        public string SyncAt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "tableAlias")]
        public string TableAlias { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "tableFingerprint")]
        public string TableFingerprint { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "totalDiscountMoney")]
        public decimal TotalDiscountMoney { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "totalMoney")]
        public decimal TotalMoney { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "transactionNo")]
        public string TransactionNo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "vipInfo")]
        public string VipInfo { get; set; }

        [JsonProperty(propertyName: "subOrders")]
        public List<SubOrder> SubOrders { get; set; }


        /*
         {
			"accountId":1,
			"backAccountId":0,
			"backAt":"",
			"backBy":"",
			"backMoney":0,
			"backRemark":"",
			"blendPays":"",
			"changeMoney":0.00,
			"closeAt":"",
			"completeAt":"",
			"createAt":"2022-06-01 15:43:28.74",
			"createBy":"1",
			"customerId":0,
			"deposit":0,
			"depositType":0,
			"dinerNum":1,
			"discount":"[{\"type\":\"reduce\",\"disc\":\"\",\"name\":\"减价\"}]",
			"discountMoney":0.00,
			"fingerprint":"42b7a8af-befa-4ae4-8d64-4d69771eb129",
			"id":1,
			"info1":"",
			"info2":"",
			"isDel":0,
			"orderNo":"***********.1",
			"payMoney":20.00,
			"payOrderNo":"",
			"paySerialNo":"",
			"remark":"",
			"reviseAt":"2022-06-11 12:35:22.621",
			"reviseBy":"1",
			"shouldMoney":20.00,
			"status":3,
			"subOrders":[
				{
					"createAt":"2022-06-01 15:43:28.741",
					"createBy":"1",
					"customerId":0,
					"discount":"",
					"discountMoney":0.00,
					"fingerprint":"2cb021d8-1e9a-474b-b052-f4e3b3f16f6d",
					"id":1,
					"info1":"",
					"info2":"",
					"isDel":0,
					"orderFingerprint":"42b7a8af-befa-4ae4-8d64-4d69771eb129",
					"orderItems":[
						{
							"allDiscountMoney":0.00,
							"allMoney":20.00,
							"chargeUnit":"",
							"createAt":"2022-06-01 15:43:28.741",
							"createBy":"1",
							"discount":"",
							"discountMoney":0.00,
							"fingerprint":"42876445-7d86-4bd9-93d8-c43e95a3b821",
							"goodsFingerprint":"c7880485391c6472529231bbe3b0dba0",
							"goodsName":"asdf",
							"id":1,
							"info1":"",
							"info2":"",
							"isDel":0,
							"isGift":0,
							"isStockManagement":0,
							"orderFeeds":[],
							"price":20.00,
							"quantity":1.0,
							"remark":"",
							"reviseAt":"2022-06-01 15:43:28.741",
							"reviseBy":"1",
							"salePrice":20.00,
							"specs":"",
							"subOrderFingerprint":"2cb021d8-1e9a-474b-b052-f4e3b3f16f6d",
							"syncAt":"2022-06-01 15:43:28.807305",
							"sysSid":1,
							"sysUid":"18053510016",
							"taste":"[]",
							"totalMoney":20.00,
							"vipPrice":0.00
						}
					],
					"placeOrderType":1,
					"remark":"",
					"reviseAt":"2022-06-01 15:43:28.741",
					"reviseBy":"1",
					"serialNo":"A1",
					"status":2,
					"subOrderNo":"1654069408719",
					"subOrderType":1,
					"syncAt":"2022-06-01 15:43:28.807305",
					"sysSid":1,
					"sysUid":"18053510016",
					"totalMoney":20.00
				}
			],
			"syncAt":"2022-06-11 12:35:22.942012",
			"sysSid":1,
			"sysUid":"18053510016",
			"tableAlias":"1",
			"tableFingerprint":"67c3af2a-c89f-49f7-a2a7-e2efddc3c949",
			"totalDiscountMoney":0.00,
			"totalMoney":20.00,
			"transactionNo":"",
			"vipInfo":""
		}
         
         
         */
    }
}
