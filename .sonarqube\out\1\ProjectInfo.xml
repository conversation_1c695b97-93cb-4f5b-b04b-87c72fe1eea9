<?xml version="1.0" encoding="utf-8"?>
<ProjectInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.sonarsource.com/msbuild/integration/2015/1">
  <ProjectName>TreChina.Printer</ProjectName>
  <ProjectLanguage>C#</ProjectLanguage>
  <ProjectType>Product</ProjectType>
  <ProjectGuid>b04c2d59-4e43-4cfd-b2df-f73b51472449</ProjectGuid>
  <FullPath>C:\Users\<USER>\Documents\POSVueChrome\TreChina.Printer\TreChina.Printer.csproj</FullPath>
  <IsExcluded>false</IsExcluded>
  <AnalysisResults>
    <AnalysisResult Id="FilesToAnalyze" Location="C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\out\1\FilesToAnalyze.txt" />
  </AnalysisResults>
  <AnalysisSettings>
    <Property Name="sonar.cs.roslyn.reportFilePath">C:\Users\<USER>\Documents\POSVueChrome\TreChina.Printer\bin\Debug\net452\TreChina.Printer.dll.RoslynCA.json|C:\Users\<USER>\Documents\POSVueChrome\TreChina.Printer\bin\Debug\net452\TreChina.Printer.dll.RoslynCA.json</Property>
    <Property Name="sonar.cs.analyzer.projectOutPath">C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\out\1</Property>
  </AnalysisSettings>
  <Configuration>Debug</Configuration>
  <Platform>AnyCPU</Platform>
  <TargetFramework>net452</TargetFramework>
</ProjectInfo>