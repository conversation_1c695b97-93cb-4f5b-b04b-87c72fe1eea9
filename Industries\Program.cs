﻿#undef ISPRODCT

using System;
using System.Windows.Forms;
using zgpos;
using zgpos.EventHandlers;
using zgpos.ProgramTemplate;
using Resources = Industries.Properties.Resources;

namespace Clothing
{
    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            MainClass mainClass = new MainClass();
            mainClass.FilePath=Environment.CurrentDirectory;
            Application.Run(mainClass.getMainForm());
        } 
        public class MainClass : ProgramTemplateClass
        {
            //Authorization 服装版默认token 其他业态需重新生成
            public override string Authorization { get; protected set; }
            public override string Name { get; protected set; } = zgzn.ConfigController.GetConfig("SystemTitle");
            public override string Description { get; protected set; } = "共通画面 - wwj";

            public MainClass()
            {

                ProgramFinished += MainClass_ProgramFinished;
                zgCommon.Properties.Resources.ErrorPage = Resources.ErrorPage;
                zgCommon.Properties.Resources.logo = Resources.logo;
                zgCommon.Properties.Resources.LogoImage = Resources.LogoImage;
                zgCommon.Properties.Resources.qrcode = Resources.qrcode;
                zgCommon.Properties.Resources.top = Resources.top;
                zgCommon.Properties.Resources.Icon = Resources.ico;
                zgCommon.Properties.Resources.DefaultProgramIcon = Resources.DefaultProgramIcon;
                zgCommon.Properties.Resources.back_bg = Resources.back_bg;
                zgCommon.Properties.Resources.Advert = Resources.Advert;

                App.Init(this, System.Reflection.Assembly.GetExecutingAssembly());
#if DEBUG
                App.MainPageUrl = "http://localhost:3000";
#endif
            }

            private void MainClass_ProgramFinished(object sender, EventArgs e)
            {
                MessageBox.Show("ProgramFinished");
            }

            public override string FileName => System.Reflection.Assembly.GetExecutingAssembly().ManifestModule.ScopeName;


            protected override Form CreateProgramForm()
            {
                return getMainForm();
            }

            public Form getMainForm()
            {

                try
                {
                    Application.ApplicationExit += ApplicationExitHandler.DoApplicationExit;
                    Application.ThreadException += ApplicationThreadExceptionHandler.DoApplicationThreadException;
                    AppDomain.CurrentDomain.UnhandledException += AppDomainExceptionHandler.DoAppDomainException;


                    return new FormMain();
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                    //Log.WriterNormalLog(ex.Message);
                    throw ex;
                }
                //return new Form();
            }

        }


    }


}
