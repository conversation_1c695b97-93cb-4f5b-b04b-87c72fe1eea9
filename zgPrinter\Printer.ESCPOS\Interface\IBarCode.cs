﻿using zgPrinter.Model;

namespace zgPrinter.Printer.ESCPOS.Interface
{
    /// <summary>
    /// 处理条码
    /// </summary>
    interface IBarCode
    {
        /// <summary>
        /// Code128条码
        /// </summary>
        /// <param name="code"></param>
        /// <param name="printString"></param>
        /// <returns></returns>
        byte[] Code128(string code,EnumPositions printString);

        /// <summary>
        /// Code39条码
        /// </summary>
        /// <param name="code"></param>
        /// <param name="printString"></param>
        /// <returns></returns>
        byte[] Code39(string code, EnumPositions printString);

        /// <summary>
        /// Ean13条码
        /// </summary>
        /// <param name="code"></param>
        /// <param name="printString"></param>
        /// <returns></returns>
        byte[] Ean13(string code, EnumPositions printString);
    }
}

