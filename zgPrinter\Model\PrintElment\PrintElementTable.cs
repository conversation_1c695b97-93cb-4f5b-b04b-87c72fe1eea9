﻿using System.Data;
using System.Drawing;

namespace zgPrinter.Model
{
    /// <summary>
    /// 打印元素-表格
    /// </summary>
    public class PrintElementTable : PrintElement
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="index">序号</param>
        /// <param name="table">打印内容</param>
        /// <param name="printHead">是否需要打印表头，打印表头时，表头和表体间会有分割线</param>
        /// <param name="headFont">表头字体</param>
        /// <param name="headTextAlign">表头文字浮动</param>
        /// <param name="bodyFont">表体字体</param>
        /// <param name="bodyTextAlign">表体文本浮动</param>
        public PrintElementTable(int index, DataTable table, bool printHead = true, Font headFont = null, EnumTextAlign headTextAlign = EnumTextAlign.Center, Font bodyFont = null, EnumTextAlign bodyTextAlign = EnumTextAlign.Left)
        {
            this.Index = index;
            this.Content = table;
            this.ContentType = EnumContentType.Table;
            this.HeadTextAlign = headTextAlign;
            this.HeadFont = headFont;
            this.ContentFont = bodyFont;
            this.TextAlign = bodyTextAlign;
        }

        /// <summary>
        /// 数据结构
        /// </summary>
        public DataTable DataStruct { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        public new DataTable Content { get; set; }

        /// <summary>
        /// 是否打印表头,默认打印
        /// </summary>
        public bool PrintHead { get; set; } = true;

        /// <summary>
        /// 是否打分割线
        /// </summary>
        public bool PrintSeparatorLine { get; set; } = true;

        /// <summary>
        /// 表头文本浮动
        /// </summary>
        public EnumTextAlign HeadTextAlign { get; set; }

        /// <summary>
        /// 表头字体
        /// </summary>
        public Font HeadFont { get; set; }

        public EnumTextAlign BodyTextAlign { get; set; }

        public Font BodyFont { get; set; }

    }
}
