﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace zgSQLite.Properties {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("zgSQLite.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找类似 .header off
        ///.mode csv 
        ///.once .\\.\\sync-csv\\order_feeds.csv
        ///select order_item_fingerprint, goods_fingerprint, goods_name, charge_unit, quantity, price, total_money,
        ///	discount, discount_money, specs, is_stock_management, is_del, fingerprint, remark,
        ///	info1, info2, create_by, revise_by, create_at, revise_at, sync_at
        ///from order_feeds
        ///where id in (select id from sync_order_feeds); 的本地化字符串。
        /// </summary>
        internal static string order_feeds {
            get {
                return ResourceManager.GetString("order_feeds", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 .header off
        ///.mode csv 
        ///.once .\\.\\sync-csv\\order_items.csv
        ///select sub_order_fingerprint, goods_fingerprint, goods_name, charge_unit, all_money, all_discount_money,
        ///	quantity, price, sale_price, vip_price, total_money, discount, discount_money, taste, is_gift, specs,
        ///	is_stock_management, is_del, fingerprint, remark, info1, info2, create_by, revise_by, create_at, revise_at, sync_at
        ///from order_items
        ///where id in (select id from sync_order_items); 的本地化字符串。
        /// </summary>
        internal static string order_items {
            get {
                return ResourceManager.GetString("order_items", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 .header off
        ///.mode csv 
        ///.once .\\.\\sync-csv\\orders.csv
        ///select order_no, customer_id, diner_num, table_fingerprint, table_alias, total_money, total_discount_money,
        ///discount, discount_money, deposit_type, deposit, should_money, pay_money, change_money, back_money, status,
        ///account_id, back_account_id, blend_pays, transaction_no, complete_at, close_at, pay_order_no, pay_serial_no,
        ///vip_info, is_del, fingerprint, remark, back_remark, info1, info2,
        ///create_by, revise_by, back_by, create_at, revise_at, back_ [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string orders {
            get {
                return ResourceManager.GetString("orders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 .header off
        ///.mode csv 
        ///.once .\\.\\sync-csv\\sale_blend_pays.csv
        ///select acct_id, pay_amt, remark, sale_fingerprint, fingerprint
        ///from sale_blend_pays
        ///where id in (select id from sync_sale_blend_pays); 的本地化字符串。
        /// </summary>
        internal static string sale_blend_pays {
            get {
                return ResourceManager.GetString("sale_blend_pays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 .header off
        ///.mode csv 
        ///.once .\\.\\sync-csv\\sub_orders.csv
        ///select sub_order_no, sub_order_type, customer_id, place_order_type, total_money, discount, discount_money,
        ///	status, serial_no, order_fingerprint, is_del, fingerprint, remark,
        ///	info1, info2, create_by, revise_by, create_at, revise_at, sync_at
        ///from sub_orders
        ///where id in (select id from sync_sub_orders); 的本地化字符串。
        /// </summary>
        internal static string sub_orders {
            get {
                return ResourceManager.GetString("sub_orders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 .separator &apos;,&apos;
        ///.import .\\.\\sync-csv\\tmp_order_feeds.csv tmp_order_feeds 的本地化字符串。
        /// </summary>
        internal static string tmp_order_feeds {
            get {
                return ResourceManager.GetString("tmp_order_feeds", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 .separator &apos;,&apos;
        ///.import .\\.\\sync-csv\\tmp_order_items.csv tmp_order_items 的本地化字符串。
        /// </summary>
        internal static string tmp_order_items {
            get {
                return ResourceManager.GetString("tmp_order_items", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 .separator &apos;,&apos;
        ///.import .\\.\\sync-csv\\tmp_orders.csv tmp_orders 的本地化字符串。
        /// </summary>
        internal static string tmp_orders {
            get {
                return ResourceManager.GetString("tmp_orders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 .separator &apos;,&apos;
        ///.import .\\.\\sync-csv\\tmp_sale_blend_pays.csv tmp_sale_blend_pays 的本地化字符串。
        /// </summary>
        internal static string tmp_sale_blend_pays {
            get {
                return ResourceManager.GetString("tmp_sale_blend_pays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 .separator &apos;,&apos;
        ///.import .\\.\\sync-csv\\tmp_sub_orders.csv tmp_sub_orders 的本地化字符串。
        /// </summary>
        internal static string tmp_sub_orders {
            get {
                return ResourceManager.GetString("tmp_sub_orders", resourceCulture);
            }
        }
    }
}
