﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgSyncData.Model
{
    public class SaleItem
    {
        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "amt")]
        public decimal Amt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "disc")]
        public int Disc { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "id")]
        public int Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "info1")]
        public string Info1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "info2")]
        public string Info2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "isDel")]
        public int IsDel { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "isSync")]
        public int IsSync { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "itemDisc")]
        public int ItemDisc { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "mprc")]
        public int Mprc { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "mQty")]
        public int MQty { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "oprc")]
        public int Oprc { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "prc")]
        public int Prc { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "productId")]
        public int ProductId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "productSyncG")]
        public string ProductSyncG { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "Qty")]
        public int Qty { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "remark")]
        public string Remark { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "saleId")]
        public int SaleId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "saleSyncG")]
        public string SaleSyncG { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "sorderSyncG")]
        public string SorderSyncG { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "syncAt")]
        public string SyncAt { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "syncG")]
        public string SyncG { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "syncN")]
        public int SyncN { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "syncV")]
        public string SyncV { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "sysSid")]
        public int SysSid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "sysUid")]
        public string SysUid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [JsonProperty(propertyName: "utye")]
        public int Utye { get; set; }

        public string ToInsertSql()
        {
            var sql = "insert into dataCopy.tmp_saleitems(good_fingerprint, oprice, disc, price, qty, amt, remark, is_deleted, fingerprint, sale_fingerprint, item_disc, mprice) values ";
            sql += $"('{ProductSyncG}', {Oprc}, {Disc}, {Prc}, round({Qty}, 3), {Amt}, '{Remark}', {IsDel}, '{SyncG}', '{SaleSyncG}', {ItemDisc}, {Mprc} );";
            return sql;
        }

        public static string SyncSaleitemsInsert
        {
            get
            {
                var sql = "insert into sale_items(sale_id, good_id, oprice, disc, price, qty, amt, remark, is_deleted, is_synced, fingerprint, sale_fingerprint, item_disc, mprice)"
                         + " select sales.id, goods.id, tmp.oprice, tmp.disc, tmp.price, tmp.qty, tmp.amt, tmp.remark, tmp.is_deleted, 1, tmp.fingerprint, tmp.sale_fingerprint, tmp.item_disc, tmp.mprice"
                         + " from dataCopy.tmp_saleitems tmp"
                         + " left join sale_items"
                         + " on tmp.fingerprint = sale_items.fingerprint"
                         + " inner join sales"
                         + " on tmp.sale_fingerprint = sales.fingerprint"
                         + " inner join goods"
                         + " on tmp.good_fingerprint = goods.fingerprint"
                         + " where sale_items.fingerprint is null; ";
                return sql;
            }
        }

        public static string SyncSaleitemsUpdate
        {
            get
            {
                var sql = " update sale_items set"
                      + " is_deleted = (select is_deleted from dataCopy.tmp_saleitems where fingerprint = sale_items.fingerprint)"
                      + " where is_synced = 1"
                      + " and exists(select 1 from dataCopy.tmp_saleitems where fingerprint = sale_items.fingerprint); ";

                return sql;
            }
        }
    }


}
