﻿<!DOCTYPE html>
<html style="overflow: hidden;margin: 0;padding: 0;">
<head>
    <title>back</title>
    <meta charset="utf-8" />
    <style type="text/css">
        .slide-show p {
            width: 100%;
            position: absolute;
            color: #fff;
            background-color: #000;
            opacity: 0.7;
            filter: alpha(opacity=70);
            bottom: 0px;
            padding: 0.5vmin 0px;
            margin: 0 auto;
            text-align: center;
        }

        .slide-img,.slide-img img {
            width: 100%;
            height: 100%;
            top: 0;
        }

        .ad2_container {
            width: 100%;
            height: 100%;
        }

        .ellipse {
            width: 80%;
            height: 80%;
            background: white;
            border-radius: 50%;
            opacity: 0.7;
            filter: alpha(opacity=70);
            position: absolute;
            bottom: 10%;
            left: 10%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: black;
            font-weight: 500;
            font-size: 180%;
            overflow: hidden;
        }

            .ellipse div {
                width: 80%;
                height: 50%;
                white-space: pre-wrap;
                display: flex;
                justify-content: center;
                align-items: center;
            }

        .box,.box img {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            width: 100%;
            margin: auto;
            z-index: -1;
            *zoom: 1;
        }

            .box:before {
                content: "";
                display: inline-block;
                padding-bottom: 100%;
                width: 0.1px;
                vertical-align: middle;
            }
    </style>
</head>
<body>
 <div class="box">$myHtml</div>
</body>
</html>