﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using zgAdvert.Model;
using zgAdvert.Network;
using zgLogging;
using zgUtils.Controls;
using zgUtils.Files;
using zgUtils.Model;

namespace zgAdvert
{
    public class AdvertFactory
    {
        //public static readonly string FileDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "advert");
        //public static readonly string CacheDir = Path.Combine(FileDir, "cache");
        
        public static bool isShowAd { get;  set; } = false;
        public static List<ADItem> ADItems { get; set; } = new List<ADItem>();
        public static List<News> ADItems_1 { 
            get {
                List<News> list = new List<News>();
                if (ADItems != null && ADItems.Count > 0)
                {
                    foreach (var item in ADItems.Where(item => item.location == 1).ToList())
                    {
                        News news = new News
                        {
                            type = item.templateId,
                            message = item.comments
                        };
                        list.Add(news);
                    }
                }

                return list;
            }
        }
        public static List<Poster> ADItems_2
        {
            get
            {
                List<Poster> list = new List<Poster>();
                if (ADItems != null && ADItems.Count > 0)
                {
                    foreach (var item in ADItems.Where(item => item.location == 2).ToList()) {
                        Poster poster = new Poster();
                        poster.id = item.id;
                        poster.remoteImg = item.img;
                        string filename = poster.remoteImg.Remove(0, poster.remoteImg.LastIndexOf("/") + 1);
                        poster.img = Path.Combine(AdvertLocalUrl,item.location.ToString(), filename);
                        poster.url = item.comments;
                        poster.changeTime = item.duration;
                        poster.orderIndex = item.index;
                        poster.is_del = item.isDel;
                        list.Add(poster);
                    }
                }
                
                return list;
            }
        }
        //副屏广告列表
        public static List<ADItem> ADItems_3
        {
            get
            {
                List<ADItem> list = new List<ADItem>();
                if (ADItems != null && ADItems.Count > 0)
                {
                    list = ADItems.Where(item => item.location == 3).ToList();
                }
                return list;
            }
        }
        private static AdvertFactory _instance;
        public delegate void RecevieAdsDelegate(List<ADItem> ads);
        public delegate void ConnectedDelegate();
        public event RecevieAdsDelegate Recevie;

        public event ConnectedDelegate Connected;

        public static AdvertFactory Instance
        {
            get
            {
                if (_instance == null)
                {
                    Log.WriterNormalLog("初始化物联网");
                    _instance = new AdvertFactory();
                }
                return _instance;
            }
        }

        public string Authorization { get; private set; }
        public static string AdvertBaseUrl { get; internal set; }
        public static string FileDir { get; private set; }
        public static string CacheDir { get; private set; }
        public static string DeviceId { get; set; }
        public static string AdvertLocalUrl { get; set; }
        public static Bitmap back_bg { get; set; }
        public static string htmlstr { get; set; } = "";

        /// <summary>
        /// 
        /// </summary>

        private void Init()
        {
            Log.WriterNormalLog(string.Format("SDK初始化方法被重新调用"));
            ConnectedDelegate connected = Connected;
            if (connected != null)
            {
                connected();
            }
            if (!Directory.Exists(FileDir))
            {
                Directory.CreateDirectory(FileDir);
            }
            if (!Directory.Exists(CacheDir))
            {
                Directory.CreateDirectory(CacheDir);
            }
            if (ADItems == null || ADItems.Count <= 0)
            {
                this.GetAds(delegate (List<ADItem> ads)
                {
                    RecevieAdsDelegate recevie = Recevie;
                    if (recevie == null)
                    {
                        return;
                    }
                    recevie(ads);
                });
                return;
            }
            Files.FileManager.Instance.DownLoads(ADItems);
        }
        public void Init(string advertBaseUrl, string advertLocalUrl, string directory, string authorization, string deviceId)
        {
            Authorization = authorization;
            DeviceId = deviceId;
            AdvertBaseUrl = advertBaseUrl;
            FileDir = Path.Combine(directory, "advert");
            CacheDir = Path.Combine(directory, "advert", "cache");
            AdvertLocalUrl = advertLocalUrl;
            Log.WriterNormalLog(string.Format("SDK初始化方法被调用"));
            ConnectedDelegate connected = Connected;
            if (connected != null)
            {
                connected();
            }
            if (!Directory.Exists(FileDir))
            {
                Directory.CreateDirectory(FileDir);
            }
            if (!Directory.Exists(CacheDir))
            {
                Directory.CreateDirectory(CacheDir);
            }
            if (ADItems == null || ADItems.Count <= 0)
            {
                GetAds(delegate (List<ADItem> ads)
                {
                    RecevieAdsDelegate recevie = Recevie;
                    if (recevie == null)
                    {
                        return;
                    }
                    recevie(ads);
                });
                return;
            }
            Files.FileManager.Instance.DownLoads(ADItems);
        }
        public void ReloadAll() {
            if (!string.IsNullOrEmpty(AdvertBaseUrl)) {
                ADItems.Clear();
                Init();
            }
            
        }
        /// <summary>
        /// 获取广告
        /// </summary>
        /// <returns></returns>
        private void SetAdvert(List<ADItem> advert)
        {
            if (advert != null && advert.Count > 0)
            {
                foreach (var item in advert)
                {
                    if (item.location != 1)
                    {
                        item.MaterialType = "IMG";

                    }
                    if (item.location == 3)
                    {
                        item.html = GetAdvertPageHtml(item); //"<html><center><h1>New tab</h1></center><center><a href=\"youtube.com\">YouTube</a>, </center><center><a href=\"google.com\">Google</a></center><center><a href=\"github.com\">Github</a></center></html>";
                    }
                    ADItems.Add(item);
                }
            }
            
            //if (advert.CustomerItems != null)
            //{
            //    foreach (var item in advert.CustomerItems)
            //    {
            //        item.is_default = 0;
            //        if (item.location != 1)
            //        {
            //            item.MaterialType = "IMG";

            //        }
            //        if (item.location == 3)
            //        {
            //            item.html = GetAdvertPageHtml(item);// "<html><center><h1>New tab</h1></center><center><a href=\"youtube.com\">YouTube</a>, </center><center><a href=\"google.com\">Google</a></center><center><a href=\"github.com\">Github</a></center></html>";
            //        }
            //        AdvertFactory.ADItems.Add(item);
            //    }
            //}
            
        }
        public string GetAdvertPageHtml(ADItem item)
        {
            try
            {
                string imagename = AdvertLocalUrl +item.location+"/"+ item.img.Remove(0, item.img.LastIndexOf("/") + 1);
                string strHtml = string.Format(item.html.Replace("picture_path", "0").Replace("comments", "1"), imagename, item.comments);

                //string htmlstr = Properties.Resources.Advert;
                var content = htmlstr.Replace("$myHtml", strHtml);
                return content;
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
                return "";
            }


        }
      
        public void GetAds(Action<List<ADItem>> adItemActions)
        {
            RequestBase<PackageBase> getAdsRequest = new RequestBase<PackageBase>(AdvertBaseUrl, "/advertisements/getAllAdvertisement?");
            
            NetworkCenter.Instance.SendRequest(getAdsRequest, delegate (GetAdsResponse reply)
            {
                
                try
                {
                    saveXML(reply);
                    adItemActions(ADItems);
                }
                catch (Exception ex)
                {
                    Log.WriterExceptionLog(ex.Message);
                    adItemActions(null);
                }
            });
        }
        //public void GetCustomerAds()
        //{
        //    RequestBase<PackageBase> getAdsRequest = new RequestBase<PackageBase>(AdvertBaseUrl, "/advertisements/getCustomerAdv?");
        //    NetworkCenter.Instance.SendRequest<RequestBase<PackageBase>, GetAdsResponse>(getAdsRequest, delegate (GetAdsResponse reply)
        //    {

        //        try
        //        {
        //            saveXML(reply);
        //        }
        //        catch (Exception ex)
        //        {
        //            Log.WriterExceptionLog(ex.Message);
        //        }
        //    });
        //}

        private void saveXML(GetAdsResponse reply)
        {
            string xmlpath = Path.Combine(FileDir, "ads.xml");
            if (reply.Code == 200)
            {
                ADItems.Clear();
                SetAdvert(reply.data);

                Files.FileManager.Instance.DownLoads(ADItems);

                XmlManager.SaveFile(ADItems, xmlpath);
            }
            else if(File.Exists(xmlpath))
            {
                ADItems = XmlManager.Load<List<ADItem>>(xmlpath);
                Files.FileManager.Instance.DownLoads(ADItems);
            }
            
        }

        public static List<Poster> getPoster()
        {
            return ADItems_2;
        }
    }
}
