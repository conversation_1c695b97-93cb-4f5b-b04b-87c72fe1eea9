﻿using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using zgPrinter.Model;

namespace zgPrinter.Printer.Usb
{
    public class UsbPrinter
    {
        private static readonly Guid PRINTER_CLASS_ID = new Guid("28d78fad-5a12-11d1-ae5b-0000f803a8c2");

        /// <summary>
        /// 
        /// </summary>
        /// <param name="devicePath"></param>
        /// <param name="printData"></param>
        public void Print(string devicePath, byte[] printData,int printCount)
        {
            var safeFileHandle = SetupAPINativeMethods.CreateFile(devicePath, FileAccess.ReadWrite, FileShare.ReadWrite, 0, FileMode.OpenOrCreate, 0, (IntPtr)null);
            using (var sh = safeFileHandle)
            {
                using (var f = new System.IO.FileStream(sh, FileAccess.Write))
                {
                    for (int i = 0; i < printCount; i++)
                    {
                        f.Write(printData, 0, printData.Length);
                        Thread.Sleep(10);
                    }
                    
                }
            }
        }

        public void Print(string devicePath, IEnumerable<Tuple<int, byte[]>> printData) {
            var safeFileHandle = SetupAPINativeMethods.CreateFile(devicePath, FileAccess.ReadWrite, FileShare.ReadWrite, 0, FileMode.OpenOrCreate, 0, (IntPtr)null);
            using (var sh = safeFileHandle)
            {
                using (var f = new System.IO.FileStream(sh, FileAccess.Write))
                {
                    foreach (var item in printData)
                    {
                        for (int i = 0; i < item.Item1; i++)
                        {
                            f.Write(item.Item2, 0, item.Item2.Length);
                            Thread.Sleep(10);
                        }
                    }
                }
            }
        }

        public List<KeyValueItem> GetUsbPrintList()
        {
            List<KeyValueItem> kvItems = new List<KeyValueItem>();
            string[] devicePath = SetupAPINativeMethods.GetDevicePath(Guid.Empty, PRINTER_CLASS_ID, null);
            if (devicePath == null || devicePath.Length == 0)
            {
                return kvItems;
            }
            else
            {
                int index = 1;
                for (int i = 0; i < devicePath.Length; i++)
                {
                    string path = devicePath[i];
                    string printerName = $"USB打印机 {index++}";
                    kvItems.Add(new KeyValueItem(printerName, "USB:"+path));
                }
                return kvItems;
            }
        }
    }
}
