﻿using System;
using System.Net.NetworkInformation;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using uPLibrary.Networking.M2Mqtt;
using uPLibrary.Networking.M2Mqtt.Messages;
using Newtonsoft.Json;
using System.Collections.Generic;
using zgLogging;

namespace zgUtils.Controls
{
    public class MqttCore : IDisposable
    {
        public static MqttCore Instance
        {
            get
            {
                if (MqttCore._instance == null)
                {
                    MqttCore._instance = new MqttCore();
                    Log.WriterNormalLog("MQTTCore-初始化物联网MQTT");
                }
                return MqttCore._instance;
            }
        }

        public MqttCore()
        {
            Log.WriterNormalLog("MQTTCore-监听系统网络状态");
            this.task = new Task(new Action(this.WaiteConnect));
            Log.WriterNormalLog("MQTTCore-重连线程初始化完毕");
        }

        private void WaiteConnect()
        {
            while (!this.isDisposable)
            {
                Thread.Sleep(600000);
                if (!this.IsConnected && !this.isDisposable)
                {
                    this.NewConnect();
                }
            }
        }

        private void NetworkChangeNetworkAddressChanged(object sender, EventArgs e)
        {
            this.Disconnect();
        }

        private void NetworkChangeNetworkAvailabilityChanged(object sender, NetworkAvailabilityEventArgs e)
        {
            if (e.IsAvailable)
            {
                //this.Disconnect();
            }
            else
            {
                this.Disconnect();
            }
        }

        private bool IsConnected
        {
            get
            {
                return this.client != null && (this.client.IsConnected && this.client.IsRunning && this.isconnected);
            }
        }

        private string TopicUrl { get; set; }

        private string ClientId { get; set; }

        private string Topic_user_name { get; set; }

        private string Topic_password { get; set; }
        Action<string> ReceivedMessage;
        private string Topic_sub_command { get; set; }

        private string Topic_report_command { get; set; }

        //public event MqttCore.PublishReceivedDelegate PublishReceived;

        public event MqttCore.ConnectedDelegate Connected;

        private bool Connect()
        {
            try
            {
                if (this.client.Connect(this.ClientId, this.Topic_user_name, this.Topic_password, true, 60) != 0)
                {
                    Log.WriterNormalLog("MQTTCore-未能连接到MQTT");
                    return false;
                }
                this.topics = this.Topic_sub_command.Split(';');
                this.client.Subscribe(this.topics, new byte[]
                {
                    1,1,1
                });
                Log.WriterNormalLog("MQTTCore-设备连接物联网成功");
                this.isconnected = true;
                MqttCore.ConnectedDelegate connected = this.Connected;
                if (connected != null)
                {
                    connected();
                }
            }
            catch (Exception ex)
            {
                this.isconnected = false;
                if (this.isDisposable)
                {
                    return false;
                }
                Log.WriterNormalLog("MQTTCore-连接物联网失败:" + ex.Message.ToString());
                return false;
            }
            return true;
        }
        public void Subscribe(Action<string> receivedMessage)
        {
            this.ReceivedMessage = receivedMessage;
            this.TopicUrl = CommonApp.Config?.ServerUrl?.TopicUrl;
            this.ClientId = CommonApp.DeviceId;//NetworkConnection.GetMac();
            this.Topic_user_name = "zgzn";
            this.Topic_password = "password";
            this.Topic_sub_command = string.Format("{0}/{1}/{2};{0}/{1}/{2}/{3};{0}/{1}/{2}/{3}/{4}", CommonApp.Config?.systemName,CommonApp.Config?.subName,CommonApp.sysUid, CommonApp.sysSid, CommonApp.DeviceId);
            //this.Topic_report_command = config.TopicSubReport;
            Log.WriterNormalLog("MQTTCore-开始新的连接");
            this.NewConnect();
            Log.WriterNormalLog("MQTTCore-连接执行完毕");
        }

        public void Unsubscribe()
        {
            if (this.client != null)
            {
                this.client.Unsubscribe(this.topics);
            }
        }

        public void Disconnect()
        {
            try
            {
                if (this.client != null)
                {
                    this.client.Disconnect();
                }
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog("Disconnect发生异常:" + ex.Message);
            }
        }

        private void NewConnect()
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(this.TopicUrl))
                {
                    if (this.client != null)
                    {
                        this.client.MqttMsgPublishReceived -= this.ClientMqttMsgPublishReceived;
                        this.client.MqttMsgUnsubscribed -= this.ClientMqttMsgUnsubscribed;
                        this.client.MqttMsgSubscribed -= this.ClientMqttMsgSubscribed;
                        this.client.ConnectionClosed -= this.ClientConnectionClosed;
                        this.isconnected = false;
                        this.client = null;
                    }
                    if (this.TopicUrl.StartsWith("ssl:"))
                    {
                        Uri uri = new Uri(this.TopicUrl);
                        this.client = new MqttClient(uri.Host, uri.Port, false, null, null, MqttSslProtocols.TLSv1_0);
                    }
                    else
                    {
                        this.client = new MqttClient(this.TopicUrl);
                    }
                    this.client.MqttMsgPublishReceived += this.ClientMqttMsgPublishReceived;
                    this.client.MqttMsgUnsubscribed += this.ClientMqttMsgUnsubscribed;
                    this.client.MqttMsgSubscribed += this.ClientMqttMsgSubscribed;
                    this.client.ConnectionClosed += this.ClientConnectionClosed;
                    this.Connect();
                }
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog("Reconnect:" + ex.Message);
            }
            finally
            {
                if (this.task.Status != TaskStatus.Running)
                {
                    this.task.Start();
                }
            }
        }

        private void ClientConnectionClosed(object sender, EventArgs e)
        {
            this.isconnected = false;
            if (!this.isDisposable)
            {
                Log.WriterNormalLog("MQTT连接中断,尝试重连");
                //if (!NetworkCenter.IsConnected)
                //{
                //    Log.WriterNormalLog("链接中断--没有网络");
                //    return;
                //}
            }
            else
            {
                Log.WriterNormalLog("MQTT连接中断, 程序已经退出,不进行重连");
            }
        }

        private void ClientMqttMsgPublishReceived(object sender, MqttMsgPublishEventArgs e)
        {
            string @string = Encoding.UTF8.GetString(e.Message);
            Log.WriterNormalLog("Received = " + @string + " on topic =" + e.Topic);
            this.ReceivedMessage(@string);

        }

        public void Publish(string message)
        {
            if (this.client != null)
            {
                byte[] bytes = Encoding.UTF8.GetBytes(message);
                this.client.Publish(this.Topic_report_command, bytes, 1, false);
            }
        }

        private void ClientMqttMsgUnsubscribed(object sender, MqttMsgUnsubscribedEventArgs e)
        {
            Log.WriterNormalLog("Unsubscribed for id = " + e.MessageId);
        }

        private void ClientMqttMsgSubscribed(object sender, MqttMsgSubscribedEventArgs e)
        {
            Log.WriterNormalLog("Subscribed for id = " + e.MessageId);
        }

        public void Dispose()
        {
            this.isDisposable = true;
            NetworkChange.NetworkAvailabilityChanged -= this.NetworkChangeNetworkAvailabilityChanged;
            NetworkChange.NetworkAddressChanged -= this.NetworkChangeNetworkAddressChanged;
            if (this.client != null)
            {
                try
                {
                    this.client.MqttMsgPublishReceived -= this.ClientMqttMsgPublishReceived;
                    this.client.MqttMsgUnsubscribed -= this.ClientMqttMsgUnsubscribed;
                    this.client.ConnectionClosed -= this.ClientConnectionClosed;
                    this.client.MqttMsgSubscribed -= this.ClientMqttMsgSubscribed;
                    this.client.Unsubscribe(this.topics);
                    this.client.Disconnect();
                    this.client = null;
                }
                catch (Exception ex)
                {
                    Log.WriterExceptionLog(ex.Message);
                }
            }
        }

        private Task task;

        private bool isDisposable;

        private bool isconnected;

        private static MqttCore _instance;

        private MqttClient client;

        private string[] topics;

        public delegate void PublishReceivedDelegate(MqttMessage message);

        public delegate void ConnectedDelegate();
    }
    public class MqttMessage<TPayLoad> : MqttMessage where TPayLoad : new()
    {
        [JsonProperty("data")]
        public TPayLoad Payload { get; set; }

        public MqttMessage()
        {
            this.Payload = Activator.CreateInstance<TPayLoad>();
            base.Version = "0.0.2";
        }
    }
    public class MqttMessage
    {
        [JsonProperty("bizId")]
        public int BizID { get; set; }

        [JsonProperty("bizName")]
        public string BizName { get; set; }

        [JsonProperty("command")]
        public string Command { get; set; }

        [JsonProperty("id")]
        public int ID { get; set; }

        [JsonProperty("remote")]
        public int Remote { get; set; }

        [JsonProperty("timestamp")]
        public long Timestamp { get; set; }

        [JsonProperty("traceId")]
        public string TraceId { get; set; }

        [JsonProperty("type")]
        public int Type { get; set; }

        [JsonProperty("uuid")]
        public string Uuid { get; set; }

        [JsonProperty("version")]
        public string Version { get; set; }

        public string Content { get; set; }

        [JsonProperty("bizBatchId")]
        public int BizBatchId { get; set; }
    }

    public enum MqttMessageType
    {
        UploadLog,
        UploadData,
        UploadFile,
        DownloadFile,
        PopMsg,
        ExecSql,
        LogOff,
        ClearData
    }
    public class MqttReceivedMessage
    {
        public MqttMessageType sendtype { get; set; }
        public Dictionary<string, object> data { get; set; }
        public string filepath { get; set; }
        public string orderno { get; set; }
        public string clientId { get; set; }
        public string orderData { get; set; }
        public string msg { get; set; }
    }
}
