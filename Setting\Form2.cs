﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Threading;


namespace Setting
{
    public partial class Form2 : Form
    {
        public Form2()
        {
            InitializeComponent();
        }
        delegate void SendToParent(string txt);

        // public delegate string SendToParent();
        //mysqlConnect conn = new mysqlConnect();
        //DataTable dt = new DataTable();
        //time tm = new time();
        private void Form1_Load(object sender, EventArgs e)
        {

            //conn.getConnection();

        }
        private void ConnServer()
        {
            SendToParent stc = new SendToParent(ConnServerRes);
            SendToParent lb = new SendToParent(lbtext);
            //线程的相关操作
            this.Invoke(lb, new object[] { "获取用户名...." });
            DataTable dm = new DataTable();
            string[] meber = new string[dm.Rows.Count];

            for (int i = 0; i < dm.Rows.Count; i++)
            {
                meber[i] = dm.Rows[i]["username"].ToString();

            }
            this.Invoke(stc, new object[] { "等待插入...." });
            Thread.Sleep(1000);
                {
                    this.Invoke(stc, new object[] { "插入成功" });
                }

            Thread.Sleep(1000);
            this.Invoke(stc, new object[] { "插入完毕" });

        }

        private void ConnServerRes(string str)
        {

            //操作主线程中的控件
            listBox1.Items.Add(str);

        }
        private void lbtext(string str)
        {

            //操作主线程中的控件
            label1.Text = str;

        }

        private void button2_Click(object sender, EventArgs e)
        {
            //string sql = "select * from data_content_1  order by ID ASC limit 50";
            //dt = conn.executeQuery(sql);
            //dataGrid1.DataSource = dt;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            


        }

        private void Form2_Load(object sender, EventArgs e)
        {

        }

        private void button1_Click_1(object sender, EventArgs e)
        {
            Thread connThread = new Thread(ConnServer);
            //connThread = new Thread(new ThreadStart(ConnServer));

            connThread.IsBackground = true;
            connThread.Start();
        }
    }
}