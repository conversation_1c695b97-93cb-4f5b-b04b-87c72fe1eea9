﻿using System;
using System.IO;
using System.Text;
using CefSharp;
using zgUtils.Extensions;
using zgUtils.Controls;

namespace zgpos.EventHandlers
{
    public static class ChromiumRegisterResourceHandler
    {
        
        internal static string GetErrorPageHtml(string title, string message = "")
        {
            var logoImageBase64 = zgCommon.Properties.Resources.LogoImage.ToBase64();
            var content = string.Format(zgCommon.Properties.Resources.ErrorPage, logoImageBase64, title, message);
            return content;
        }
    }
}
