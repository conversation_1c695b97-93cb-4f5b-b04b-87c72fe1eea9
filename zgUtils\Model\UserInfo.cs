﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgUtils.Model
{
    public class UserInfo {
        private string _accesstoken = "";
        public string sysUid { get; set; }
        public string sysSid { get; set; }
        [JsonProperty("token")]
        public string Accesstoken { get => _accesstoken?.Length > 10?(!string.IsNullOrEmpty(_accesstoken)&&_accesstoken.StartsWith("Bearer ") ? _accesstoken : "Bearer " + _accesstoken):""; set => _accesstoken = string.IsNullOrEmpty(value)?"":value; }
        public string name { get; set; }
        //public string Pass { get; set; }
        public long uid { get; set; }
        public string employeenumber { get; set; }//          remark: '工号'
        public string privilege { get; set; }
        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }
}
