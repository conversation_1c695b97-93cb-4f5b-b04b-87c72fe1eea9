﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgPrinter.Printer.TSPL
{
    public class TsplPrinter
    {
        private readonly List<string> cmdList = new List<string>();
        private readonly Encoding defaultEncoding = Encoding.GetEncoding("GB2312");

        /// <summary>
        /// 在队尾增加TSPL指令
        /// </summary>
        /// <param name="tspl"></param>
        public void PushTspl(string tspl)
        {
            if (!string.IsNullOrEmpty(tspl))
            {
                this.cmdList.Add(tspl);
            }
        }

        /// <summary>
        /// 在队尾增加TSPL指令
        /// </summary>
        /// <param name="tspl"></param>
        public void PushTspl(List<string> tspl)
        {
            this.cmdList.AddRange(tspl);
        }

        public TsplPrinter Home() {
            var cmd = $"HOME\r\n";
            cmdList.Add(cmd);
            return this;
        }

        /// <summary>
        /// 命令完成，输出byte数组
        /// </summary>
        /// <param name="printCount"></param>
        /// <returns></returns>
        public Byte[] Finish(int printCount = 1)
        {
            var cmd = $"PRINT {printCount}\r\n";
            cmdList.Add(cmd);
            var result = new List<byte>();
           
            foreach (var item in cmdList)
            {
                byte[] bytes = defaultEncoding.GetBytes(item);
                result.AddRange(bytes);
            }
            this.Clear();
            return result.ToArray();
        }

        /// <summary>
        /// 清除指令队列
        /// </summary>
        public void Clear()
        {
            cmdList.Clear();
        }

        /// <summary>
        /// 设定卷标纸的宽度及长度,英制系统(inch) SIZE W, H.公制系统(mm) SIZE Wmm, Hmm
        /// </summary>
        /// <param name="width"></param>
        /// <param name="height"></param>
        /// <returns></returns>
        public TsplPrinter SetSize(string width, string height)
        {
            var cmd = $"SIZE {width},{height}\r\n";
            cmdList.Add(cmd);
            return this;
        }

        /// <summary>
        /// 定义打印时出纸和打印字体的方向
        /// </summary>
        /// <param name="direction">0||1</param>
        /// <returns></returns>
        public TsplPrinter SetDirection(int direction)
        {
            var cmd = $"DIRECTION {direction}\r\n";
            cmdList.Add(cmd);
            return this;
        }

        /// <summary>
        /// 该指令定义两张卷标纸间的垂直间距距离,英制系统 GAP m, n, 公制系统 (mm) GAP m mm, n mm
        /// </summary>
        /// <param name="m">width</param>
        /// <param name="n">height</param>
        /// <returns></returns>
        public TsplPrinter SetGap(string m = "2mm", string n = "0mm")
        {
            var cmd = $"GAP {m},{n}\r\n";
            cmdList.Add(cmd);
            return this;
        }

        /**
         * 于控制打印时的浓度. 0, 使用最淡的打印浓度 15, 使用最深的打印浓
         *
         * @param density
         */
        public TsplPrinter SetDensity(int density = 8)
        {
            var cmd = "DENSITY ${density}\r\n";
            cmdList.Add(cmd);
            return this;
        }

        /// <summary>
        /// 定义卷标的参考坐标原点
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <returns></returns>
        public TsplPrinter SetReference(int x = 0, int y = 0)
        {
            var cmd = $"REFERENCE {x},{y}\r\n";
            cmdList.Add(cmd);
            return this;
        }

        /// <summary>
        /// 用于清除影像缓冲区(image buffer)的数据
        /// </summary>
        /// <returns></returns>
        public TsplPrinter CLS()
        {
            var cmd = "CLS\r\n";
            cmdList.Add(cmd);
            return this;
        }

        /// <summary>
        /// 打印文字
        /// </summary>
        /// <param name="x">文字 X 方向启始点坐标</param>
        /// <param name="y">文字 Y 方向启始点坐标</param>
        /// <param name="text">打印文本</param>
        /// <param name="rotation">文字旋转角度(顺时钟方向)，0，90，180，270</param>
        /// <param name="xMultiplication">X 方向放大倍率 1~10</param>
        /// <param name="yMultiplication">Y 方向放大倍率 1~10</param>
        /// <returns></returns>
        public TsplPrinter PrintText(
            int x,
            int y,
            string text,
            int rotation = 0,
            int xMultiplication = 1,
            int yMultiplication = 1
        )
        {
            var cmd =
                $"TEXT {x},{y},\"TSS24.BF2\",{rotation},{xMultiplication},{yMultiplication},\"{text}\"\r\n";
            cmdList.Add(cmd);
            return this;
        }

        /// <summary>
        /// 该指令用来画一维条码
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <param name="height">条形码高度，以点(dot)表示</param>
        /// <param name="codeType">条码类型</param>
        /// <param name="code">条码内容</param>
        /// <param name="rotation">条形码旋转角度，顺时钟方向</param>
        /// <returns></returns>
        public TsplPrinter PrintBarCode(
            int x,
            int y,
            int height,
            string codeType,
            string code,
            int rotation = 0
        )
        {
            var cmd = $"BARCODE {x},{y},\"{codeType}\",{height},1,{rotation},2,2,\"{code}\"\r\n";
               cmdList.Add(cmd);
            return this;
        }
    }
}
