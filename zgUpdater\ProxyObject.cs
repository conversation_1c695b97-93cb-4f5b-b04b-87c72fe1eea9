﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace zgUpdater
{
    public class ProxyObject : MarshalByRefObject
    {
        Assembly assembly = null;
        string commonDllPath = Environment.CurrentDirectory + @"\Programs\Libs\zgCommon.dll";

        public void LoadAssembly()
        {

            assembly = Assembly.LoadFrom(commonDllPath);
        }

        public bool Invoke(string fullClassName, string methodName, params Object[] args)
        {
            if (assembly == null)
                return false;
            Type tp = assembly.GetType(fullClassName);
            if (tp == null)
                return false;
            MethodInfo method = tp.GetMethod(methodName);
            if (method == null)
                return false;

            method.Invoke(null, args);
            return true;
        }

    }
}
