﻿using Newtonsoft.Json;
using System;
using zgAdvert.Extensions;

namespace zgAdvert.Network
{
    public abstract class RequestBase
    {
        public virtual RequestInfoBase RequestInfo
        {
            get
            {
                if (this._requestInfoBase == null)
                {
                    this._requestInfoBase = new EXFRequestInfo();
                }
                return this._requestInfoBase;
            }
        }

        public virtual string PostContent()
        {
            return string.Format("{0}", "{}");
        }

        public string Uuid { get; set; }

        public abstract string ActionParam { get; }

        public virtual string RequestBodyJson { get; }

        public string Authorization { get; set; }

        public string CreateURL()
        {
            string requestBodyJson = "{}";
            if (!string.IsNullOrWhiteSpace(this.RequestBodyJson))
            {
                requestBodyJson = this.RequestBodyJson;
            }
            string arg = this.RequestInfo.CreateURl(requestBodyJson, DateTime.Now.DateTimeToStamp().ToString(), this.Uuid);
            return string.Format("{0}{1}{2}", this.RequestInfo.BaseUrl, this.ActionParam, arg);
        }

        public string ToJson(object targert)
        {
            return JsonConvert.SerializeObject(targert);
        }

        public string ToJson()
        {
            return JsonConvert.SerializeObject(this);
        }

        public RequestBase()
        {
            this.Uuid = string.Empty;
        }

        protected RequestInfoBase _requestInfoBase;
    }
    public abstract class RequestBase<TRequestBody> : RequestBase where TRequestBody : new()
    {
        public TRequestBody RequestBody { get; set; }

        public RequestBase()
        {
            this.RequestBody = Activator.CreateInstance<TRequestBody>();
        }

        public override string RequestBodyJson
        {
            get
            {
                return base.ToJson(this.RequestBody);
            }
        }

        public override string PostContent()
        {
            return string.Format("{0}", base.ToJson(this.RequestBody));
        }
    }
}
