﻿using NLog;
using NLog.Config;
using NLog.Targets;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace zgUpdater
{
    class NLogger
    {
        private static int _tempLogDay = 30;
        Logger logger { get; set; } = null;
        bool isInitialized { get; set; } = false;
        bool isRunning { get; set; } = false;
        //private static object obj{ get; } = log4net.Config.BasicConfigurator.Configure();
        public NLogger() { 
        
        }
        public void WriteLog(LogLevel level, string msg)
        {
            if (isInitialized && isRunning)
            {
                try
                {

                    logger.Log(level, msgWithTime(level, msg));
                }
                catch (Exception ex)
                {
                    logger.Fatal(ex, msgWithTime(LogLevel.Error,"LibraryLogger Exception"));
                }
            }
        }
        private string msgWithTime(LogLevel level, string msg)
        {
            return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff ") + "|" + level + "|" + Thread.CurrentThread.ManagedThreadId + " =>" + msg;
        }
        public static NLogger Instance { get; } = new NLogger();

        public void Init(string DirectoryPath, string LogName, Encoding encoding, LogLevel loglevel, long MaximumFileSize = 1024 * 1024 * 20)
        {
            isRunning = true;

            // Step 1. Create configuration object 
            var config = new LoggingConfiguration();

            // Step 2. Create targets and add them to the configuration 
            var fileTarget = new FileTarget();
            config.AddTarget(LogName, fileTarget);

			// Step 3. Set target properties 
			fileTarget.Layout = "${message}";
            fileTarget.FileName = DirectoryPath + "\\" + "${shortdate}.log";
            //fileTarget.ArchiveAboveSize = MaximumFileSize;
            //fileTarget.ArchiveEvery = FileArchivePeriod.Day;
            //fileTarget.ArchiveFileName = DirectoryPath + "/${shortdate}/" + LogName + "_{#}.log";
            //fileTarget.ArchiveNumbering = ArchiveNumberingMode.DateAndSequence;
            fileTarget.MaxArchiveFiles = _tempLogDay;
            //fileTarget.MaxArchiveDays = _tempLogDay;
            fileTarget.Encoding = encoding;
            //fileTarget.DeleteOldFileOnStartup = true;


            NLog.LogLevel lv = NLog.LogLevel.Debug;
            if (loglevel == LogLevel.Debug)
                lv = NLog.LogLevel.Debug;
            else if (loglevel == LogLevel.Error)
                lv = NLog.LogLevel.Error;
            else if (loglevel == LogLevel.Fatal)
                lv = NLog.LogLevel.Fatal;
            else if (loglevel == LogLevel.Info)
                lv = NLog.LogLevel.Info;
            else if (loglevel == LogLevel.Off)
                lv = NLog.LogLevel.Off;
            else if (loglevel == LogLevel.Trace)
                lv = NLog.LogLevel.Trace;
            else if (loglevel == LogLevel.Warn)
                lv = NLog.LogLevel.Warn;

            // Step 4. Define rules
            var rule = new LoggingRule(LogName, lv, fileTarget);
            config.LoggingRules.Add(rule);

            // Step 5. Activate the configuration
            LogManager.Configuration = config;
            logger = LogManager.GetLogger(LogName);
            TaskScheduler.UnobservedTaskException += (sender, args) =>
            {
                foreach (var ex in args.Exception.InnerExceptions)
                {
                    logger.Fatal(ex.ToString());
                }
                args.SetObserved();
            };

            isInitialized = true;

			
        }

        public void Init(string DirectoryPath,long MaximumFileSize = 1024 * 1024 * 20)
        {
            string LogName = DateTime.Today.ToString("yyyyMMdd");
            Init(DirectoryPath, LogName, Encoding.UTF8, LogLevel.Info, MaximumFileSize);
            logger.Info("zgUpdater 程序启动...");
        }

    }
}
