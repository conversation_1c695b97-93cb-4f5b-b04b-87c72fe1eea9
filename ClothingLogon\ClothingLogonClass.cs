﻿using System.Drawing;
using System.Windows.Forms;

using zgpos.LogonTemplate;

namespace ClothingLogon
{
    public class ClothingLogonClass : LogonTemplateClass
    {
        public override string Name { get; protected set; } = "服装";

        public override string Description { get; protected set; } = "登录画面 - wwj";

        public override Image Preview { get; protected set; } = ClothingLogon.Properties.Resources.DefaultLogonPreview;

        public ClothingLogonClass()
        {
        }

        public override string FileName => System.Reflection.Assembly.GetExecutingAssembly().ManifestModule.ScopeName;

        protected override Form CreateLogonForm()
        {
            return new ClothingLogonForm() { ParentLogon = this };
        }

    }
}
