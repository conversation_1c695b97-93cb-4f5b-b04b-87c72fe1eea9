﻿using CefSharp;
using CefSharp.DevTools.Runtime;
using CefSharp.WinForms;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml;
using System.Xml.Serialization;
using zgLogging;
using zgpos.Browser;
using zgpos.EventHandlers;
using zgpos.ProgramTemplate;
using zgUtils;
using zgUtils.Controls;
using zgUtils.Files;
using zgUtils.Model;
using zgzn;

namespace zgpos
{
    public class App
    {
        private static SQLitePlugin plugin = new SQLitePlugin();
        public static BrowserBack browserBackObj = null;
        public static BrowserShow browserShowObj = null;
        //internal static BrowserPopup popupObj;
        internal static bool isReload;
        internal static Assembly assembly;
        internal static bool isSwitchIndustry;

        public static string MainPageUrl { get; set; }
        public static bool AllowToQuit { get; set; } = true;

        internal static void ReadCard_OnValueChanged(object sender, EventArgs e)
        {
            Log.WriterNormalLog("code:" + sender);
            App.browserShowObj.RunJS("demo.$store.commit('SET_SHOW',{cardNo: '" + sender + "'})");
        }
        internal static void CreateBrowserBack(FormBack formBack)
        {
            browserBackObj = new BrowserBack(formBack);

        }
        public static void Init(ProgramTemplateClass mainClass, Assembly _assembly, bool iSPRODCT=true,string defaultConfigURL = "")
        { 
            CommonApp.MainClass = mainClass;
            CommonApp.ProgramFile = Path.Combine(zgzn.UnityModule.ProgramDirectory, mainClass.FileName);
            CommonApp.systemTitle = mainClass.Name;
            AllowToQuit = true;
            assembly = _assembly;
        }
        public static void SwitchIndustry(string className, string classUrl, IJavascriptCallback onsuccess = null)
        {

            SwitchIndustry(IndustryClass.GetIndustry(className), onsuccess);
        }
        internal static void SwitchIndustry(IndustryClass industry, IJavascriptCallback onsuccess)
        {
            Log.WriterNormalLog("切换到业态："+JsonConvert.SerializeObject(industry));
            var result = "";
            //下载
            string ProgramPath = zgzn.FileController.PathCombine(zgzn.UnityModule.ProgramDirectory, industry.className);
            
            if (onsuccess!=null){ onsuccess.ExecuteAsync(result);}
            if (!File.Exists(ProgramPath))
            {
                Dictionary<string, object> dics = ConfigController.dics;
                if (ConfigController.dics != null) {
                    ConfigController.dics["ProgramFile"] = industry.className;
                    ConfigController.dics["ProgramFileUrl"] = industry.classUrl;
                }
                
            }
            //重新设置config
            App.browserBackObj?.CloseAll();
            App.isSwitchIndustry = true;
            ConfigController.SaveIndustryConfig(industry);
            
            CommonApp.mainform?.Close();
            Log.WriterNormalLog(industry.className + "业态加载开始.....");

        }

        public static Control initMainBrowser(Form form)
        {
            Log.WriterNormalLog("登录画面初始加载......");
            CommonApp.mainform = form;
            // 初始化浏览器
            return new BrowserMainShow(form).browser;
        }
        public static Control initBrowser(Form form)
        {
            Log.WriterNormalLog("登录画面初始加载......");
            browserShowObj = new BrowserShow(form);
            CommonApp.mainform = form;
            // 初始化浏览器
            return browserShowObj.browser;
        }
        public static void loginSynced()
        {
            CommonApp.step = CommonApp.Setp.Step_2;
            App.browserShowObj.RunJS(CommonApp.GetInitDataJsonStr());
            //if (CommonApp.secondaryScreen != null && App.browserBackObj != null)
            //{
            //    App.browserBackObj.reloadAdvertData();
            //}
        }

    }
}
