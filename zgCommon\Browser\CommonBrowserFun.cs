﻿using CefSharp;
using ESCPOS.Printer;
using Newtonsoft.Json.Linq;
using PrintCore;
using System;
using System.Drawing;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.Speech.Synthesis;

using System.Data;
using System.Collections.Generic;


using zgUtils.Controls;
using zgUtils.Model;
using zgUtils;
using zgSerialPort;
using zgSerialPort.Common;
using zgLogging;
using zgReadCard;
using zgUtils.Extensions;
using zgAdvert.Model;
using NAudio.Wave;
using NAudio.Wave.SampleProviders;
using zgPrinter.Model;
using zgPrinter;
using Microsoft.VisualBasic.Devices;
using static zgUtils.CommonApp;
using System.Net;
using System.Threading;
using CefSharp.DevTools.Network;
using System.Text;
using System.Net.Http;

namespace zgpos.Browser
{
    public class CommonBrowserFun
    {
        private RS232 _rs232;
        private SpeechSynthesizer synth;
        private InstalledVoice iv;
        private WaveOut waveout;
        public CommonBrowserFun()
        {

        }
        public void showPopup()
        {
            //if (App.popupObj != null)
            //{
            //    ((Popup.Frm_Popup)App.popupObj.mainform).init();
            //}
        }
        public void showDevTools()
        {
            App.browserShowObj.browser.GetBrowser().ShowDevTools();
        }
        public void switchwindow(object param, IJavascriptCallback onsuccess = null)
        {

        }
        /// <summary>
        /// 切换业态
        /// </summary>
        /// <param name="className">xxxx.dll</param>
        /// <param name="classUrl">https://xxxx/xxxx.dll</param>
        /// <param name="onsuccess"></param>
        public void switchIndustry(string className, string classUrl, IJavascriptCallback onsuccess = null)
        {
            App.SwitchIndustry(className, classUrl, onsuccess);
        }
        #region //扫描枪
        internal static ScanerHook scanerHook;
        public void scanerHookStart()
        {
            if (scanerHook == null)
            {
                scanerHook = new ScanerHook((ScanerHook.ScanerCodes scanerCodes) =>
                {
                    Log.Info("scanerHookStart Start");
                    string scanerStr = JsonConvert.SerializeObject(scanerCodes);
                    Log.Info("scanerHookStart END");
                    App.browserShowObj.RunJS("console.log('" + scanerStr + "');");
                    App.browserShowObj.RunJS("$scanerObj = '" + scanerStr + "';$scanerCodes = '" + scanerCodes.Result + "';");
                    Log.Info("browserShowObj.RunJS END");
                });
            }
            CommonApp.mainform.InvokeOnUiThreadIfRequired(() => scanerHook?.Start());
        }

        public void scanerHookStop()
        {
            CommonApp.mainform.InvokeOnUiThreadIfRequired(() => scanerHook?.Stop());
        }
        #endregion
        /// <summary>
        /// index画面启动时调用
        /// </summary>
        /// 
        public string created(IJavascriptCallback onsuccess = null)
        {

            string result = CommonApp.SendToWebBrowser();

            if (onsuccess != null)
            {
                onsuccess.ExecuteAsync("created");

            }

            return result;
        }
        public void getProducts(int startLine,int cnt) {

            //CommonApp.getCommonProduct("6901025400674");
            string filePath = Environment.CurrentDirectory + @"\Programs\Libs\products.txt"; // 替换为你的文件路径
            Log.Info("读取文件开始：" + filePath);
            try
            {
                using (StreamReader sr = new StreamReader(filePath))
                {
                    int lineCount = 0;
                    string line;

                    while ((line = sr.ReadLine()) != null)
                    {
                        lineCount++;

                        if (lineCount >= startLine && lineCount < startLine+cnt)
                        {
                            System.Threading.Thread.Sleep(1);
                            HttpPost(line);
                            Log.Info(line);
                        }

                    }
                }
            }
            catch (Exception e)
            {
                Log.WriterExceptionLog("读取文件时发生错误：" + e.Message);
                Console.WriteLine("读取文件时发生错误：" + e.Message);
            }
            
        }
        public void HttpPost(string code)

        {
            try
            {
                Product Param = new Product(code);
                var url = "http://pos.zhangguizhinang.com/POSAPI/products/getCommonProducts?code="+code;
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
                request.Method = "POST";
                request.ContentType = "application/json";
                request.Accept = "application/json";
                request.Headers.Add("Authorization", "Bearer eyJhbGciOiJIUzUxMiJ9.eyJhZHZlcnRNb2R1bGVVcmwiOiJodHRwczovL2Rldi56aGFuZ2d1aXpoaW5hbmcuY29tL3pnem4tYWR2ZXJ0Iiwic2NoZW1hIjoicG9zIiwic2V0dGluZ01vZHVsZVVybCI6Imh0dHBzOi8vZGV2LnpoYW5nZ3VpemhpbmFuZy5jb20vemd6bi1zZXR0aW5ncyIsInBvc01vZHVsZVVybCI6Imh0dHBzOi8vZGV2LnpoYW5nZ3VpemhpbmFuZy5jb20vemd6bi1wb3MiLCJzdWIiOiIxNzcwNTM1MTkxNCIsImNsaWVudEFnZW50IjoicGMiLCJwcm9kdWN0TW9kdWxlVXJsIjoiaHR0cHM6Ly9kZXYuemhhbmdndWl6aGluYW5nLmNvbS96Z3puLXByb2R1Y3RzIiwicGFydGl0aW9uSWQiOjQ5LCJjcmVhdGVkIjoxNzAyMjcxMjkyNjQzLCJ2aXBNb2R1bGVVcmwiOiJodHRwczovL2Rldi56aGFuZ2d1aXpoaW5hbmcuY29tL3pnem4tdmlwIiwibmdpbnhNb2R1bGVVcmwiOiJodHRwczovL2Rldi56aGFuZ2d1aXpoaW5hbmcuY29tIiwidWlkIjoxLCJzeXN0ZW1OYW1lIjoiemd6biIsInVzZXJNb2R1bGVVcmwiOiJodHRwczovL2Rldi56aGFuZ2d1aXpoaW5hbmcuY29tL3pnem4tdXNlcnMiLCJzdWJOYW1lIjoicG9zIiwic3lzVWlkIjoiMTc3MDUzNTE5MTQiLCJleHBpcmF0aW9uIjp0cnVlLCJzeXNTaWQiOjEsImV4cCI6MTcwMzU2NzI5Mn0.F2gSk13zaDpGuuRaVggMnL-i92R9XX5yddI8vkF9Mkrlbt03M1hX_53_OK6BkL-pJMAs08tEcvOHA05fv1448A");
                string data = JsonConvert.SerializeObject(Param);
                byte[] byteData = Encoding.UTF8.GetBytes(data);
                request.ContentLength = byteData.Length;
                using (Stream requestBody = request.GetRequestStream())
                {
                    requestBody.Write(byteData, 0, byteData.Length);
                }
                using (WebResponse response = request.GetResponse())
                {
                    //using (Stream responseStream = response.GetResponseStream())
                    //{
                    //    //using (StreamReader streamReader = new StreamReader(responseStream, Encoding.UTF8))
                    //    //{
                    //    //    string responseData = streamReader.ReadToEnd();
                    //    //    JuejinArticleDto articleDto = JsonConvert.DeserializeObject<JuejinArticleDto>(responseData);
                    //    //    Console.WriteLine($"文章标题：{articleDto.Data.Title}");
                    //    //}
                    //}
                }
                //Console.ReadKey();
            }
            catch  { 
                
            }

        }
    public void getCommonProduct(string code)
        {
            Product Param = new Product(code);
            var url = "http://pos.zhangguizhinang.com/POSAPI/products/getCommonProducts";
            step = Setp.Step_1;
            Log.Info(string.Format("登录参数：{0}", JsonConvert.SerializeObject(Param)));
            //RequestBase<Product> request = new RequestBase<Product>(url, "", Param);
            //Log.WriterNormalLog(string.Format("请求远程接口登录：{0}", request.ToJson()));
            //result = NetworkCenter.Instance.SendRequest<RequestBase<Product>, ResponseJsonStr>(request);
            //Log.Info(string.Format("请求远程接口登录：{0}", result.ToJson()));
            Req<string> req = new Req<string>(url, JsonConvert.SerializeObject(Param));
            var result = ApiHelper.PostAsJsonAsync<string,ResponseBase>(req).Result.RespData;


            //if (result.Code != 200)
            //{
            //    Log.Info(string.Format("请求远程接口登录：{0}", result.ToJson()));

            //}

        }
        public void login(object param, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null, IJavascriptCallback onfail2 = null)
        {
            string result = "{}";
            try
            {
                string jsonstr = JsonConvert.SerializeObject(param);
                LoginRequest loginParam = JsonConvert.DeserializeObject<LoginRequest>(jsonstr);
                ResponseLogin response = CommonApp.Logon(loginParam);

                if (response.Code == 200)
                {
                    try
                    {
                        if (CommonApp.secondaryScreen != null && App.browserBackObj != null)
                        {
                            Log.WriterNormalLog(string.Format("登录初始化成功，副屏广告加载开始......"));
                            App.browserBackObj.reloadAdvertData();
                        }
                        else
                        {
                            Log.WriterNormalLog(string.Format("登录初始化成功，广告加载开始......"));
                            zgAdvert.AdvertFactory.Instance.Init(CommonApp.Config.ServerUrl.AdvertBaseUrl, CommonApp.Config.Base.AdvertLocalUrl, CommonApp.Directory, CommonApp.Authorization, CommonApp.DeviceId);
                        }
                        Log.WriterNormalLog(string.Format("广告加载完成。"));
                    }
                    catch (Exception ex)
                    {
                        Log.WriterExceptionLog(string.Format("广告加载失败：{0}", ex.Message));
                    }
                    result = JsonConvert.SerializeObject(response);
                    //Log.WriterNormalLog(string.Format("登录完成：{0}", result));
                    if (onsuccess != null)
                    {
                        onsuccess.ExecuteAsync(result);
                    }
                }
                else
                {
                    if (onfail2 != null)
                    {
                        response.data = null;
                        result = JsonConvert.SerializeObject(response);
                        Log.WriterExceptionLog(string.Format("登录失败：{0}", result));
                        if (onfail != null)
                        {
                            onfail.ExecuteAsync(result);
                        }
                    }
                    else
                    {
                        var msg = response.Message ?? "error";
                        Log.WriterExceptionLog(string.Format("登录失败：{0}", msg));
                        if (onfail != null)
                        {
                            onfail.ExecuteAsync(msg);
                        }
                    }                    
                }
            }
            catch (Exception e)
            {
                Log.WriterExceptionLog("login 登录失败：" + e.Message);
                if (onfail != null)
                {
                    onfail.ExecuteAsync(e.Message);
                }
            }
        }
        /// <summary>
        /// 画面登录后云同步后调用
        //       external.loginSynced(
        //         {
        //           sys_uid:demo.$store.state.show.sys_uid,
        //           sys_sid:demo.$store.state.show.sys_sid,
        //           printer_name:demo.$store.state.show.setting_small_printer,
        //           token:demo.$store.state.show.token
        //         }
        //         ,function(data)
        //   {
        //       alert(data);
        //                }
        //       );
        /// </summary>
        /// <param name="param"></param>
        /// <param name="onsuccess"></param>
        public void loginSynced(IJavascriptCallback onsuccess = null, IJavascriptCallback mqttCallBack = null, IJavascriptCallback alimqttCallBack = null)
        {
            try
            {
                App.loginSynced();
                onsuccess?.ExecuteAsync("");
                if (alimqttCallBack != null || CommonApp.Config?.mqttOptions?.defaultTopic != null)
                {
                    openAliMqtt(alimqttCallBack, mqttCallBack, CommonApp.Config?.mqttOptions?.defaultTopic != null);
                }
                
                //if (!CommonApp.Mqtt.Equals(-1) || CommonApp.isGrayUser)
                //{
                //    openMqtt(mqttCallBack);
                //}
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }
        }
        public void closeAliMqtt(IJavascriptCallback mqttCallBack)
        {
            AliMqttCore.Instance.Dispose();
        }
        public void connectedAliMqtt(IJavascriptCallback mqttCallBack)
        {
            mqttCallBack?.ExecuteAsync(AliMqttCore.Instance.isconnected);
        }
        public bool openAliMqtt(IJavascriptCallback aliMqttCallBack, IJavascriptCallback mqttCallBack, bool defaultConnect)
        {
            return AliMqttCore.Instance.Subscribe(delegate (uPLibrary.Networking.M2Mqtt.Messages.MqttMsgPublishEventArgs ReceivedMessage)
            {
                

                try
                {

                    string @string = System.Text.Encoding.UTF8.GetString(ReceivedMessage.Message);
                    MqttReceivedMessage mqttReceivedMessage = JsonConvert.DeserializeObject<MqttReceivedMessage>(@string);
                    if (!string.IsNullOrWhiteSpace(mqttReceivedMessage.orderData))
                    {
                        aliMqttCallBack?.ExecuteAsync(@string);
                        //AliMqttCore.Instance.P2PCallback(mqttReceivedMessage, ReceivedMessage.Topic, string.Format("{{\"code\":{0},\"msg\":\"{1}\"}}", 0, "下单成功。"));
                    }
                    else
                    {
                        if (MqttMessageType.LogOff.Equals(mqttReceivedMessage.sendtype)) {
                            logOff(null);
                        }
                        mqttCallBack?.ExecuteAsync(@string);
                    }                    
                }
                catch (Exception ex)
                {
                    //AliMqttCore.Instance.P2PCallback(mqttReceivedMessage, ReceivedMessage.Topic, string.Format("{{\"code\":{0},\"msg\":\"{1}\",errmsg:{2}}}", 1, "下单失败。", ex.Message));
                    Log.WriterExceptionLog(ex.Message);
                }
            }, aliMqttCallBack != null, defaultConnect);
        }
        public void publishSynced(object param, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null)
        {

            try
            {
                string message = JsonConvert.SerializeObject(param);
                Log.Info(message);
                AliMqttCore.Instance.publishSynced(message);
                if (onsuccess != null)
                {
                    onsuccess.ExecuteAsync("publish success");
                }
            }
            catch (Exception ex)
            {
                if (onfail != null)
                {
                    onfail.ExecuteAsync(ex.Message);
                }
                Log.WriterExceptionLog(ex.Message);
            }

        }
        public void logOff(IJavascriptCallback onsuccess = null)
        {
            try
            {
                CommonApp.isClose = true;
                Computer computer = new Computer();
                string DirectoryName = CommonApp.SubDirectory + "-" + CommonApp.sysUid + "-" + CommonApp.sysSid ;
                try
                {
                    computer.FileSystem.DeleteFile(CommonApp.ProgramFile+".config");
                
                }
                catch { }
                try
                {
                    computer.FileSystem.DeleteFile(zgzn.ConfigController.IniPath);

                }
                catch { }
                
                SQLiteHelper.Exit();
                computer.FileSystem.RenameDirectory(CommonApp.BaseDirectory + DirectoryName, DirectoryName + new Random().Next().ToString());
                if (onsuccess != null)
                {
                    onsuccess.ExecuteAsync("uploaddata success");
                }
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }
        }
        public void switchUser(string sysUid = null, int? sysSid = null)
        {

            try
            {
                CommonApp.ClearUser(sysUid, sysSid);
                App.browserShowObj.browser.Reload();
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }
        }
        public void uploaddata(IJavascriptCallback onsuccess = null)
        {
            try
            {
                Utils.UpLoadDatadbFile(string.Format("{0}/upload/uploadFile", CommonApp.Config.ServerUrl.USERURL), SQLiteHelper.FullPath);
                if (onsuccess != null)
                {
                    onsuccess.ExecuteAsync("uploaddata success");
                }
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }
        }
        public void downloaddata(String param, IJavascriptCallback onsuccess = null)
        {
            if (onsuccess != null)
            {
                onsuccess.ExecuteAsync("popmsg success");
            }
        }
        public void popmsg(String param, IJavascriptCallback onsuccess = null)
        {
            App.browserShowObj.browser.GetBrowser().MainFrame.EvaluateScriptAsync(param);
            if (onsuccess != null)
            {
                onsuccess.ExecuteAsync("popmsg success");
            }
        }
        /// <summary>
        /// db文件备份
        /// </summary>
        /// <param name="sourceFilePath"></param>
        /// <param name="targetFilePath"></param>
        public void CopyData(IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null)
        {
            try
            {
                string sourceFilePath = SQLiteHelper.FullPath;
                string targetFilePath = string.Format(@"{0}." + DateTime.Now.ToString("yyyyMMddHHmmss"), sourceFilePath);
                File.Copy(sourceFilePath, targetFilePath, true);
                if (onsuccess != null) {
                    onsuccess.ExecuteAsync("ok");
                }
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog("data.db 备份失败。");
                if (onfail != null)
                {
                    onfail.ExecuteAsync(ex.Message);
                }
            }

        }
        /// <summary>
        /// 关闭按钮隐藏
        /// </summary>
        public void hideControlBox()
        {
            CommonApp.mainform.ControlBox = false;
        }
        /// <summary>
        /// 关闭按钮显示
        /// </summary>
        public void showControlBox()
        {
            CommonApp.isClose = false;
            CommonApp.mainform.ControlBox = true;
        }
        /// <summary>
        /// 关闭按钮确认框
        /// </summary>
        public object showTipsDialog()
        {
            CommonApp.isClose = true;
            hideControlBox();
            return App.browserShowObj?.EvaluateScript("demo.$store.state.show.showTipsDialog=true;", TimeSpan.FromSeconds(3));
        }
        /// <summary>
        /// 关闭取消
        /// </summary>
        public void closeCancel()
        {
            showControlBox();
        }
        
        /// <summary>
        /// //退出 重新登录
        /// </summary>
        public void reloadForm()
        {
            reloadMainForm();
        }
        /// <summary>
        /// //退出 重新登录
        /// </summary>
        public void reloadMainForm()
        {
            try
            {
                App.isReload = true;
                Log.WriterNormalLog("reloadMainForm:start");
                CommonApp.isClose = true;
                CloseMainForm();
                Log.WriterNormalLog("reloadMainForm:end");

                System.Threading.Thread.Sleep(5000);
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }
        }
        /// <summary>
        /// 调用方法，关闭主窗口程序
        /// </summary>
        public void CloseMainForm()
        {
            try
            {
                CommonApp.mainform.InvokeOnUiThreadIfRequired(() =>
                {
                    try
                    {

                        Log.WriterNormalLog("CloseMainForm:start");
                        CommonApp.mainform.ControlBox = true;
                        closeSecondScreen();
                        App.browserShowObj.CloseApp();
                        Log.WriterNormalLog("CloseMainForm:end");
                    }
                    catch (Exception ex)
                    {
                        Log.WriterNormalLog("CloseMainForm:" + ex.Message);
                    }


                });
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog("CloseMainForm:" + ex.Message);

            }
        }

        public bool isConnected()
        {
            bool isconnected = NetworkCenter.IsConnected;
            Log.WriterNormalLog("isConnected........................" + isconnected.ToString());
            return isconnected;
        }
        public string getMac()
        {
            return CommonApp.DeviceId;
            //return NetworkConnection.GetMac();
        }
        public int getDeviceCode()
        {
            return CommonApp.Config.deviceCode;
        }
        public string getVersion()
        {
            string version = CommonApp.Version + (CommonApp.ISPRODCT ? "" : "(体验版)");

            return version;
        }
        
        public string getApiUrl(string type)
        {
            if (CommonApp.Config == null) return "";
            string url = "";
            switch (type)
            {
                case "userUrl":
                    url = CommonApp.Config.ServerUrl.USERURL;
                    break;
                case "settingUrl":
                    url = CommonApp.Config.ServerUrl.SETTINGURL;
                    break;
                case "ip":
                    url = CommonApp.Config.ServerUrl.HttpUrl;
                    break;
                case "posUrl":
                    url = CommonApp.Config.ServerUrl.POSURL;
                    break;
                case "productUrl":
                    url = CommonApp.Config.ServerUrl.PRODUCTURL;
                    break;
                case "vipUrl":
                    url = CommonApp.Config.ServerUrl.VIPURL;
                    break;
                case "pushUrl":
                    url = CommonApp.Config.ServerUrl.PUSHURL;
                    break;
                case "payUrl":
                    url = CommonApp.Config.ServerUrl.PAYURL;
                    break;
                case "billUrl":
                    url = CommonApp.Config.ServerUrl.BILLURL;
                    break;
                case "advertUrl":
                    url = CommonApp.Config.ServerUrl.AdvertBaseUrl;
                    break;
                case "agentUrl":
                    url = CommonApp.Config.ServerUrl.AgentUrl;
                    break;
                case "webSocketUrl":
                    url = CommonApp.Config.ServerUrl.WEBSOCKETURL;
                    break;
                case "weixinModuleUrl":
                    url = CommonApp.Config.ServerUrl.weixinModuleUrl;
                    break;
                case "weixinUrl":
                    url = CommonApp.Config.ServerUrl.weixinUrl;
                    break;
                default:
                    break;
            }
            return url;
        }
        #region //打印处理
        public void saveImage(string type, string filebase64, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null)
        {
            try
            {
                string path = Path.Combine(CommonApp.Directory, CommonApp.Config.Base.LogoDir);
                //if (!Directory.Exists(path))
                //{
                //    Directory.CreateDirectory(path);
                //}
                string filename = type + ".png";
                string destFileName = Path.Combine(path, filename);

                Image image = new Bitmap(BitmapExtension.Base64StringToImage(filebase64.Substring(filebase64.IndexOf(",") + 1)));
                if (File.Exists(destFileName))
                {
                    File.Delete(destFileName);
                }
                image.Save(destFileName);

                string newfilepath = CommonApp.Config.Base.Scheme + "://" + CommonApp.Config.Base.DomainName + "/" + CommonApp.Config.Base.LogoDir.Replace("\\", "/") + "/" + filename + "?" + Guid.NewGuid().ToString("N");
                if (onsuccess != null)
                {
                    onsuccess.ExecuteAsync(newfilepath);
                }
            }
            catch (Exception e)
            {
                if (onfail != null)
                {
                    onfail.ExecuteAsync(e.Message);
                }
            }
        }
        public void printtip(string printname, string jsonText, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null)
        {
            try
            {
                string pattern = @"(\\[^bfrnt\\/'\""])";
                jsonText = Regex.Replace(jsonText, pattern, "\\$1");
                var array = JArray.Parse(jsonText);
                foreach (var obj in array)  //查找某个字段与值
                {
                    var printer = PrinterFactory.GetPrinter_70(printname);
                    printer.Print(obj);
                }

                if (onsuccess != null)
                {
                    onsuccess.ExecuteAsync("printtip success");
                }
            }
            catch (Exception e)
            {
                if (onfail != null)
                {
                    onfail.ExecuteAsync(e.Message);
                }
            }
        }
        public void PrintLabel(string printname, string jsonText, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null)
        {
            try
            {
                Log.WriterNormalLog(printname + ":" + jsonText);
                PrintLabelModel printdata = JsonConvert.DeserializeObject<PrintLabelModel>(string.Format("{{items:{0}}}", jsonText));
                if (printdata == null || printdata.items == null || printdata.items.Count == 0)
                {
                    if (onfail != null)
                    {
                        onfail.ExecuteAsync("data is null");
                    }
                }
                printdata.printname = printname;
                PrintHelper.PrintLabel(printdata);

                if (onsuccess != null)
                {
                    onsuccess.ExecuteAsync("PrintLabel success");
                }
            }
            catch (Exception e)
            {
                if (onfail != null)
                {
                    onfail.ExecuteAsync(e.Message);
                }
            }
        }
        public void PrintLabel40(string printname, string jsonText, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null)
        {
            try
            {
                Log.WriterNormalLog(printname + ":" + jsonText);
                PrintLabelModel printdata = JsonConvert.DeserializeObject<PrintLabelModel>(string.Format("{{items:{0}}}", jsonText));
                if (printdata == null || printdata.items == null || printdata.items.Count == 0)
                {
                    if (onfail != null)
                    {
                        onfail.ExecuteAsync("data is null");
                    }
                }
                printdata.printname = printname;
                printdata.setModel_40();
                PrintHelper.PrintLabel(printdata);

                if (onsuccess != null)
                {
                    onsuccess.ExecuteAsync("PrintLabel success");
                }

            }
            catch (Exception e)
            {
                if (onfail != null)
                {
                    onfail.ExecuteAsync(e.Message);
                }
            }
        }
        public void printLabel_new(object jsonText, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null)
        {
            try
            {
                string jsonStr = JsonConvert.SerializeObject(jsonText);
                Log.WriterNormalLog(jsonStr);
                PrintLabelModel printdata = JsonConvert.DeserializeObject<PrintLabelModel>(jsonStr);
                PrintHelper.PrintLabel_new(printdata);

                if (onsuccess != null)
                {
                    onsuccess.ExecuteAsync("PrintLabel success");
                }

            }
            catch (Exception e)
            {
                if (onfail != null)
                {
                    onfail.ExecuteAsync(e.Message);
                }
            }
        }

        /// <summary>
        /// 以毫米为单位打印条码
        /// </summary>
        /// <param name="jsonText"></param>
        /// <param name="onsuccess"></param>
        /// <param name="onfail"></param>
        public void PrintLabelAndBarcodeInMM(object jsonText, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null)
        {
            try
            {
                string jsonStr = JsonConvert.SerializeObject(jsonText);
                Log.WriterNormalLog(jsonStr);
                PrintLabelModel printdata = JsonConvert.DeserializeObject<PrintLabelModel>(jsonStr);
                PrintHelper.PrintLabelAndBarcodeInMM(printdata);

                if (onsuccess != null)
                {
                    onsuccess.ExecuteAsync("PrintLabel success");
                }

            }
            catch (Exception e)
            {
                if (onfail != null)
                {
                    onfail.ExecuteAsync(e.Message);
                }
            }
        }

        public void PrintPOS(object jsonText, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null)
        {
            try
            {
                string LogoDir = Path.Combine(CommonApp.Directory, CommonApp.Config.Base.LogoDir);
                PrinterNomalFactory.PrintCommon(jsonText, LogoDir);
                if (onsuccess != null)
                {
                    onsuccess.ExecuteAsync("PrintPOS success");
                }
            }
            catch (Exception e)
            {
                if (onfail != null)
                {
                    onfail.ExecuteAsync(e.Message);
                }
            }
        }

        private static readonly ZGPrintTool _printerTool = new ZGPrintTool();

        public void OpenCashBox(string printname, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null)
        {
            try
            {
                _printerTool.OpenDrawer(printname);

                if (onsuccess != null)
                {
                    onsuccess.ExecuteAsync("OpenCashBox success");
                }
            }
            catch (Exception e)
            {
                if (onfail != null)
                {
                    onfail.ExecuteAsync(e.Message);
                }
            }
        }
        /// <param name="_spPortName">端口名称（COM1,COM2，COM3...）</param>
        /// <param name="_spBaudRate">通信波特率（2400,9600....）</param>
        /// <param name="_spStopBits">停止位</param>
        /// <param name="_spDataBits">数据位</param>
        public void OpenCashBox_separate(string _spPortName, int _spBaudRate, int _spDataBits,
            IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null, StopBits _spStopBits = StopBits.One)
        {
            try
            {
                SerialPort serialPort = new SerialPort();
                serialPort.PortName = _spPortName;
                serialPort.BaudRate = _spBaudRate;
                serialPort.StopBits = _spStopBits;
                serialPort.DataBits = _spDataBits;
                serialPort.Open();

                ////方法1
                ////ESC P NULL E E (ASCII码对照表)
                //byte[] byteA = { 0x1B, 0x70, 0x00, 0x45, 0x45 };
                //serialPort.Write(byteA, 0, byteA.Length);
                //System.Threading.Thread.Sleep(100);

                //方法2
                string str = ((char)027).ToString() + ((char)112).ToString()
                    + ((char)0).ToString() + ((char)069).ToString() + ((char)069).ToString();
                serialPort.WriteLine(str);

                serialPort.Close();

                if (onsuccess != null)
                {
                    onsuccess.ExecuteAsync("OpenCashBox success");
                }
            }
            catch (Exception e)
            {
                if (onfail != null)
                {
                    onfail.ExecuteAsync(e.Message);
                }
            }
        }
        /*获取打印机列表**/
        public string ListPrinters()
        {
            return CommonApp.Printers;
        }

        /// <summary>
        /// 获取打印机列表，Key value类型
        /// </summary>
        /// <returns></returns>
        public string ListPrintersKV()
        {
            var kvItems = CommonApp.Printers.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).
                Select(i => new KeyValueItem(i, i))?.ToList() ?? new List<KeyValueItem>();

            var usbPrinterList = new ZgPrinterBrowserFun().GetUsbPrinterList();
            kvItems.AddRange(usbPrinterList);
            return JsonConvert.SerializeObject(kvItems);
        }

        #endregion
        #region //副屏 广告
        public string getAdvert(string isdefault)
        {
            try
            {
                List<ADItem> list = new List<ADItem>();
                List<ADItem> ADItems = zgAdvert.AdvertFactory.ADItems_3;
                if (isdefault != null && ADItems != null && ADItems.Count > 0)
                {
                    list = ADItems.Where(item => item.IsDefault == int.Parse(isdefault)).ToList();
                }
                return JsonConvert.SerializeObject(list);
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }
            return "[]";
        }
        public string getAd_news()
        {
            try
            {
                return JsonConvert.SerializeObject(zgAdvert.AdvertFactory.ADItems_1);
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }
            return "[]";
        }
        public void getPoster(IJavascriptCallback onsuccess = null)
        {
            try
            {
                if (null != onsuccess)
                {
                    List<Poster> posters = zgAdvert.AdvertFactory.getPoster();
                    string rsultt = JsonConvert.SerializeObject(posters);
                    onsuccess.ExecuteAsync(rsultt);
                }
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }

        }
        public async void reloadAd(bool isShowAd)
        {
            try
            {
                if (App.browserBackObj == null || App.browserBackObj.browser == null) { return; }
                await Task.Run(() =>
                {
                    App.browserBackObj.reloadAdvertData(isShowAd);
                });
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }
        }
        public async void reloadScreen2(object data)
        {
            try
            {
                if (App.browserBackObj == null || App.browserBackObj.browser == null) { return; }
                await Task.Run(() =>
                {
                    App.browserBackObj.Reload(data);
                });
                //await App.browserBackObj.browser.GetBrowser().MainFrame.EvaluateScriptAsync(script);
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }
        }
        #endregion
        private void Rs232_ExecuteResponseAsync(string data)
        {
            System.Threading.Thread.Sleep(1);
            sendScale(data);

        }
        public async void sendScale(string data)
        {
            try
            {
                if (_rs232.Changed(data))
                {
                    string script = string.Format(_rs232.Script, data);

                    JavascriptResponse response = await App.browserShowObj.browser.GetBrowser().MainFrame.EvaluateScriptAsync(script);

                }
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="param">{exec:"open",port:"com1",baudrate:"9600",databits:"8",parity:"0",stopBits:"1",script:"document.getElementById('').value={0}"}</param>
        public void execScale(object param)
        {
            try
            {
                if (param == null)
                {
                    if (_rs232 != null)
                    {
                        _rs232.ClosePort();
                        _rs232 = null;
                    }
                    return;
                }

                string jsonstr = JsonConvert.SerializeObject(param);

                JObject data = JObject.Parse(jsonstr);
                if (data["exec"].ToString() == @"open")
                {
                    if (_rs232 != null)
                    {
                        _rs232.ClosePort();
                        _rs232 = null;
                    }
                    var port = Convert.ToString(data["port"].ToString());
                    var baudrate = Convert.ToInt32(data["baudrate"].ToString());
                    var databits = Convert.ToInt32(data["databits"].ToString());
                    var parity = (Parity)int.Parse(data["parity"].ToString());
                    var stopBits = (StopBits)int.Parse(data["stopBits"].ToString());
                    var scaleName = data["scaleName"].ToString();
                    _rs232 = new RS232(Rs232_ExecuteResponseAsync, port, baudrate, databits, parity, stopBits, scaleName);
                    _rs232.Script = data["script"].ToString();
                }
                else
                {
                    _rs232.ClosePort();
                    _rs232 = null;

                }
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }
        }
        public string getScaleList()
        {
            string Contentjson = string.Empty;
            try
            {
                Contentjson = JsonConvert.SerializeObject(ElectronicScaleHelper.GetAllElectronicScale());

            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }

            return Contentjson;
        }
        public string[] getPortNames()
        {
            return SerialPort.GetPortNames();
        }
        #region 客显
        /// <summary>  
        ///客显
        /// </summary>  
        /// <param name="param">{displaydata:"3.40",displaytype:3,port:"COM2",baudrate:"2400",databits:"8",stopBits:"1"}</param>
        /// displaytype 0 清屏(Clear) 1 单价(Price)  2 总计(Total) 3 收款(Recive) 4 找零(Change)
        public void customerdisplay(object param, IJavascriptCallback funsuccess = null, IJavascriptCallback funfail = null)
        {

            try
            {
                if (!CommonApp.hasCustomerDisplay)
                {
                    if (funfail != null)
                    {
                        funfail.ExecuteAsync("客显设备异常");
                    }
                    return;
                }
                string jsonstr = JsonConvert.SerializeObject(param);
                JObject data = JObject.Parse(jsonstr);

                var port = Convert.ToString(data["port"].ToString());
                var baudrate = Convert.ToInt32(data["baudrate"].ToString());
                var databits = Convert.ToInt32(data["databits"].ToString());
                var stopBits = data["stopBits"].ToString();

                CustomerDisplay display = new CustomerDisplay(port, baudrate, stopBits, databits);

                if (data["displaytype"] != null)
                {
                    display.DispiayType = (CustomerDispiayType)Convert.ToInt32(data["displaytype"].ToString());
                }
                var displaydata = "";
                if (data["displaydata"] != null)
                {
                    displaydata = data["displaydata"].ToString();
                }

                display.DisplayData(displaydata);
                if (funsuccess != null)
                {
                    funsuccess.ExecuteAsync("success");
                }
            }
            catch (Exception e)
            {
                if (funfail != null)
                {
                    funfail.ExecuteAsync(e.Message);
                }
            }
        }
        #endregion

        /// <summary>
        /// useCardReader：1-开始读卡，其它-停止读卡
        /// </summary>
        /// <param name="useCardReader"></param>
        /// <param name="onsuccess"></param>
        /// <param name="onfail"></param>
        public void SetVipCardReader(int useCardReader, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null)
        {
            try
            {
                if (!useCardReader.Equals(CommonApp.settings.setting.useCardReader))
                {
                    CommonApp.settings.setting.useCardReader = useCardReader.ToString();
                    SQLiteHelper.SetSettingByKey("useCardReader", useCardReader.ToString(), "是否读卡：0-不读，1-读");
                }
                ReadCard.OpenClose((object sender, EventArgs e) =>
                {
                    Log.WriterNormalLog("code:" + sender);
                    App.browserShowObj.RunJS("demo.$store.commit('SET_SHOW',{cardNo: '" + sender + "'})");
                }, useCardReader.ToString());

                if (null != onsuccess)
                {
                    onsuccess.ExecuteAsync(1 == useCardReader ? "SetVipCardReader_Start success" : "SetVipCardReader_Stop success");
                }
            }
            catch (Exception e)
            {
                if (null != onfail)
                {
                    onfail.ExecuteAsync(e.Message);
                }
            }
        }


        /// <summary>
        /// 播放语音
        /// </summary>
        /// <param name="voice"></param>
        /// <param name="onfail"></param>
        public void PlayVoice(string voice, float volume = 1, IJavascriptCallback onfail = null)
        {
            try
            {
                if (synth == null)
                {
                    synth = new SpeechSynthesizer();
                    synth.SetOutputToDefaultAudioDevice();
                    synth.Volume = 100;
                }

                if (iv == null)
                {
                    foreach (InstalledVoice item in synth.GetInstalledVoices())
                    {
                        if (item.Enabled && "zh-cn".Equals(item.VoiceInfo.Culture.Name.ToLower()))
                        {
                            iv = item;

                            break;
                        }
                    }
                    synth.SelectVoice(iv.VoiceInfo.Name);
                }

                Task.Run(() =>
                {
                    MemoryStream waveStream = new MemoryStream();
                    synth.SetOutputToWaveStream(waveStream);
                    synth.Speak(voice);
                    waveStream.Flush();
                    waveStream.Position = 0;
                    waveout = new WaveOut();
                    WaveFileReader waveReader = new WaveFileReader(waveStream);
                    var volumeSampleProvider = new VolumeSampleProvider(waveReader.ToSampleProvider());
                    volumeSampleProvider.Volume = volume;
                    waveout.Init(volumeSampleProvider);
                    waveout.Play();

                });
            }
            catch (Exception e)
            {
                if (null != onfail)
                {
                    onfail.ExecuteAsync(e.Message);
                }
                Log.WriterExceptionLog(e.Message);
            }
        }

        public void closeSecondScreen()
        {
            try
            {
                if (App.browserBackObj != null) App.browserBackObj.CloseAll();
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }

        }
        /// <summary>
        /// 更新setting
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public string updateSetting(string sql, IJavascriptCallback successback = null, IJavascriptCallback errorBack = null)
        {

            string results;
            try
            {
                results = SQLiteHelper.ExecuteReader(sql);
                if (successback != null)
                {
                    Task.Factory.StartNew(async () =>
                    {
                        using (successback)
                        {
                            await successback.ExecuteAsync(results, CommonApp.UpdateSetinng());
                            if (App.browserBackObj != null) App.browserBackObj.Reload_FromUpSetting();
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                results = "{\"code\":\"" + ex.HResult + "\",\"message\":\"" + ex.Message + "\"}";
                results = results.Replace("\r\n", "");
                if (errorBack != null)
                {
                    Task.Factory.StartNew(async () =>
                    {
                        using (errorBack)
                        {
                            await errorBack.ExecuteAsync(results);
                        }
                    });
                }

                throw ex;
            }
            return results;
        }

        private class ResClearData
        {
            public String schema { get; set; }
            public int partitionId { get; set; }
            public String sysUid { get; set; }
            public int sysSid { get; set; }
        }
    }
}