﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{43DC1278-5EFF-4E9D-9370-2A94AECC84C7}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>TreChina.Printer</RootNamespace>
    <AssemblyName>TreChina.Printer</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\out\Programs\Libs\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\out\Programs\Libs\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Printing" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="zxing" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Commands\Alignment.cs" />
    <Compile Include="Commands\BarCode.cs" />
    <Compile Include="Commands\Drawer.cs" />
    <Compile Include="Commands\EscPos.cs" />
    <Compile Include="Commands\FontHeight.cs" />
    <Compile Include="Commands\FontMode.cs" />
    <Compile Include="Commands\FontWidth.cs" />
    <Compile Include="Commands\Image.cs" />
    <Compile Include="Commands\InitializePrint.cs" />
    <Compile Include="Commands\LineHeight.cs" />
    <Compile Include="Commands\PaperCut.cs" />
    <Compile Include="Commands\QrCode.cs" />
    <Compile Include="Enums\Fonts.cs" />
    <Compile Include="Enums\Justifications.cs" />
    <Compile Include="Enums\Positions.cs" />
    <Compile Include="Enums\PrinterModeState.cs" />
    <Compile Include="Enums\QrCodeSize.cs" />
    <Compile Include="Extensions\PrinterExtensions.cs" />
    <Compile Include="Helper\BarCodeHelper.cs" />
    <Compile Include="Helper\BitmapScaleHelper.cs" />
    <Compile Include="Helper\PrintHelper.cs" />
    <Compile Include="Helper\PrintQueueHelper.cs" />
    <Compile Include="Helper\RawPrinterHelper.cs" />
    <Compile Include="Interfaces\Command\IAlignment.cs" />
    <Compile Include="Interfaces\Command\IBarCode.cs" />
    <Compile Include="Interfaces\Command\IDrawer.cs" />
    <Compile Include="Interfaces\Command\IFontHeight.cs" />
    <Compile Include="Interfaces\Command\IFontMode.cs" />
    <Compile Include="Interfaces\Command\IFontWidth.cs" />
    <Compile Include="Interfaces\Command\IImage.cs" />
    <Compile Include="Interfaces\Command\IInitializePrint.cs" />
    <Compile Include="Interfaces\Command\ILineHeight.cs" />
    <Compile Include="Interfaces\Command\IPaperCut.cs" />
    <Compile Include="Interfaces\Command\IPrintCommand.cs" />
    <Compile Include="Interfaces\Command\IQrCode.cs" />
    <Compile Include="Interfaces\Printer\IPrinter.cs" />
    <Compile Include="Internal\Printer.cs" />
    <Compile Include="Internal\Printer_70.cs" />
    <Compile Include="IPrinter.cs" />
    <Compile Include="Models\FontSize.cs" />
    <Compile Include="Models\paperWidth.cs" />
    <Compile Include="Models\PrintLabelModel.cs" />
    <Compile Include="POSPrinter.cs" />
    <Compile Include="PrinterFactory.cs" />
    <Compile Include="PrintNomalClass.cs" />
    <Compile Include="PrintOnlineClass.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>