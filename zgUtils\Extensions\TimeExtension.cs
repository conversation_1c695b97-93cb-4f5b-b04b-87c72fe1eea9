﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgUtils.Extensions
{
    public static class TimeExtension
    {
        public static string GetTimestamp(this DateTime time)
        {
            return ((int)DateTime.UtcNow.Subtract(new DateTime(1970, 1, 1)).TotalSeconds).ToString();
        }

        public static int DateTimeToStamp(this DateTime time)
        {
            DateTime d = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
            return (int)(time - d).TotalSeconds;
        }

        public static long DataHubTimeToStamp(this DateTime time)
        {
            DateTime d = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
            return (long)(time - d).TotalMilliseconds * 1000L;
        }

        public static bool IsTimeout(this DateTime dt, double seconds)
        {
            return (DateTime.Now - dt).TotalSeconds >= seconds;
        }
    }
}
