﻿using zgSerialPort.ACS;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace zgSerialPort.Common
{
    /// <summary>
    /// 电子秤帮助
    /// </summary>
    public class ElectronicScaleHelper
    {
        /// <summary>
        /// 获得所有 已发布的电子秤
        /// </summary>
        /// <returns></returns>
        public static Dictionary<string, string> GetAllElectronicScale()
        {
            Assembly ass = Assembly.GetAssembly(typeof(BaseACS));
            Type[] types = ass.GetTypes();
            var acsInfoList = new List<Tuple<int, string, string>>();

            foreach (var item in types)
            {
                object[] objAttrs = item.GetCustomAttributes(typeof(ACSAttribute), true);
                if (objAttrs.Length > 0)
                {
                    ACSAttribute attr = objAttrs[0] as ACSAttribute;
                    if (attr != null && attr.IsRelease)
                    {
                        var key = item.Name;
                        var name = $"{attr.ModelName}{attr.ModelNumber ?? string.Empty}";
                        acsInfoList.Add(new Tuple<int, string, string>(attr.Index, key, name));
                    }
                }
            }
            var kvName = acsInfoList.OrderBy(i => i.Item1).Select(i => new KeyValuePair<string, string>(i.Item2, i.Item3));
            var result = kvName.ToDictionary(x => x.Key, x => x.Value);
            return result;
        }
    }
}
