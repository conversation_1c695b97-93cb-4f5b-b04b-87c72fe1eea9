﻿using Microsoft.Win32;
using Microsoft.Win32.SafeHandles;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;

namespace zgPrinter
{
	public class SetupAPINativeMethods
	{
		public static readonly Guid UsbPrintInterfaceClassGuid = new Guid("28d78fad-5a12-11d1-ae5b-0000f803a8c2");

		/// <summary>
		/// 包含本机上所有被请求的设备信息的设备信息集句柄。
		/// </summary>
		/// <param name="ClassGuid"></param>
		/// <param name="Enumerator"></param>
		/// <param name="hwndParent"></param>
		/// <param name="Flags"></param>
		/// <param name="DeviceInfoSet"></param>
		/// <param name="MachineName"></param>
		/// <param name="Reserved"></param>
		/// <returns></returns>
		[DllImport("setupapi.dll", CharSet = CharSet.Auto)]
		public static extern IntPtr SetupDiGetClassDevsEx(ref Guid ClassGuid, [MarshalAs(UnmanagedType.LPTStr)] string Enumerator, IntPtr hwndParent, SetupAPINativeMethods.DIGCF Flags, IntPtr DeviceInfoSet, [MarshalAs(UnmanagedType.LPTStr)] string MachineName, IntPtr Reserved);

		[DllImport("setupapi.dll", SetLastError = true)]
		public static extern IntPtr SetupDiCreateDeviceInfoList(ref Guid ClassGuid, IntPtr hwndParent);

		[DllImport("setupapi.dll", SetLastError = true)]
		public static extern IntPtr SetupDiCreateDeviceInfoList(IntPtr ClassGuid, IntPtr hwndParent);

		/// <summary>
		/// 销毁一个设备信息集合，并且释放所有关联的内存
		/// </summary>
		/// <param name="DeviceInfoSet"></param>
		/// <returns></returns>
		[DllImport("setupapi.dll", SetLastError = true)]
		public static extern bool SetupDiDestroyDeviceInfoList(IntPtr DeviceInfoSet);

		/// <summary>
		/// 枚举设备信息集中的全部接口
		/// </summary>
		/// <param name="hDevInfo"></param>
		/// <param name="devInfo"></param>
		/// <param name="interfaceClassGuid"></param>
		/// <param name="memberIndex"></param>
		/// <param name="deviceInterfaceData"></param>
		/// <returns></returns>
		[DllImport("setupapi.dll", CharSet = CharSet.Auto, SetLastError = true)]
		public static extern bool SetupDiEnumDeviceInterfaces(IntPtr hDevInfo, [MarshalAs(UnmanagedType.AsAny)] object devInfo, ref Guid interfaceClassGuid, uint memberIndex, ref SetupAPINativeMethods.SP_DEVICE_INTERFACE_DATA deviceInterfaceData);

		/// <summary>
		/// 返回设备接口的详细信息
		/// </summary>
		/// <param name="hDevInfo"></param>
		/// <param name="deviceInterfaceData"></param>
		/// <param name="deviceInterfaceDetailData"></param>
		/// <param name="deviceInterfaceDetailDataSize"></param>
		/// <param name="requiredSize"></param>
		/// <param name="deviceInfoData"></param>
		/// <returns></returns>
		[DllImport("setupapi.dll", CharSet = CharSet.Auto, SetLastError = true)]
		public static extern bool SetupDiGetDeviceInterfaceDetail(IntPtr hDevInfo, ref SetupAPINativeMethods.SP_DEVICE_INTERFACE_DATA deviceInterfaceData, ref SetupAPINativeMethods.SP_DEVICE_INTERFACE_DETAIL_DATA deviceInterfaceDetailData, int deviceInterfaceDetailDataSize, IntPtr requiredSize, [MarshalAs(UnmanagedType.AsAny)] object deviceInfoData);

		/// <summary>
		/// 创建SafeFileHandle
		/// </summary>
		/// <param name="filename"></param>
		/// <param name="fileaccess"></param>
		/// <param name="fileshare"></param>
		/// <param name="securityattributes"></param>
		/// <param name="creationdisposition"></param>
		/// <param name="flags"></param>
		/// <param name="template"></param>
		/// <returns></returns>
		[DllImport("kernel32.dll")]
		public static extern SafeFileHandle CreateFile(string filename, [MarshalAs(UnmanagedType.U4)] FileAccess fileaccess, [MarshalAs(UnmanagedType.U4)] FileShare fileshare, int securityattributes, [MarshalAs(UnmanagedType.U4)] FileMode creationdisposition, int flags, IntPtr template);


		/// <summary>
		/// 获得设备路径
		/// </summary>
		/// <param name="setupClassGuid"></param>
		/// <param name="interfaceClassGuid"></param>
		/// <param name="Enumerator"></param>
		/// <returns></returns>
		public static string[] GetDevicePath(Guid setupClassGuid, Guid interfaceClassGuid, string Enumerator = null)
		{
			string[] result;
			if (interfaceClassGuid == Guid.Empty)
			{
				result = null;
			}
			else
			{
				IntPtr intPtr;
				if (setupClassGuid == Guid.Empty)
				{
					intPtr = SetupAPINativeMethods.SetupDiCreateDeviceInfoList(IntPtr.Zero, IntPtr.Zero);
				}
				else
				{
					intPtr = SetupAPINativeMethods.SetupDiCreateDeviceInfoList(ref setupClassGuid, IntPtr.Zero);
				}
				if (intPtr == new IntPtr(-1))
				{
					result = null;
				}
				else
				{
					IntPtr intPtr2 = SetupAPINativeMethods.SetupDiGetClassDevsEx(ref interfaceClassGuid, Enumerator, IntPtr.Zero, SetupAPINativeMethods.DIGCF.DIGCF_PRESENT | SetupAPINativeMethods.DIGCF.DIGCF_DEVICEINTERFACE, intPtr, null, IntPtr.Zero);
					if (intPtr2 == new IntPtr(-1))
					{
						result = null;
					}
					else
					{
						List<string> list = new List<string>();
						uint num = 0U;
						SetupAPINativeMethods.SP_DEVICE_INTERFACE_DATA empty = SetupAPINativeMethods.SP_DEVICE_INTERFACE_DATA.Empty;
						while (SetupAPINativeMethods.SetupDiEnumDeviceInterfaces(intPtr2, null, ref interfaceClassGuid, num++, ref empty))
						{
							SetupAPINativeMethods.SP_DEVICE_INTERFACE_DETAIL_DATA sp_DEVICE_INTERFACE_DETAIL_DATA = default(SetupAPINativeMethods.SP_DEVICE_INTERFACE_DETAIL_DATA);
							sp_DEVICE_INTERFACE_DETAIL_DATA.cbSize = (uint)((IntPtr.Size == 4) ? (4 + Marshal.SystemDefaultCharSize) : 8);
							bool flag5 = SetupAPINativeMethods.SetupDiGetDeviceInterfaceDetail(intPtr2, ref empty, ref sp_DEVICE_INTERFACE_DETAIL_DATA, Marshal.SizeOf(sp_DEVICE_INTERFACE_DETAIL_DATA), IntPtr.Zero, null);
							if (flag5)
							{
								list.Add(sp_DEVICE_INTERFACE_DETAIL_DATA.DevicePath);
							}
						}
						SetupAPINativeMethods.SetupDiDestroyDeviceInfoList(intPtr2);
						result = list.Count == 0 ? null : list.ToArray();
					}
				}
			}
			return result;
		}

		[DllImport("gdi32.dll")]
        static extern int GetDeviceCaps(IntPtr hdc,int nIndex);

		public static bool SendBytesToPrinter(string devicePath, byte[] bytes)
		{
			bool result;
			try
			{
				using (SafeFileHandle safeFileHandle = SetupAPINativeMethods.CreateFile(devicePath, FileAccess.ReadWrite, FileShare.ReadWrite, 0, FileMode.OpenOrCreate, 0, (IntPtr)null))
				{
					try
					{
						using (FileStream fileStream = new FileStream(safeFileHandle, FileAccess.Write))
						{
							fileStream.Write(bytes, 0, bytes.Length);
							fileStream.Close();
						}
					}
					finally
					{
						safeFileHandle.Close();
					}
				}
				result = true;
			}
			catch (Exception ex)
			{
				result = false;
			}
			return result;
		}

        //public static float GetScreenScalingFactor()
        //{
        //    var g = Graphics.FromHwnd(IntPtr.Zero);
        //    IntPtr desktop = g.GetHdc();
        //    var physicalScreenHeight = GetDeviceCaps(desktop, (int)DeviceCap.DESKTOPVERTRES);

        //    var screenScalingFactor =
        //        (float)physicalScreenHeight / Screen.PrimaryScreen.Bounds.Height;
        //    //SystemParameters.PrimaryScreenHeight;

        //    return screenScalingFactor;
        //}

        public static Dictionary<string, string> GetUSBRegistryList()
		{
			Dictionary<string, string> dictionary = new Dictionary<string, string>();
			RegistryKey registryKey = Registry.LocalMachine.OpenSubKey("SYSTEM\\CurrentControlSet\\Enum\\USBPRINT");
			bool flag = registryKey != null;
			if (flag)
			{
				foreach (string text in registryKey.GetSubKeyNames())
				{
					RegistryKey registryKey2 = registryKey.OpenSubKey(text);
					string[] subKeyNames2 = registryKey2.GetSubKeyNames();
					bool flag2 = subKeyNames2 != null && subKeyNames2.Any<string>();
					if (flag2)
					{
						string key = string.Join("|", subKeyNames2);
						bool flag3 = !dictionary.ContainsKey(key);
						if (flag3)
						{
							dictionary.Add(key, text);
						}
					}
				}
			}
			return dictionary;
		}


		[Flags]
		public enum DIGCF
		{
			//对于指定的设备接口类，只返回与系统默认设备接口相关联的设备（如果已设置的话）。
			DIGCF_DEFAULT = 1,

			//只返回当前系统中存在的（已连接）设备
			DIGCF_PRESENT = 2,

			//返回所有已安装设备的列表或所有设备接口类。
			DIGCF_ALLCLASSES = 4,

			//只返回当前硬件列表中的一部分设备。
			DIGCF_PROFILE = 8,

			//返回支持指定设备接口类的设备。如果Enumerators参数制定了设备的实例ID，那么必须在Flags参数中设置此标志位。
			DIGCF_DEVICEINTERFACE = 16
		}

		[StructLayout(LayoutKind.Sequential, CharSet = CharSet.Auto)]
		public struct SP_DEVICE_INTERFACE_DETAIL_DATA
		{
			public uint cbSize;

			[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
			public string DevicePath;
		}

		public struct SP_DEVICE_INTERFACE_DATA
		{
			public SP_DEVICE_INTERFACE_DATA(int size)
			{
				this.cbSize = (uint)size;
				this.InterfaceClassGuid = Guid.Empty;
				this.Flags = 0U;
				this.Reserved = UIntPtr.Zero;
			}

			public static readonly SetupAPINativeMethods.SP_DEVICE_INTERFACE_DATA Empty = new SetupAPINativeMethods.SP_DEVICE_INTERFACE_DATA(Marshal.SizeOf(typeof(SetupAPINativeMethods.SP_DEVICE_INTERFACE_DATA)));

			public uint cbSize;

			public Guid InterfaceClassGuid;

			public uint Flags;

			public UIntPtr Reserved;
		}

		///// <summary>
		///// 毫米转像素
		///// </summary>
		///// <param name="mm"></param>
		///// <returns></returns>
  //      public static float MM2PX(float mm)
  //      {
  //          var scalingFactor = PrimaryScreen.GetScreenScalingFactor();
  //          Graphics graphics = Graphics.FromHwnd(IntPtr.Zero);
  //          float dpiX = graphics.DpiX;
  //          float inch = mm / 25.4f;
  //          float px = inch * dpiX / scalingFactor;
		//	return px;
  //      }
    }
}
