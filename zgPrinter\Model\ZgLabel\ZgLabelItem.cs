﻿using System.ComponentModel;
using System.Xml.Serialization;

namespace zgPrinter.Model.ZgLabel
{
    public class ZgLabelItem
    {
        [XmlAttribute("INDEX")]
        public int Index { get; set; }

        /// <summary>
        /// 元素值
        /// </summary>
        [XmlAttribute("TXT")]
        public string Text { get; set; }

        /// <summary>
        /// 元素类型：text-文本，barcode-条码
        /// </summary>
        public string Type { get; set; } = "text";

        /// <summary>
        /// 文字缩放比例
        /// </summary>
        [XmlAttribute("FSIZE"), DefaultValue("11")]
        public string FontSize { get; set; } = "11";

        [XmlAttribute("X")]
        public float X { get; set; }

        [XmlAttribute("Y")]
        public float Y { get; set; }

        [XmlAttribute("DISPLAY"), DefaultValue(true)]
        public bool Display { get; set; } = true;

        [XmlAttribute("BIND"), DefaultValue("")]
        public string DataMember { get; set; }
    }
}
