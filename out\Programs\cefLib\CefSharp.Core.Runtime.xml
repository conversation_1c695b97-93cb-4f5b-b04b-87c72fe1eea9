<?xml version="1.0"?>
<doc>
    <assembly>
        "CefSharp.Core.Runtime"
    </assembly>
    <members>
        <member name="P:CefSharp.Core.PostData.HasExcludedElements">
            <summary>
Returns true if the underlying POST data includes elements that are not
represented by this IPostData object (for example, multi-part file upload
data). Modifying IPostData objects with excluded elements may result in
the request failing.
</summary>
        </member>
        <member name="M:CefSharp.Core.PostData.CreatePostDataElement">
            <summary>
Create a new <see cref="T:CefSharp.IPostDataElement" /> instance
</summary>
            <returns>PostDataElement</returns>
        </member>
        <member name="M:CefSharp.Core.PostData.RemoveElements">
            <summary>
Remove all existing post data elements.
</summary>
        </member>
        <member name="M:CefSharp.Core.PostData.RemoveElement(CefSharp.IPostDataElement)">
            <summary>
Remove  the specified <see cref="T:CefSharp.IPostDataElement" />.
</summary>
            <param name="element">element to be removed.</param>
            <returns> Returns true if the add succeeds.</returns>
        </member>
        <member name="M:CefSharp.Core.PostData.AddElement(CefSharp.IPostDataElement)">
            <summary>
Add the specified <see cref="T:CefSharp.IPostDataElement" />.
</summary>
            <param name="element">element to be added.</param>
            <returns>Returns true if the add succeeds.</returns>
        </member>
        <member name="P:CefSharp.Core.PostData.Elements">
            <summary>
Retrieve the post data elements.
</summary>
        </member>
        <member name="P:CefSharp.Core.PostData.IsReadOnly">
            <summary>
Returns true if this object is read-only.
</summary>
        </member>
        <member name="M:CefSharp.Core.PostData.#ctor">
            <summary>
Default constructor.
</summary>
        </member>
        <member name="M:CefSharp.Core.PostData.ThrowIfReadOnly">
            <summary>
Throw exception if Readonly
</summary>
            <exception cref="T:System.Exception">Thrown when an exception error condition occurs.</exception>
        </member>
        <member name="M:CefSharp.Core.PostData.Dispose">
            <summary>
Destructor.
</summary>
        </member>
        <member name="M:CefSharp.Core.PostData.Finalize">
            <summary>
Finalizer.
</summary>
        </member>
        <member name="T:CefSharp.Core.PostData">
            <summary>
Form Post Data
</summary>
            <seealso cref="!:T:IPostData" />
        </member>
        <member name="M:CefSharp.Core.PopupFeatures.#ctor(CefStructBase&lt;CefPopupFeaturesTraits&gt;!System.Runtime.CompilerServices.IsConst*)">
            <summary>
Constructor.
</summary>
            <param name="popupFeatures">The popup features.</param>
        </member>
        <member name="T:CefSharp.Core.PopupFeatures">
            <summary>
Class representing popup window features.
</summary>
            <exclude />
        </member>
        <member name="T:CefSharp.Core.NativeMethodWrapper">
            <exclude />
        </member>
        <member name="M:CefSharp.Core.Cef.WaitForBrowsersToClose">
            <summary>
Helper method to ensure all ChromiumWebBrowser instances have been
closed/disposed, should be called before Cef.Shutdown.
Disposes all remaning ChromiumWebBrowser instances
then waits for CEF to release it's remaning CefBrowser instances.
Finally a small delay of 50ms to allow for CEF to finish it's cleanup.
Should only be called when MultiThreadedMessageLoop = true;
(Hasn't been tested when when CEF integrates into main message loop).
</summary>
        </member>
        <member name="M:CefSharp.Core.Cef.EnableWaitForBrowsersToClose">
            <summary>
WaitForBrowsersToClose is not enabled by default, call this method
before Cef.Initialize to enable. If you aren't calling Cef.Initialize
explicitly then this should be called before creating your first
ChromiumWebBrowser instance.
</summary>
        </member>
        <member name="M:CefSharp.Core.Cef.GetMimeType(System.String)">
            <summary>
Returns the mime type for the specified file extension or an empty string if unknown.
</summary>
            <param name="extension">file extension</param>
            <returns>Returns the mime type for the specified file extension or an empty string if unknown.</returns>
        </member>
        <member name="M:CefSharp.Core.Cef.RegisterWidevineCdmAsync(System.String)">
            <summary>
Register the Widevine CDM plugin.

See <see cref="M:CefSharp.Core.Cef.RegisterWidevineCdm(System.String,CefSharp.IRegisterCdmCallback)" /> for more details.
</summary>
            <param name="path"> is a directory that contains the Widevine CDM files</param>
            <returns>Returns a Task that can be awaited to receive the <see cref="T:CefSharp.CdmRegistration" /> response.</returns>
        </member>
        <member name="M:CefSharp.Core.Cef.RegisterWidevineCdm(System.String,CefSharp.IRegisterCdmCallback)">
            <summary>
Register the Widevine CDM plugin.

The client application is responsible for downloading an appropriate
platform-specific CDM binary distribution from Google, extracting the
contents, and building the required directory structure on the local machine.
The <see cref="M:CefSharp.IBrowserHost.StartDownload(System.String)" /> method class can be used
to implement this functionality in CefSharp. Contact Google via
https://www.widevine.com/contact.html for details on CDM download.


path is a directory that must contain the following files:
  1. manifest.json file from the CDM binary distribution (see below).
  2. widevinecdm file from the CDM binary distribution (e.g.
     widevinecdm.dll on Windows).
  3. widevidecdmadapter file from the CEF binary distribution (e.g.
     widevinecdmadapter.dll on Windows).

If any of these files are missing or if the manifest file has incorrect
contents the registration will fail and callback will receive an ErrorCode
value of <see cref="F:CefSharp.CdmRegistrationErrorCode.IncorrectContents" />.

The manifest.json file must contain the following keys:
  A. "os": Supported OS (e.g. "mac", "win" or "linux").
  B. "arch": Supported architecture (e.g. "ia32" or "x64").
  C. "x-cdm-module-versions": Module API version (e.g. "4").
  D. "x-cdm-interface-versions": Interface API version (e.g. "8").
  E. "x-cdm-host-versions": Host API version (e.g. "8").
  F. "version": CDM version (e.g. "1.4.8.903").
  G. "x-cdm-codecs": List of supported codecs (e.g. "vp8,vp9.0,avc1").

A through E are used to verify compatibility with the current Chromium
version. If the CDM is not compatible the registration will fail and
callback will receive an ErrorCode value of <see cref="F:CefSharp.CdmRegistrationErrorCode.Incompatible" />.

If registration is not supported at the time that Cef.RegisterWidevineCdm() is called then callback
will receive an ErrorCode value of <see cref="F:CefSharp.CdmRegistrationErrorCode.NotSupported" />.
</summary>
            <param name="path"> is a directory that contains the Widevine CDM files</param>
            <param name="callback">optional callback - <see cref="M:CefSharp.IRegisterCdmCallback.OnRegistrationComplete(CefSharp.CdmRegistration)" /> 
will be executed asynchronously once registration is complete</param>
        </member>
        <member name="M:CefSharp.Core.Cef.SetCrashKeyValue(System.String,System.String)">
            <summary>
Sets or clears a specific key-value pair from the crash metadata.
</summary>
        </member>
        <member name="P:CefSharp.Core.Cef.CrashReportingEnabled">
            <summary>
Crash reporting is configured using an INI-style config file named
crash_reporter.cfg. This file must be placed next to
the main application executable. File contents are as follows:

 # Comments start with a hash character and must be on their own line.

 [Config]
 ProductName=&lt;Value of the "prod" crash key; defaults to "cef"&gt;
 ProductVersion=&lt;Value of the "ver" crash key; defaults to the CEF version&gt;
 AppName=&lt;Windows only; App-specific folder name component for storing crash
          information; default to "CEF"&gt;
 ExternalHandler=&lt;Windows only; Name of the external handler exe to use
                  instead of re-launching the main exe; default to empty&gt;
 ServerURL=&lt;crash server URL; default to empty&gt;
 RateLimitEnabled=&lt;True if uploads should be rate limited; default to true&gt;
 MaxUploadsPerDay=&lt;Max uploads per 24 hours, used if rate limit is enabled;
                   default to 5&gt;
 MaxDatabaseSizeInMb=&lt;Total crash report disk usage greater than this value
                      will cause older reports to be deleted; default to 20&gt;
 MaxDatabaseAgeInDays=&lt;Crash reports older than this value will be deleted;
                       default to 5&gt;

 [CrashKeys]
 my_key1=&lt;small|medium|large&gt;
 my_key2=&lt;small|medium|large&gt;

Config section:

If "ProductName" and/or "ProductVersion" are set then the specified values
will be included in the crash dump metadata. 

If "AppName" is set on Windows then crash report information (metrics,
database and dumps) will be stored locally on disk under the
"C:\Users\<USER>\AppData\Local\[AppName]\User Data" folder. 

If "ExternalHandler" is set on Windows then the specified exe will be
launched as the crashpad-handler instead of re-launching the main process
exe. The value can be an absolute path or a path relative to the main exe
directory. 

If "ServerURL" is set then crashes will be uploaded as a multi-part POST
request to the specified URL. Otherwise, reports will only be stored locally
on disk.

If "RateLimitEnabled" is set to true then crash report uploads will be rate
limited as follows:
 1. If "MaxUploadsPerDay" is set to a positive value then at most the
    specified number of crashes will be uploaded in each 24 hour period.
 2. If crash upload fails due to a network or server error then an
    incremental backoff delay up to a maximum of 24 hours will be applied for
    retries.
 3. If a backoff delay is applied and "MaxUploadsPerDay" is &gt; 1 then the
    "MaxUploadsPerDay" value will be reduced to 1 until the client is
    restarted. This helps to avoid an upload flood when the network or
    server error is resolved.

If "MaxDatabaseSizeInMb" is set to a positive value then crash report storage
on disk will be limited to that size in megabytes. For example, on Windows
each dump is about 600KB so a "MaxDatabaseSizeInMb" value of 20 equates to
about 34 crash reports stored on disk.

If "MaxDatabaseAgeInDays" is set to a positive value then crash reports older
than the specified age in days will be deleted.

CrashKeys section:

Any number of crash keys can be specified for use by the application. Crash
key values will be truncated based on the specified size (small = 63 bytes,
medium = 252 bytes, large = 1008 bytes). The value of crash keys can be set
from any thread or process using the Cef.SetCrashKeyValue function. These
key/value pairs will be sent to the crash server along with the crash dump
file. Medium and large values will be chunked for submission. For example,
if your key is named "mykey" then the value will be broken into ordered
chunks and submitted using keys named "mykey-1", "mykey-2", etc.
</summary>
            <returns>Returns true if crash reporting is enabled.</returns>
        </member>
        <member name="M:CefSharp.Core.Cef.ColorSetARGB(System.UInt32,System.UInt32,System.UInt32,System.UInt32)">
            <summary>
Helper function (wrapper around the CefColorSetARGB macro) which combines
the 4 color components into an uint32 for use with BackgroundColor property
</summary>
            <param name="a">Alpha</param>
            <param name="r">Red</param>
            <param name="g">Green</param>
            <param name="b">Blue</param>
            <returns>Returns the color.</returns>
        </member>
        <member name="M:CefSharp.Core.Cef.GetGlobalRequestContext">
            <summary>
Gets the Global Request Context. Make sure to Dispose of this object when finished.
The earlier possible place to access the IRequestContext is in IBrowserProcessHandler.OnContextInitialized.
Alternative use the ChromiumWebBrowser BrowserInitialized (OffScreen) or IsBrowserInitializedChanged (WinForms/WPF) events.
</summary>
            <returns>Returns the global request context or null if the RequestContext has not been initialized yet.</returns>
        </member>
        <member name="M:CefSharp.Core.Cef.CurrentlyOnThread(CefSharp.CefThreadIds)">
            <summary>
Returns true if called on the specified CEF thread.
</summary>
            <returns>Returns true if called on the specified thread.</returns>
        </member>
        <member name="M:CefSharp.Core.Cef.EnableHighDPISupport">
            <summary>
Call during process startup to enable High-DPI support on Windows 7 or newer.
Older versions of Windows should be left DPI-unaware because they do not
support DirectWrite and GDI fonts are kerned very badly.
</summary>
        </member>
        <member name="M:CefSharp.Core.Cef.UnregisterInternalWebPlugin(System.String)">
            <summary>
Unregister an internal plugin. This may be undone the next time RefreshWebPlugins() is called. 
</summary>
            <param name="path">Path (directory + file).</param>
        </member>
        <member name="M:CefSharp.Core.Cef.RefreshWebPlugins">
            <summary>
Cause the plugin list to refresh the next time it is accessed regardless of whether it has already been loaded.
</summary>
        </member>
        <member name="M:CefSharp.Core.Cef.GetPlugins">
            <summary>
Async returns a list containing Plugin Information
(Wrapper around CefVisitWebPluginInfo)
</summary>
            <returns>Returns List of <see cref="T:CefSharp.WebPluginInfo" /> structs.</returns>
        </member>
        <member name="M:CefSharp.Core.Cef.VisitWebPluginInfo(CefSharp.IWebPluginInfoVisitor)">
            <summary>
Visit web plugin information. Can be called on any thread in the browser process.
</summary>
        </member>
        <member name="M:CefSharp.Core.Cef.ClearSchemeHandlerFactories">
            <summary>
Clear all scheme handler factories registered with the global request context.
Returns false on error. This function may be called on any thread in the browser process.
Using this function is equivalent to calling Cef.GetGlobalRequestContext().ClearSchemeHandlerFactories().
</summary>
            <returns>Returns false on error.</returns>
        </member>
        <member name="M:CefSharp.Core.Cef.ShutdownWithoutChecks">
            <summary>
This method should only be used by advanced users, if you're unsure then use Cef.Shutdown().
This function should be called on the main application thread to shut down
the CEF browser process before the application exits. This method simply obtains a lock
and calls the native CefShutdown method, only IsInitialized is checked. All ChromiumWebBrowser
instances MUST be Disposed of before calling this method. If calling this method results in a crash
or hangs then you're likely hanging on to some unmanaged resources or haven't closed all of your browser
instances
</summary>
        </member>
        <member name="M:CefSharp.Core.Cef.Shutdown">
            <summary>
Shuts down CefSharp and the underlying CEF infrastructure. This method is safe to call multiple times; it will only
shut down CEF on the first call (all subsequent calls will be ignored).
This method should be called on the main application thread to shut down the CEF browser process before the application exits. 
If you are Using CefSharp.OffScreen then you must call this explicitly before your application exits or it will hang.
This method must be called on the same thread as Initialize. If you don't call Shutdown explicitly then CefSharp.Wpf and CefSharp.WinForms
versions will do their best to call Shutdown for you, if your application is having trouble closing then call thus explicitly.
</summary>
        </member>
        <member name="M:CefSharp.Core.Cef.PreShutdown">
            <summary>
Called prior to calling Cef.Shutdown, this diposes of any remaning
ChromiumWebBrowser instances. In WPF this is used from Dispatcher.ShutdownStarted
to release the unmanaged resources held by the ChromiumWebBrowser instances.
Generally speaking you don't need to call this yourself.
</summary>
        </member>
        <member name="M:CefSharp.Core.Cef.GetGlobalCookieManager(CefSharp.ICompletionCallback)">
            <summary>
Returns the global cookie manager. By default data will be stored at CefSettings.CachePath if specified or in memory otherwise.
Using this method is equivalent to calling Cef.GetGlobalRequestContext().GetCookieManager()
The cookie managers storage is created in an async fashion, whilst this method may return a cookie manager instance,
there may be a short delay before you can Get/Write cookies.
To be sure the cookie manager has been initialized use one of the following
- Access the ICookieManager after ICompletionCallback.OnComplete has been called
- Access the ICookieManager instance in IBrowserProcessHandler.OnContextInitialized.
- Use the ChromiumWebBrowser BrowserInitialized (OffScreen) or IsBrowserInitializedChanged (WinForms/WPF) events.
</summary>
            <param name="callback">If non-NULL it will be executed asnychronously on the CEF UI thread after the manager's storage has been initialized.</param>
            <returns>A the global cookie manager or null if the RequestContext has not yet been initialized.</returns>
        </member>
        <member name="M:CefSharp.Core.Cef.GetGlobalCookieManager">
            <summary>
Returns the global cookie manager. By default data will be stored at CefSettings.CachePath if specified or in memory otherwise.
Using this method is equivalent to calling Cef.GetGlobalRequestContext().GetCookieManager()
The cookie managers storage is created in an async fashion, whilst this method may return a cookie manager instance,
there may be a short delay before you can Get/Write cookies.
To be sure the cookie manager has been initialized use one of the following
- Use the GetGlobalCookieManager(ICompletionCallback) overload and access the ICookieManager after
  ICompletionCallback.OnComplete has been called.
- Access the ICookieManager instance in IBrowserProcessHandler.OnContextInitialized.
- Use the ChromiumWebBrowser BrowserInitialized (OffScreen) or IsBrowserInitializedChanged (WinForms/WPF) events.
</summary>
            <returns>A the global cookie manager or null if the RequestContext has not yet been initialized.</returns>
        </member>
        <member name="M:CefSharp.Core.Cef.ClearCrossOriginWhitelist">
            <summary>Remove all entries from the cross-origin access whitelist.</summary>
            <remarks>
Remove all entries from the cross-origin access whitelist. Returns false if
the whitelist cannot be accessed.
</remarks>
        </member>
        <member name="M:CefSharp.Core.Cef.RemoveCrossOriginWhitelistEntry(System.String,System.String,System.String,System.Boolean)">
            <summary>Remove entry from cross-origin whitelist</summary>
            <param name="sourceOrigin">The origin allowed to be accessed by the target protocol/domain.</param>
            <param name="targetProtocol">The target protocol allowed to access the source origin.</param>
            <param name="targetDomain">The optional target domain allowed to access the source origin.</param>
            <param name="allowTargetSubdomains">If set to true would allow a blah.example.com if the 
    <paramref name="targetDomain" /> was set to example.com
</param>
            <remarks>
Remove an entry from the cross-origin access whitelist. Returns false if
<paramref name="sourceOrigin" /> is invalid or the whitelist cannot be accessed.
</remarks>
        </member>
        <member name="M:CefSharp.Core.Cef.AddCrossOriginWhitelistEntry(System.String,System.String,System.String,System.Boolean)">
            <summary>Add an entry to the cross-origin whitelist.</summary>
            <param name="sourceOrigin">The origin allowed to be accessed by the target protocol/domain.</param>
            <param name="targetProtocol">The target protocol allowed to access the source origin.</param>
            <param name="targetDomain">The optional target domain allowed to access the source origin.</param>
            <param name="allowTargetSubdomains">If set to true would allow a blah.example.com if the 
    <paramref name="targetDomain" /> was set to example.com
</param>
            <returns>Returns false if is invalid or the whitelist cannot be accessed.</returns>
            <remarks>
The same-origin policy restricts how scripts hosted from different origins
(scheme + domain + port) can communicate. By default, scripts can only access
resources with the same origin. Scripts hosted on the HTTP and HTTPS schemes
(but no other schemes) can use the "Access-Control-Allow-Origin" header to
allow cross-origin requests. For example, https://source.example.com can make
XMLHttpRequest requests on http://target.example.com if the
http://target.example.com request returns an "Access-Control-Allow-Origin:
https://source.example.com" response header.
Scripts in separate frames or iframes and hosted from the same protocol and
domain suffix can execute cross-origin JavaScript if both pages set the
document.domain value to the same domain suffix. For example,
scheme://foo.example.com and scheme://bar.example.com can communicate using
JavaScript if both domains set document.domain="example.com".
This method is used to allow access to origins that would otherwise violate
the same-origin policy. Scripts hosted underneath the fully qualified
<paramref name="sourceOrigin" /> URL (like http://www.example.com) will be allowed access to
all resources hosted on the specified <paramref name="targetProtocol" /> and <paramref name="targetDomain" />.
If <paramref name="targetDomain" /> is non-empty and <paramref name="allowTargetSubdomains" /> if false only
exact domain matches will be allowed. If <paramref name="targetDomain" /> contains a top-
level domain component (like "example.com") and <paramref name="allowTargetSubdomains" /> is
true sub-domain matches will be allowed. If <paramref name="targetDomain" /> is empty and
<paramref name="allowTargetSubdomains" /> if true all domains and IP addresses will be
allowed.
This method cannot be used to bypass the restrictions on local or display
isolated schemes. See the comments on <see cref="T:CefSharp.CefCustomScheme" /> for more
information.

This function may be called on any thread. Returns false if <paramref name="sourceOrigin" />
is invalid or the whitelist cannot be accessed.
</remarks>
        </member>
        <member name="M:CefSharp.Core.Cef.ExecuteProcess">
            <summary>
This function should be called from the application entry point function to execute a secondary process.
It can be used to run secondary processes from the browser client executable (default behavior) or
from a separate executable specified by the CefSettings.browser_subprocess_path value.
If called for the browser process (identified by no "type" command-line value) it will return immediately with a value of -1.
If called for a recognized secondary process it will block until the process should exit and then return the process exit code.
The |application| parameter may be empty. The |windows_sandbox_info| parameter is only used on Windows and may be NULL (see cef_sandbox_win.h for details). 
</summary>
        </member>
        <member name="M:CefSharp.Core.Cef.DoMessageLoopWork">
            <summary>
Perform a single iteration of CEF message loop processing.This function is
provided for cases where the CEF message loop must be integrated into an
existing application message loop. Use of this function is not recommended
for most users; use CefSettings.MultiThreadedMessageLoop if possible (the default).
When using this function care must be taken to balance performance
against excessive CPU usage. It is recommended to enable the
CefSettings.ExternalMessagePump option when using
this function so that IBrowserProcessHandler.OnScheduleMessagePumpWork()
callbacks can facilitate the scheduling process. This function should only be
called on the main application thread and only if Cef.Initialize() is called
with a CefSettings.MultiThreadedMessageLoop value of false. This function
will not block.
</summary>
        </member>
        <member name="M:CefSharp.Core.Cef.QuitMessageLoop">
            <summary>
Quit the CEF message loop that was started by calling Cef.RunMessageLoop().
This function should only be called on the main application thread and only
if Cef.RunMessageLoop() was used.
</summary>
        </member>
        <member name="M:CefSharp.Core.Cef.RunMessageLoop">
            <summary>
Run the CEF message loop. Use this function instead of an application-
provided message loop to get the best balance between performance and CPU
usage. This function should only be called on the main application thread and
only if Cef.Initialize() is called with a
CefSettings.MultiThreadedMessageLoop value of false. This function will
block until a quit message is received by the system.
</summary>
        </member>
        <member name="M:CefSharp.Core.Cef.Initialize(CefSharp.Core.CefSettingsBase,System.Boolean,CefSharp.IApp)">
            <summary>
Initializes CefSharp with user-provided settings.
It's important to note that Initialize/Shutdown <strong>MUST</strong> be called on your main
application thread (typically the UI thread). If you call them on different
threads, your application will hang. See the documentation for Cef.Shutdown() for more details.
</summary>
            <param name="cefSettings">CefSharp configuration settings.</param>
            <param name="performDependencyCheck">Check that all relevant dependencies avaliable, throws exception if any are missing</param>
            <param name="cefApp">Implement this interface to provide handler implementations. Null if you don't wish to handle these events</param>
            <returns>true if successful; otherwise, false.</returns>
        </member>
        <member name="M:CefSharp.Core.Cef.Initialize(CefSharp.Core.CefSettingsBase,System.Boolean,CefSharp.IBrowserProcessHandler)">
            <summary>
Initializes CefSharp with user-provided settings.
It's important to note that Initialize/Shutdown <strong>MUST</strong> be called on your main
applicaiton thread (Typically the UI thead). If you call them on different
threads, your application will hang. See the documentation for Cef.Shutdown() for more details.
</summary>
            <param name="cefSettings">CefSharp configuration settings.</param>
            <param name="performDependencyCheck">Check that all relevant dependencies avaliable, throws exception if any are missing</param>
            <param name="browserProcessHandler">The handler for functionality specific to the browser process. Null if you don't wish to handle these events</param>
            <returns>true if successful; otherwise, false.</returns>
        </member>
        <member name="M:CefSharp.Core.Cef.Initialize(CefSharp.Core.CefSettingsBase,System.Boolean)">
            <summary>
Initializes CefSharp with user-provided settings.
It's important to note that Initialize/Shutdown <strong>MUST</strong> be called on your main
application thread (typically the UI thread). If you call them on different
threads, your application will hang. See the documentation for Cef.Shutdown() for more details.
</summary>
            <param name="cefSettings">CefSharp configuration settings.</param>
            <param name="performDependencyCheck">Check that all relevant dependencies avaliable, throws exception if any are missing</param>
            <returns>true if successful; otherwise, false.</returns>
        </member>
        <member name="M:CefSharp.Core.Cef.Initialize(CefSharp.Core.CefSettingsBase)">
            <summary>
Initializes CefSharp with user-provided settings.
It's important to note that Initialize and Shutdown <strong>MUST</strong> be called on your main
application thread (typically the UI thread). If you call them on different
threads, your application will hang. See the documentation for Cef.Shutdown() for more details.
</summary>
            <param name="cefSettings">CefSharp configuration settings.</param>
            <returns>true if successful; otherwise, false.</returns>
        </member>
        <member name="P:CefSharp.Core.Cef.CefCommitHash">
            <summary>
Gets a value that indicates the Git Hash for CEF version currently being used.
</summary>
            <value>The Git Commit Hash</value>
        </member>
        <member name="P:CefSharp.Core.Cef.ChromiumVersion">
            <summary>Gets a value that indicates the Chromium version currently being used.</summary>
            <value>The Chromium version.</value>
        </member>
        <member name="P:CefSharp.Core.Cef.CefVersion">
            <summary>Gets a value that indicates the CEF version currently being used.</summary>
            <value>The CEF Version</value>
        </member>
        <member name="P:CefSharp.Core.Cef.CefSharpVersion">
            <summary>Gets a value that indicates the version of CefSharp currently being used.</summary>
            <value>The CefSharp version.</value>
        </member>
        <member name="P:CefSharp.Core.Cef.IsInitialized">
            <summary>Gets a value that indicates whether CefSharp is initialized.</summary>
            <value>true if CefSharp is initialized; otherwise, false.</value>
        </member>
        <member name="T:CefSharp.Core.Cef">
            <summary>
Global CEF methods are exposed through this class. e.g. CefInitalize maps to Cef.Initialize
CEF API Doc https://magpcss.org/ceforum/apidocs3/projects/(default)/(_globals).html
This class cannot be inherited.
</summary>
        </member>
        <member name="M:CefSharp.Internals.CefRegisterCdmCallbackAdapter.OnCdmRegistrationComplete(cef_cdm_registration_error_t,CefStringBase&lt;CefStringTraitsUTF16&gt;!System.Runtime.CompilerServices.IsConst*!System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
            <summary>
Method that will be called when CDM registration is complete. |result|
will be CEF_CDM_REGISTRATION_ERROR_NONE if registration completed
successfully. Otherwise, |result| and |error_message| will contain
additional information about why registration failed.
</summary>
        </member>
        <member name="M:CefSharp.Core.CefSettingsBase.RegisterScheme(CefSharp.CefCustomScheme)">
            <summary>
Registers a custom scheme using the provided settings.
</summary>
            <param name="cefCustomScheme">The CefCustomScheme which provides the details about the scheme.</param>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.ApplicationClientIdForFileScanning">
            <summary>
GUID string used for identifying the application. This is passed to the system AV function for scanning downloaded files. By
default, the GUID will be an empty string and the file will be treated as an untrusted file when the GUID is empty.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.BackgroundColor">
            <summary>
Background color used for the browser before a document is loaded and when no document color is specified. The alpha
component must be either fully opaque (0xFF) or fully transparent (0x00). If the alpha component is fully opaque then the RGB
components will be used as the background color. If the alpha component is fully transparent for a WinForms browser then the
default value of opaque white be used. If the alpha component is fully transparent for a windowless (WPF/OffScreen) browser
then transparent painting will be enabled.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.AcceptLanguageList">
            <summary>
Comma delimited ordered list of language codes without any whitespace that will be used in the "Accept-Language" HTTP header.
May be set globally using the CefSettings.AcceptLanguageList value. If both values are empty then "en-US,en" will be used.

</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.PersistUserPreferences">
            <summary>
To persist user preferences as a JSON file in the cache path directory set this value to true. A CachePath value must also be
specified to enable this feature. Also configurable using the "persist-user-preferences" command-line switch. Can be
overridden for individual RequestContext instances via the RequestContextSettings.PersistUserPreferences value.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.PersistSessionCookies">
            <summary>
To persist session cookies (cookies without an expiry date or validity interval) by default when using the global cookie
manager set this value to true. Session cookies are generally intended to be transient and most Web browsers do not persist
them. A CachePath value must also be specified to enable this feature. Also configurable using the "persist-session-cookies"
command-line switch. Can be overridden for individual RequestContext instances via the
RequestContextSettings.PersistSessionCookies value.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.WindowlessRenderingEnabled">
            <summary>
Set to true (1) to enable windowless (off-screen) rendering support. Do not enable this value if the application does not use
windowless rendering as it may reduce rendering performance on some systems.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.UserAgent">
            <summary>
Value that will be returned as the User-Agent HTTP header. If empty the default User-Agent string will be used. Also
configurable using the "user-agent" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.UncaughtExceptionStackSize">
            <summary>
The number of stack trace frames to capture for uncaught exceptions. Specify a positive value to enable the
CefRenderProcessHandler:: OnUncaughtException() callback. Specify 0 (default value) and OnUncaughtException() will not be
called. Also configurable using the "uncaught-exception-stack-size" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.RemoteDebuggingPort">
            <summary>
Set to a value between 1024 and 65535 to enable remote debugging on the specified port. For example, if 8080 is specified the
remote debugging URL will be http://localhost:8080. CEF can be remotely debugged from any CEF or Chrome browser window. Also
configurable using the "remote-debugging-port" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.ProductVersion">
            <summary>
Value that will be inserted as the product portion of the default User-Agent string. If empty the Chromium product version
will be used. If UserAgent is specified this value will be ignored. Also configurable using the "product-version" command-
line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.PackLoadingDisabled">
            <summary>
Set to true to disable loading of pack files for resources and locales. A resource bundle handler must be provided for the
browser and render processes via CefApp::GetResourceBundleHandler() if loading of pack files is disabled. Also configurable
using the "disable-pack-loading" command- line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.JavascriptFlags">
            <summary>
Custom flags that will be used when initializing the V8 JavaScript engine. The consequences of using custom flags may not be
well tested. Also configurable using the "js-flags" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.LogSeverity">
            <summary>
The log severity. Only messages of this severity level or higher will be logged. When set to
<see cref="F:CefSharp.LogSeverity.Disable" /> no messages will be written to the log file, but Fatal messages will still be
output to stderr. Also configurable using the "log-severity" command-line switch with a value of "verbose", "info", "warning",
"error", "fatal", "error-report" or "disable".
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.LogFile">
            <summary>
The directory and file name to use for the debug log. If empty a default log file name and location will be used. On Windows
a "debug.log" file will be written in the main executable directory. Also configurable using the"log-file" command- line
switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.ResourcesDirPath">
            <summary>
The fully qualified path for the resources directory. If this value is empty the cef.pak and/or devtools_resources.pak files
must be located in the module directory. Also configurable using the "resources-dir-path" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.LocalesDirPath">
            <summary>
The fully qualified path for the locales directory. If this value is empty the locales directory must be located in the
module directory. If this value is non-empty then it must be an absolute path. Also configurable using the "locales-dir-path"
command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.Locale">
            <summary>
The locale string that will be passed to WebKit. If empty the default locale of "en-US" will be used. Also configurable using
the "lang" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.IgnoreCertificateErrors">
            <summary>
Set to true in order to completely ignore SSL certificate errors. This is NOT recommended.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.UserDataPath">
            <summary>
The location where user data such as spell checking dictionary files will be stored on disk. If this value is empty then the
default user data directory will be used ("Local Settings\Application Data\CEF\User Data" directory under the user
profile directory on Windows). If this value is non-empty then it must be an absolute path.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.RootCachePath">
            <summary>
The root directory that all CefSettings.CachePath and RequestContextSettings.CachePath values must have in common. If this
value is empty and CefSettings.CachePath is non-empty then it will default to the CefSettings.CachePath value.
If this value is non-empty then it must be an absolute path.  Failure to set this value correctly may result in the sandbox
blocking read/write access to the CachePath directory. NOTE: CefSharp does not implement the CHROMIUM SANDBOX. A non-empty
RootCachePath can be used in conjuncation with an empty CefSettings.CachePath in instances where you would like browsers
attached to the Global RequestContext (the default) created in "incognito mode" and instances created with a custom
RequestContext using a disk based cache.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.CachePath">
            <summary>
The location where data for the global browser cache will be stored on disk. In this value is non-empty then it must be
an absolute path that is must be either equal to or a child directory of CefSettings.RootCachePath (if RootCachePath is
empty it will default to this value). If the value is empty then browsers will be created in "incognito mode" where
in-memory caches are used for storage and no data is persisted to disk. HTML5 databases such as localStorage will only
persist across sessions if a cache path is specified. Can be overridden for individual RequestContext instances via the
RequestContextSettings.CachePath value.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.BrowserSubprocessPath">
            <summary>
The path to a separate executable that will be launched for sub-processes. By default the browser process executable is used.
See the comments on Cef.ExecuteProcess() for details. If this value is non-empty then it must be an absolute path.
Also configurable using the "browser-subprocess-path" command-line switch.
Defaults to using the provided CefSharp.BrowserSubprocess.exe instance
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.MultiThreadedMessageLoop">
            <summary>
Set to true to have the browser process message loop run in a separate thread. If false than the CefDoMessageLoopWork()
function must be called from your application message loop. This option is only supported on Windows. The default value is
true.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.ExternalMessagePump">
            <summary>
Set to true to control browser process main (UI) thread message pump scheduling via the
IBrowserProcessHandler.OnScheduleMessagePumpWork callback. This option is recommended for use in combination with the
Cef.DoMessageLoopWork() function in cases where the CEF message loop must be integrated into an existing application message
loop (see additional comments and warnings on Cef.DoMessageLoopWork). Enabling this option is not recommended for most users;
leave this option disabled and use either MultiThreadedMessageLoop (the default) if possible.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.CommandLineArgsDisabled">
            <summary>
Set to true to disable configuration of browser process features using standard CEF and Chromium command-line arguments.
Configuration can still be specified using CEF data structures or by adding to CefCommandLineArgs.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.CefCommandLineArgs">
            <summary>
Add custom command line argumens to this collection, they will be added in OnBeforeCommandLineProcessing. The
CefSettings.CommandLineArgsDisabled value can be used to start with an empty command-line object. Any values specified in
CefSettings that equate to command-line arguments will be set before this method is called.
</summary>
        </member>
        <member name="P:CefSharp.Core.CefSettingsBase.CefCustomSchemes">
            <summary>
Add Customs schemes to this collection.
</summary>
        </member>
        <member name="M:CefSharp.Core.CefSettingsBase.Dispose">
            <summary>
Destructor.
</summary>
        </member>
        <member name="M:CefSharp.Core.CefSettingsBase.Finalize">
            <summary>
Finalizer.
</summary>
        </member>
        <member name="M:CefSharp.Core.CefSettingsBase.#ctor">
            <summary>
Default Constructor.
</summary>
        </member>
        <member name="F:CefSharp.Core.CefSettingsBase._cefCustomSchemes">
            <summary>
CefCustomScheme collection
</summary>
        </member>
        <member name="F:CefSharp.Core.CefSettingsBase._cefSettings">
            <summary>
CefSettings unmanaged pointer
</summary>
        </member>
        <member name="F:CefSharp.Core.CefSettingsBase._cefCommandLineArgs">
            <summary>
Command Line Arguments Dictionary. 
</summary>
        </member>
        <member name="T:CefSharp.Core.CefSettingsBase">
            <summary>
Initialization settings. Many of these and other settings can also configured using command-line switches.
WPF/WinForms/OffScreen each have their own CefSettings implementation that sets
relevant settings e.g. OffScreen starts with audio muted.
</summary>
        </member>
        <member name="M:CefSharp.Internals.CefUrlRequestClientAdapter.OnDownloadProgress(scoped_refptr&lt;CefURLRequest&gt;,System.Int64,System.Int64)">
ref 

</member>
        <member name="T:CefSharp.Internals.CefUrlRequestClientAdapter">
Interface that should be implemented by the CefUrlRequest client.
The methods of this class will be called on the same thread that created
the request unless otherwise documented. 
</member>
        <member name="M:CefSharp.Internals.CefFrameWrapper.LoadRequest(CefSharp.IRequest)">

Load the request represented by the |request| object.

</member>
        <member name="T:CefSharp.Core.ManagedCefBrowserAdapter">
            <exclude />
        </member>
        <member name="P:CefSharp.Core.DragData.ImageHotspot">
            <summary>
Get the image hotspot (drag start location relative to image dimensions).
</summary>
        </member>
        <member name="P:CefSharp.Core.DragData.Image">
            <summary>
Get the image representation of drag data.
May return NULL if no image representation is available.
</summary>
        </member>
        <member name="P:CefSharp.Internals.CefImageWrapper.Width">
            <summary>
Returns the image width in density independent pixel(DIP) units.
</summary>
        </member>
        <member name="M:CefSharp.Internals.CefImageWrapper.RemoveRepresentation(System.Single)">
            <summary>
Removes the representation for scaleFactor.
</summary>
            <param name="scaleFactor" />
            <returns>true for success</returns>
        </member>
        <member name="M:CefSharp.Internals.CefImageWrapper.IsSame(CefSharp.IImage)">
            <summary>
Returns true if this Image and that Image share the same underlying storage.
</summary>
            <param name="that">image to compare</param>
            <returns>returns true if share same underlying storage</returns>
        </member>
        <member name="P:CefSharp.Internals.CefImageWrapper.IsEmpty">
            <summary>
Returns true if this Image is empty.
</summary>
            <returns />
        </member>
        <member name="M:CefSharp.Internals.CefImageWrapper.HasRepresentation(System.Single)">
            <summary>
Returns true if this image contains a representation for scaleFactor.
</summary>
            <param name="scaleFactor" />
            <returns />
        </member>
        <member name="P:CefSharp.Internals.CefImageWrapper.Height">
            <summary>
Returns the image height in density independent pixel(DIP) units.
</summary>
        </member>
        <member name="M:CefSharp.Internals.CefImageWrapper.GetRepresentationInfo(System.Single,System.Single@,System.Int32@,System.Int32@)">
            <summary>
Returns information for the representation that most closely matches scaleFactor.
</summary>
            <param name="scaleFactor">scale factor</param>
            <param name="actualScaleFactor">actual scale factor</param>
            <param name="pixelWidth">pixel width</param>
            <param name="pixelHeight">pixel height</param>
            <returns>return if information found for scale factor</returns>
        </member>
        <member name="M:CefSharp.Internals.CefImageWrapper.GetAsPNG(System.Single,System.Boolean,System.Int32@,System.Int32@)">
            <summary>
Returns the PNG representation that most closely matches scaleFactor.
</summary>
            <param name="scaleFactor">scale factor</param>
            <param name="withTransparency">is the PNG transparent</param>
            <param name="pixelWidth">pixel width</param>
            <param name="pixelHeight">pixel height</param>
            <returns>A stream represending the PNG or null.</returns>
        </member>
        <member name="M:CefSharp.Internals.CefImageWrapper.GetAsJPEG(System.Single,System.Int32,System.Int32@,System.Int32@)">
            <summary>
Returns the JPEG representation that most closely matches scaleFactor.
</summary>
            <param name="scaleFactor">scale factor</param>
            <param name="quality">image quality</param>
            <param name="pixelWidth">pixel width</param>
            <param name="pixelHeight">pixel height</param>
            <returns>A stream representing the JPEG or null.</returns>
        </member>
        <member name="M:CefSharp.Internals.CefImageWrapper.GetAsBitmap(System.Single,CefSharp.Enums.ColorType,CefSharp.Enums.AlphaType,System.Int32@,System.Int32@)">
            <summary>
Returns the bitmap representation that most closely matches scaleFactor.
</summary>
            <param name="scaleFactor">scale factor</param>
            <param name="colorType">color type</param>
            <param name="alphaType">alpha type</param>
            <param name="pixelWidth">pixel width</param>
            <param name="pixelHeight">pixel height</param>
            <returns>A stream represending the bitmap or null.</returns>
        </member>
        <member name="M:CefSharp.Core.RequestContext.UnWrap">
            <summary>
Gets the inner most instance
</summary>
            <returns>current instance</returns>
        </member>
        <member name="M:CefSharp.Core.RequestContext.LoadExtension(System.String,System.String,CefSharp.IExtensionHandler)">
            <summary>
Load an extension. If extension resources will be read from disk using the default load implementation then rootDirectoy
should be the absolute path to the extension resources directory and manifestJson should be null.
If extension resources will be provided by the client (e.g. via IRequestHandler and/or IExtensionHandler) then rootDirectory
should be a path component unique to the extension (if not absolute this will be internally prefixed with the PK_DIR_RESOURCES path)
and manifestJson should contain the contents that would otherwise be read from the "manifest.json" file on disk.
The loaded extension will be accessible in all contexts sharing the same storage (HasExtension returns true).
However, only the context on which this method was called is considered the loader (DidLoadExtension returns true) and only the
loader will receive IRequestContextHandler callbacks for the extension. <see cref="M:CefSharp.IExtensionHandler.OnExtensionLoaded(CefSharp.IExtension)" /> will be
called on load success or <see cref="M:CefSharp.IExtensionHandler.OnExtensionLoadFailed(CefSharp.CefErrorCode)" /> will be called on load failure.
If the extension specifies a background script via the "background" manifest key then <see cref="M:CefSharp.IExtensionHandler.OnBeforeBackgroundBrowser(CefSharp.IExtension,System.String,CefSharp.IBrowserSettings)" />
will be called to create the background browser. See that method for additional information about background scripts.
For visible extension views the client application should evaluate the manifest to determine the correct extension URL to load and then pass
that URL to the IBrowserHost.CreateBrowser* function after the extension has loaded. For example, the client can look for the "browser_action"
manifest key as documented at https://developer.chrome.com/extensions/browserAction. Extension URLs take the form "chrome-extension:///".
Browsers that host extensions differ from normal browsers as follows: - Can access chrome.* JavaScript APIs if allowed by the manifest.
Visit chrome://extensions-support for the list of extension APIs currently supported by CEF. - Main frame navigation to non-extension
content is blocked.
- Pinch-zooming is disabled.
- <see cref="P:CefSharp.IBrowserHost.Extension" /> returns the hosted extension.
- CefBrowserHost::IsBackgroundHost returns true for background hosts. See https://developer.chrome.com/extensions for extension implementation and usage documentation.
</summary>
            <param name="rootDirectory">If extension resources will be read from disk using the default load implementation then rootDirectoy
should be the absolute path to the extension resources directory and manifestJson should be null</param>
            <param name="manifestJson">If extension resources will be provided by the client then rootDirectory should be a path component unique to the extension
and manifestJson should contain the contents that would otherwise be read from the manifest.json file on disk</param>
            <param name="handler">handle events related to browser extensions</param>
        </member>
        <member name="M:CefSharp.Core.RequestContext.HasExtension(System.String)">
            <summary>
Returns true if this context has access to the extension identified by extensionId.
This may not be the context that was used to load the extension (see DidLoadExtension).
This method must be called on the CEF UI thread.
</summary>
            <param name="extensionId">extension id</param>
            <returns>Returns true if this context has access to the extension identified by extensionId</returns>
            <remarks>Use Cef.UIThreadTaskFactory to execute this method if required,
<see cref="M:CefSharp.IBrowserProcessHandler.OnContextInitialized" /> and ChromiumWebBrowser.IsBrowserInitializedChanged are both
executed on the CEF UI thread, so can be called directly.
When CefSettings.MultiThreadedMessageLoop == false (the default is true) then the main
application thread will be the CEF UI thread.</remarks>
        </member>
        <member name="M:CefSharp.Core.RequestContext.GetExtensions(System.Collections.Generic.IList`1{System.String}@)">
            <summary>
Retrieve the list of all extensions that this context has access to (see HasExtension).
<paramref name="extensionIds" /> will be populated with the list of extension ID values.
This method must be called on the CEF UI thread.
</summary>
            <param name="extensionIds">output a list of extensions Ids</param>
            <returns>returns true on success otherwise false</returns>
            <remarks>Use Cef.UIThreadTaskFactory to execute this method if required,
<see cref="M:CefSharp.IBrowserProcessHandler.OnContextInitialized" /> and ChromiumWebBrowser.IsBrowserInitializedChanged are both
executed on the CEF UI thread, so can be called directly.
When CefSettings.MultiThreadedMessageLoop == false (the default is true) then the main
application thread will be the CEF UI thread.</remarks>
        </member>
        <member name="M:CefSharp.Core.RequestContext.GetExtension(System.String)">
            <summary>
Returns the extension matching extensionId or null if no matching extension is accessible in this context (see HasExtension).
This method must be called on the CEF UI thread.
</summary>
            <param name="extensionId">extension Id</param>
            <returns>Returns the extension matching extensionId or null if no matching extension is accessible in this context</returns>
            <remarks>Use Cef.UIThreadTaskFactory to execute this method if required,
<see cref="M:CefSharp.IBrowserProcessHandler.OnContextInitialized" /> and ChromiumWebBrowser.IsBrowserInitializedChanged are both
executed on the CEF UI thread, so can be called directly.
When CefSettings.MultiThreadedMessageLoop == false (the default is true) then the main
application thread will be the CEF UI thread.</remarks>
        </member>
        <member name="M:CefSharp.Core.RequestContext.DidLoadExtension(System.String)">
            <summary>
Returns true if this context was used to load the extension identified by extensionId. Other contexts sharing the same storage will also have access to the extension (see HasExtension).
This method must be called on the CEF UI thread.
</summary>
            <returns>Returns true if this context was used to load the extension identified by extensionId</returns>
            <remarks>Use Cef.UIThreadTaskFactory to execute this method if required,
<see cref="M:CefSharp.IBrowserProcessHandler.OnContextInitialized" /> and ChromiumWebBrowser.IsBrowserInitializedChanged are both
executed on the CEF UI thread, so can be called directly.
When CefSettings.MultiThreadedMessageLoop == false (the default is true) then the main
application thread will be the CEF UI thread.</remarks>
        </member>
        <member name="M:CefSharp.Core.RequestContext.ResolveHostAsync(System.Uri)">
            <summary>
Attempts to resolve origin to a list of associated IP addresses.
</summary>
            <param name="origin">host name to resolve</param>
            <returns>A task that represents the Resoolve Host operation. The value of the TResult parameter contains ResolveCallbackResult.</returns>
        </member>
        <member name="M:CefSharp.Core.RequestContext.CloseAllConnections(CefSharp.ICompletionCallback)">
            <summary>
Clears all active and idle connections that Chromium currently has.
This is only recommended if you have released all other CEF objects but
don't yet want to call Cef.Shutdown().
</summary>
            <param name="callback">If is non-NULL it will be executed on the CEF UI thread after
completion. This param is optional</param>
        </member>
        <member name="M:CefSharp.Core.RequestContext.ClearHttpAuthCredentials(CefSharp.ICompletionCallback)">
            <summary>
Clears all HTTP authentication credentials that were added as part of handling
<see cref="M:CefSharp.IRequestHandler.GetAuthCredentials(CefSharp.IWebBrowser,CefSharp.IBrowser,System.String,System.Boolean,System.String,System.Int32,System.String,System.String,CefSharp.IAuthCallback)" />.
</summary>
            <param name="callback">If is non-NULL it will be executed on the CEF UI thread after
completion. This param is optional</param>
        </member>
        <member name="M:CefSharp.Core.RequestContext.ClearCertificateExceptions(CefSharp.ICompletionCallback)">
            <summary>
Clears all certificate exceptions that were added as part of handling
<see cref="M:CefSharp.IRequestHandler.OnCertificateError(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.CefErrorCode,System.String,CefSharp.ISslInfo,CefSharp.IRequestCallback)" />. If you call this it is
recommended that you also call <see cref="M:CefSharp.IRequestContext.CloseAllConnections(CefSharp.ICompletionCallback)" /> or you risk not
being prompted again for server certificates if you reconnect quickly.
</summary>
            <param name="callback">If is non-NULL it will be executed on the CEF UI thread after
completion. This param is optional</param>
        </member>
        <member name="M:CefSharp.Core.RequestContext.SetPreference(System.String,System.Object,System.String@)">
            <summary>
Set the value associated with preference name. If value is null the
preference will be restored to its default value. If setting the preference
fails then error will be populated with a detailed description of the
problem. This method must be called on the CEF UI thread.
Preferences set via the command-line usually cannot be modified.
</summary>
            <param name="name">preference key</param>
            <param name="value">preference value</param>
            <param name="error">out error</param>
            <returns>Returns true if the value is set successfully and false otherwise.</returns>
            <remarks>Use Cef.UIThreadTaskFactory to execute this method if required,
<see cref="M:CefSharp.IBrowserProcessHandler.OnContextInitialized" /> and ChromiumWebBrowser.IsBrowserInitializedChanged are both
executed on the CEF UI thread, so can be called directly.
When CefSettings.MultiThreadedMessageLoop == false (the default is true) then the main
application thread will be the CEF UI thread.</remarks>
        </member>
        <member name="M:CefSharp.Core.RequestContext.CanSetPreference(System.String)">
            <summary>
Returns true if the preference with the specified name can be modified
using SetPreference. As one example preferences set via the command-line
usually cannot be modified. This method must be called on the CEF UI thread.
</summary>
            <param name="name">preference key</param>
            <returns>Returns true if the preference with the specified name can be modified
using SetPreference</returns>
            <remarks>Use Cef.UIThreadTaskFactory to execute this method if required,
<see cref="M:CefSharp.IBrowserProcessHandler.OnContextInitialized" /> and ChromiumWebBrowser.IsBrowserInitializedChanged are both
executed on the CEF UI thread, so can be called directly.
When CefSettings.MultiThreadedMessageLoop == false (the default is true) then the main
application thread will be the CEF UI thread.</remarks>
        </member>
        <member name="M:CefSharp.Core.RequestContext.GetAllPreferences(System.Boolean)">
            <summary>
Returns all preferences as a dictionary. The returned
object contains a copy of the underlying preference values and
modifications to the returned object will not modify the underlying
preference values. This method must be called on the browser process UI
thread.
</summary>
            <param name="includeDefaults">If true then
preferences currently at their default value will be included.</param>
            <returns>Preferences (dictionary can have sub dictionaries)</returns>
        </member>
        <member name="M:CefSharp.Core.RequestContext.GetPreference(System.String)">
            <summary>
Returns the value for the preference with the specified name. Returns
NULL if the preference does not exist. The returned object contains a copy
of the underlying preference value and modifications to the returned object
will not modify the underlying preference value. This method must be called
on the CEF UI thread.
</summary>
            <param name="name">preference name</param>
            <returns>Returns the value for the preference with the specified name</returns>
            <remarks>Use Cef.UIThreadTaskFactory to execute this method if required,
<see cref="M:CefSharp.IBrowserProcessHandler.OnContextInitialized" /> and ChromiumWebBrowser.IsBrowserInitializedChanged are both
executed on the CEF UI thread, so can be called directly.
When CefSettings.MultiThreadedMessageLoop == false (the default is true) then the main
application thread will be the CEF UI thread.</remarks>
        </member>
        <member name="M:CefSharp.Core.RequestContext.HasPreference(System.String)">
            <summary>
Returns true if a preference with the specified name exists. This method
must be called on the CEF UI thread.
</summary>
            <param name="name">name of preference</param>
            <returns>bool if the preference exists</returns>
            <remarks>Use Cef.UIThreadTaskFactory to execute this method if required,
<see cref="M:CefSharp.IBrowserProcessHandler.OnContextInitialized" /> and ChromiumWebBrowser.IsBrowserInitializedChanged are both
executed on the CEF UI thread, so can be called directly.
When CefSettings.MultiThreadedMessageLoop == false (the default is true) then the main
application thread will be the CEF UI thread.</remarks>
        </member>
        <member name="M:CefSharp.Core.RequestContext.PurgePluginListCache(System.Boolean)">
            <summary>
Tells all renderer processes associated with this context to throw away
their plugin list cache. If reloadPages is true they will also reload
all pages with plugins. RequestContextHandler.OnBeforePluginLoad may
be called to rebuild the plugin list cache.
</summary>
            <param name="reloadPages">reload any pages with pluginst</param>
        </member>
        <member name="P:CefSharp.Core.RequestContext.CachePath">
            <summary>
Returns the cache path for this object. If empty an "incognito mode"
in-memory cache is being used.
</summary>
        </member>
        <member name="M:CefSharp.Core.RequestContext.ClearSchemeHandlerFactories">
            <summary>
Clear all registered scheme handler factories. 
</summary>
            <returns>Returns false on error.</returns>
        </member>
        <member name="M:CefSharp.Core.RequestContext.RegisterSchemeHandlerFactory(System.String,System.String,CefSharp.ISchemeHandlerFactory)">
            <summary>
Register a scheme handler factory for the specified schemeName and optional domainName.
An empty domainName value for a standard scheme will cause the factory to match all domain
names. The domainName value will be ignored for non-standard schemes. If schemeName is
a built-in scheme and no handler is returned by factory then the built-in scheme handler
factory will be called. If schemeName is a custom scheme then you must also implement the
IApp.OnRegisterCustomSchemes() method in all processes. This function may be called multiple
times to change or remove the factory that matches the specified schemeName and optional
domainName.
</summary>
            <param name="schemeName">Scheme Name</param>
            <param name="domainName">Optional domain name</param>
            <param name="factory">Scheme handler factory</param>
            <returns>Returns false if an error occurs.</returns>
        </member>
        <member name="P:CefSharp.Core.RequestContext.IsGlobal">
            <summary>
Returns true if this object is the global context. The global context is
used by default when creating a browser or URL request with a NULL context
argument.
</summary>
        </member>
        <member name="M:CefSharp.Core.RequestContext.GetCookieManager(CefSharp.ICompletionCallback)">
            <summary>
Returns the default cookie manager for this object. This will be the global
cookie manager if this object is the global request context. 
</summary>
            <param name="callback">If callback is non-NULL it will be executed asnychronously on the CEF IO thread
after the manager's storage has been initialized.</param>
            <returns>Returns the default cookie manager for this object</returns>
        </member>
        <member name="M:CefSharp.Core.RequestContext.IsSharingWith(CefSharp.IRequestContext)">
            <summary>
Returns true if this object is sharing the same storage as the specified context.
</summary>
            <param name="context">context to compare</param>
            <returns>Returns true if same storage</returns>
        </member>
        <member name="M:CefSharp.Core.RequestContext.IsSame(CefSharp.IRequestContext)">
            <summary>
Returns true if this object is pointing to the same context object.
</summary>
            <param name="context">context to compare</param>
            <returns>Returns true if the same</returns>
        </member>
        <member name="M:CefSharp.Core.RequestContext.CreateContext(CefSharp.IRequestContext,CefSharp.IRequestContextHandler)">
            <summary>
Creates a new context object that shares storage with other and uses an
optional handler.
</summary>
            <param name="other">shares storage with this RequestContext</param>
            <param name="requestContextHandler">optional requestContext handler</param>
            <returns>Returns a new RequestContext</returns>
        </member>
        <member name="M:CefSharp.Core.RequestContext.#ctor(CefSharp.IRequestContext)">
Creates a new context object that shares storage with | other | and uses an optional | handler | .
</member>
        <member name="T:CefSharp.Core.RequestContext">
            <summary>
A request context provides request handling for a set of related browser objects.
A request context is specified when creating a new browser object via the CefBrowserHost
static factory methods. Browser objects with different request contexts will never be
hosted in the same render process. Browser objects with the same request context may or
may not be hosted in the same render process depending on the process model.
Browser objects created indirectly via the JavaScript window.open function or targeted
links will share the same render process and the same request context as the source browser.
When running in single-process mode there is only a single render process (the main process)
and so all browsers created in single-process mode will share the same request context.
This will be the first request context passed into a CefBrowserHost static factory method
and all other request context objects will be ignored. 
</summary>
        </member>
        <member name="P:CefSharp.Core.RequestContextSettings.IgnoreCertificateErrors">
            <summary>
Set to true to ignore errors related to invalid SSL certificates.
Enabling this setting can lead to potential security vulnerabilities like
"man in the middle" attacks. Applications that load content from the
internet should not enable this setting. Can be set globally using the
CefSettings.IgnoreCertificateErrors value. This value will be ignored if
CachePath matches the CefSettings.cache_path value.
</summary>
        </member>
        <member name="P:CefSharp.Core.RequestContextSettings.AcceptLanguageList">
            <summary>
Comma delimited ordered list of language codes without any whitespace that
will be used in the "Accept-Language" HTTP header. Can be set globally
using the CefSettings.accept_language_list value or overridden on a per-
browser basis using the BrowserSettings.AcceptLanguageList value. If
all values are empty then "en-US,en" will be used. This value will be
ignored if CachePath matches the CefSettings.CachePath value.
</summary>
        </member>
        <member name="P:CefSharp.Core.RequestContextSettings.CachePath">
            <summary>
The location where cache data for this request context will be stored on
disk. If this value is non-empty then it must be an absolute path that is
either equal to or a child directory of CefSettings.RootCachePath.
If the value is empty then browsers will be created in "incognito mode"
where in-memory caches are used for storage and no data is persisted to disk.
HTML5 databases such as localStorage will only persist across sessions if a
cache path is specified. To share the global browser cache and related
configuration set this value to match the CefSettings.CachePath value.
</summary>
        </member>
        <member name="P:CefSharp.Core.RequestContextSettings.PersistUserPreferences">
            <summary>
To persist user preferences as a JSON file in the cache path directory set
this value to true. Can be set globally using the
CefSettings.PersistUserPreferences value. This value will be ignored if
CachePath is empty or if it matches the CefSettings.CachePath value.
</summary>
        </member>
        <member name="P:CefSharp.Core.RequestContextSettings.PersistSessionCookies">
            <summary>
To persist session cookies (cookies without an expiry date or validity
interval) by default when using the global cookie manager set this value to
true. Session cookies are generally intended to be transient and most
Web browsers do not persist them. Can be set globally using the
CefSettings.PersistSessionCookies value. This value will be ignored if
CachePath is empty or if it matches the CefSettings.CachePath value.
</summary>
        </member>
        <member name="M:CefSharp.Core.RequestContextSettings.#ctor">
            <summary>
Default constructor
</summary>
        </member>
        <member name="T:CefSharp.Core.RequestContextSettings">
            <summary>
RequestContextSettings
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.AutoDispose">
            <summary>
True if dispose should be called after this object is used
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.IsDisposed">
            <summary>
Gets a value indicating if the browser settings has been disposed.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.WindowlessFrameRate">
            <summary>
The maximum rate in frames per second (fps) that CefRenderHandler::OnPaint
will be called for a windowless browser. The actual fps may be lower if
the browser cannot generate frames at the requested rate. The minimum
value is 1 and the maximum value is 60 (default 30). This value can also be
changed dynamically via IBrowserHost.SetWindowlessFrameRate.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.AcceptLanguageList">
            <summary>
Comma delimited ordered list of language codes without any whitespace that
will be used in the "Accept-Language" HTTP header. May be overridden on a
per-browser basis using the CefBrowserSettings.AcceptLanguageList value.
If both values are empty then "en-US,en" will be used. Can be overridden
for individual RequestContext instances via the
RequestContextSettings.AcceptLanguageList value.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.BackgroundColor">
            <summary>
Background color used for the browser before a document is loaded and when no document color
is specified. The alpha component must be either fully opaque (0xFF) or fully transparent (0x00).
If the alpha component is fully opaque then the RGB components will be used as the background
color. If the alpha component is fully transparent for a WinForms browser then the
CefSettings.BackgroundColor value will be used. If the alpha component is fully transparent
for a windowless (WPF/OffScreen) browser then transparent painting will be enabled.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.WebGl">
            <summary>
Controls whether WebGL can be used. Note that WebGL requires hardware
support and may not work on all systems even when enabled. Also
configurable using the "disable-webgl" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.ApplicationCache">
            <summary>
Controls whether the application cache can be used. Also configurable using
the "disable-application-cache" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.Databases">
            <summary>
Controls whether databases can be used. Also configurable using the
"disable-databases" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.LocalStorage">
            <summary>
Controls whether local storage can be used. Also configurable using the
"disable-local-storage" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.TabToLinks">
            <summary>
Controls whether the tab key can advance focus to links. Also configurable
using the "disable-tab-to-links" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.TextAreaResize">
            <summary>
Controls whether text areas can be resized. Also configurable using the
"disable-text-area-resize" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.ImageShrinkStandaloneToFit">
            <summary>
Controls whether standalone images will be shrunk to fit the page. Also
configurable using the "image-shrink-standalone-to-fit" command-line
switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.ImageLoading">
            <summary>
Controls whether image URLs will be loaded from the network. A cached image
will still be rendered if requested. Also configurable using the
"disable-image-loading" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.WebSecurity">
            <summary>
Controls whether web security restrictions (same-origin policy) will be
enforced. Disabling this setting is not recommend as it will allow risky
security behavior such as cross-site scripting (XSS). Also configurable
using the "disable-web-security" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.FileAccessFromFileUrls">
            <summary>
Controls whether file URLs will have access to other file URLs. Also
configurable using the "allow-access-from-files" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.UniversalAccessFromFileUrls">
            <summary>
Controls whether file URLs will have access to all URLs. Also configurable
using the "allow-universal-access-from-files" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.Plugins">
            <summary>
Controls whether any plugins will be loaded. Also configurable using the
"disable-plugins" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.JavascriptDomPaste">
            <summary>
Controls whether DOM pasting is supported in the editor via
execCommand("paste"). The |javascript_access_clipboard| setting must also
be enabled. Also configurable using the "disable-javascript-dom-paste"
command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.JavascriptAccessClipboard">
            <summary>
Controls whether JavaScript can access the clipboard. Also configurable
using the "disable-javascript-access-clipboard" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.JavascriptCloseWindows">
            <summary>
Controls whether JavaScript can be used to close windows that were not
opened via JavaScript. JavaScript can still be used to close windows that
were opened via JavaScript. Also configurable using the
"disable-javascript-close-windows" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.Javascript">
            <summary>
Controls whether JavaScript can be executed. (Used to Enable/Disable javascript)
Also configurable using the "disable-javascript" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.RemoteFonts">
            <summary>
Controls the loading of fonts from remote sources. Also configurable using
the "disable-remote-fonts" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.DefaultEncoding">
            <summary>
Default encoding for Web content. If empty "ISO-8859-1" will be used. Also
configurable using the "default-encoding" command-line switch.
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.MinimumLogicalFontSize">
            <summary>
MinimumLogicalFontSize
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.MinimumFontSize">
            <summary>
MinimumFontSize
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.DefaultFixedFontSize">
            <summary>
DefaultFixedFontSize
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.DefaultFontSize">
            <summary>
DefaultFontSize
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.FantasyFontFamily">
            <summary>
FantasyFontFamily
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.CursiveFontFamily">
            <summary>
CursiveFontFamily
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.SansSerifFontFamily">
            <summary>
SansSerifFontFamily
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.SerifFontFamily">
            <summary>
SerifFontFamily
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.FixedFontFamily">
            <summary>
FixedFontFamily
</summary>
        </member>
        <member name="P:CefSharp.Core.BrowserSettings.StandardFontFamily">
            <summary>
StandardFontFamily
</summary>
        </member>
        <member name="M:CefSharp.Core.BrowserSettings.Dispose">
            <summary>
Destructor.
</summary>
        </member>
        <member name="M:CefSharp.Core.BrowserSettings.Finalize">
            <summary>
Finalizer.
</summary>
        </member>
        <member name="M:CefSharp.Core.BrowserSettings.#ctor">
            <summary>
Default Constructor
</summary>
        </member>
        <member name="M:CefSharp.Core.BrowserSettings.#ctor(CefStructBase&lt;CefBrowserSettingsTraits&gt;*)">
            <summary>
Internal Constructor
</summary>
        </member>
        <member name="T:CefSharp.Core.BrowserSettings">
            <summary>
Browser initialization settings. Specify NULL or 0 to get the recommended
default values. The consequences of using custom values may not be well
tested. Many of these and other settings can also configured using command-
line switches.
</summary>
        </member>
        <member name="M:CefSharp.Internals.StringUtils.CreateExceptionString(scoped_refptr&lt;CefV8Exception&gt;)">
            <summary>
Creates a detailed expection string from a provided Cef V8 exception.
</summary>
            <param name="exception">The exception which will be used as base for the message</param>
        </member>
        <member name="M:CefSharp.Internals.StringUtils.AssignNativeFromClr(_cef_string_utf16_t*!System.Runtime.CompilerServices.IsImplicitlyDereferenced,System.String)">
            <summary>
Assigns the provided cef_string_t object from the given .NET string.
</summary>
            <param name="cefStr">The cef_string_t that should be updated.</param>
            <param name="str">The .NET string whose value should be used to update cefStr.</param>
        </member>
        <member name="M:CefSharp.Internals.StringUtils.ToNative(System.Collections.Generic.IEnumerable`1{System.String})">
            <summary>
Converts a .NET List of strings to native (unmanaged) format.
</summary>
            <param name="str">The List of strings that should be converted.</param>
            <returns>An unmanaged representation of the provided List of strings, or an empty List if the input is a nullptr.</returns>
        </member>
        <member name="M:CefSharp.Internals.StringUtils.ToNative(System.String)">
            <summary>
Converts a .NET string to native (unmanaged) format. Note that this method does not allocate a new copy of the
</summary>
            <param name="str">The string that should be converted.</param>
            <returns>An unmanaged representation of the provided string, or an empty string if the input string is a nullptr.</returns>
        </member>
        <member name="M:CefSharp.Internals.StringUtils.ToClr(std.vector&lt;CefStringBase&lt;CefStringTraitsUTF16&gt;,std.allocator&lt;CefStringBase&lt;CefStringTraitsUTF16&gt;&gt;&gt;!System.Runtime.CompilerServices.IsConst*!System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
            <summary>
Converts an unmanaged vector of strings to a (managed) .NET List of strings.
</summary>
            <param name="cefStr">The vector of strings that should be converted.</param>
            <returns>A .NET List of strings.</returns>
        </member>
        <member name="M:CefSharp.Internals.StringUtils.ToClr(CefStringBase&lt;CefStringTraitsUTF16&gt;!System.Runtime.CompilerServices.IsConst*!System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
            <summary>
Converts an unmanaged string to a (managed) .NET string.
</summary>
            <param name="cefStr">The string that should be converted.</param>
            <returns>A .NET string.</returns>
        </member>
        <member name="M:CefSharp.Internals.StringUtils.ToClr(_cef_string_utf16_t!System.Runtime.CompilerServices.IsConst*!System.Runtime.CompilerServices.IsImplicitlyDereferenced)">
            <summary>
Converts an unmanaged string to a (managed) .NET string.
</summary>
            <param name="cefStr">The string that should be converted.</param>
            <returns>A .NET string.</returns>
        </member>
        <member name="T:CefSharp.CookieManager">
            <exclude />
        </member>
    </members>
</doc>