﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="changeShiftsTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\交接班报表20211203100902.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="changeTableform" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\转桌单.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;gb2312</value>
  </data>
  <data name="CYChangeShiftsTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\餐饮交接班报表202112031009021.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="CYProductionNoteTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\餐饮制作单20211203130400.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="CYRetreatFoodTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\餐饮退菜单20211217160802.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="CYRetreatFoodTemplate80" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\餐饮退菜单80.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="exchangeTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\积分兑换20220309161620 .txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="FZRetreatProductTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\服装退货单202112171608021.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="FZShoppingReceiptTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\服装挂单小票20220312102246.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="goodsSalesReportTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\商品销售报销小票20211203154358.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="HBMemberChargeTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\烘焙会员充值20211229153755.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="HBShoppingReceiptTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\printer202111261525431.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="HBShoppingReceiptTemplate80" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\烘焙销售小票80.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="mailing" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\寄件单.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="pickup" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\取件单.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="preSettlement" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\预结单20211220160830.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="preSettlement80" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\餐饮预结单80.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="priceTagTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\report1.rdlc;System.Byte[], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="productionNoteTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\制作单20211203130400.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="retreatFoodTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\退菜单20211217160802.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="retreatFoodTemplate80" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\退菜单80.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="retreatProductTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\退货单202112171608021.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="retreatProductTemplate80" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\退货单80.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="salesReportTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\销售报表小票20211207093214.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="salesReportTemplate80" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\销售报表小票80.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="SCBuyTimeCardTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\次卡购买20220309143953.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="SCShoppingReceiptTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\服装销售小票20220312092308.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="SCTimeCardUsedTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\次卡消费20220309161620.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="shoppingReceiptTemplate" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\printer20211126152543.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="statement" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\结账单20211221101225.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="statement80" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\结账单80.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="tableform" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\压桌单20211220112828.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="tableform80" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\压桌单202206211336.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
</root>