﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgSyncData.Model
{
    public class Accounts
    {
        public static string SyncSalesAccountsUpdate
        {
            get
            {
                var sql = "with tmp_update as ("
                      + " select tmp.account_fingerprint, tmp.pay_amt, tmp.is_deleted"
                      + " from tmp_sales as tmp inner join sales on tmp.fingerprint = sales.fingerprint and tmp.is_deleted != sales.is_deleted"
                      + " where sales.is_synced = 1 ), "
                     + " tmp_insert as ( select tmp.account_fingerprint, tmp.pay_amt from tmp_sales as tmp left join sales on tmp.fingerprint = sales.fingerprint "
                     + " where sales.id is null and tmp.is_deleted = 0 ), "
                     + " tmp_accts_all as ("
                     + " select account_fingerprint, pay_amt from tmp_update where is_deleted = 0"
                     + " union all select account_fingerprint, -pay_amt from tmp_update where is_deleted = 1"
                     + " union all select account_fingerprint, pay_amt from tmp_insert"
                     + " ), "
                     + " tmp_accts as (select account_fingerprint, sum(pay_amt) as pay_amt from tmp_accts_all group by account_fingerprint)"
                    + " update accounts set cur_amt = cur_amt + ("
                    + " select pay_amt from tmp_accts where account_fingerprint = accounts.fingerprint)"
                    + " where fingerprint in (select account_fingerprint from tmp_accts); ";
                return sql;
            }
        }
    }
}
