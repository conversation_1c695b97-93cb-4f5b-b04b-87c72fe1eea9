[{"index": 10, "contenttype": 1, "textalign": 32, "contentfont": "\"仿宋, 12pt, style=Bold\"", "content": "商品销售报表", "datamember": "storename", "format": "{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 20, "contenttype": 5, "textalign": 0, "content": 1}, {"index": 30, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 7pt\"", "content": "交易时间", "datamember": "交易时间", "format": "交易时间：{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 40, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "收银员", "datamember": "收银员", "format": "收银员：{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 50, "contenttype": 5, "textalign": 0, "content": 0}, {"index": 60, "contenttype": 3, "headtextalign": 16, "bodytextalign": 0, "datamember": "goods", "printhead": true, "printseparatorline": false, "headfont": "\"仿宋, 9pt\"", "bodyfont": "\"仿宋, 9pt\"", "config": "[{\"col_width\":\"35\",\"col_head\":\"名称\",\"col_dataMember\":\"名称\",\"col_singleLine\":\"True\",\"col_isPrint\":\"true\",\"col_align\":\"Left\"},{\"col_width\":\"30\",\"col_head\":\"次数\",\"col_dataMember\":\"次数\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\",\"col_align\":\"Center\"},{\"col_width\":\"30\",\"col_head\":\"价格\",\"col_dataMember\":\"价格\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\",\"col_align\":\"Right\"}]", "displayname": "商品信息"}, {"index": 61, "contenttype": 5, "textalign": 0, "content": 0}, {"index": 62, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "合计", "datamember": "合计", "format": "合计：{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 63, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "现金", "datamember": "现金", "format": "现金：{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 72, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "POS收银", "datamember": "POS收银", "format": "POS收银：{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 73, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "微信", "datamember": "微信", "format": "微信：{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 74, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "支付宝", "datamember": "支付宝", "format": "支付宝：{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 75, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "支付宝(扫码付)", "datamember": "支付宝(扫码付)", "format": "支付宝(扫码付)：{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 76, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "微信(扫码付)", "datamember": "微信(扫码付)", "format": "微信(扫码付)：{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 77, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "会员支付", "datamember": "会员支付", "format": "会员支付：{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 78, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "找零", "datamember": "找零", "format": "找零：{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 79, "contenttype": 5, "textalign": 0, "content": 0}, {"index": 80, "contenttype": 3, "headtextalign": 16, "bodytextalign": 0, "datamember": "goods2", "printhead": true, "printseparatorline": false, "headfont": "\"仿宋, 9pt\"", "bodyfont": "\"仿宋, 9pt\"", "config": "[{\"col_width\":\"40\",\"col_head\":\"持有有效次卡：\",\"col_dataMember\":\"name\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\",\"col_align\":\"Left\"}]", "displayname": "商品信息"}, {"index": 90, "contenttype": 5, "textalign": 0, "content": 0}, {"index": 100, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "会员名", "datamember": "会员名", "format": "会员名：{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 110, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "会员手机号", "datamember": "会员手机号", "format": "会员手机号：{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 111, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "余额", "datamember": "余额", "format": "余额：{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 112, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "积分", "datamember": "积分", "format": "积分：{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 120, "contenttype": 5, "textalign": 0, "content": 0}, {"index": 130, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "备注", "datamember": "备注", "format": "{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 140, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 7pt\"", "content": "打印时间", "datamember": "打印时间", "format": "打印时间：{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}]