[{"index": 1, "contenttype": 1, "textalign": 32, "contentfont": "\"仿宋, 12pt, style=Bold\"", "content": "交接班报表", "datamember": "storename", "format": "{0}", "defaultcontent": "", "displayname": "文本"}, {"index": 2, "contenttype": 5, "textalign": 0, "content": 1}, {"index": 2, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 7pt\"", "content": "开始时间", "datamember": "开始时间", "format": "开始时间：{0}", "defaultcontent": "", "displayname": "文本"}, {"index": 3, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 7pt\"", "content": "结束时间", "datamember": "结束时间", "format": "结束时间：{0}", "defaultcontent": "", "displayname": "文本"}, {"index": 4, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "收银员", "datamember": "收银员", "format": "收银员：{0}", "defaultcontent": "", "displayname": "文本"}, {"index": 5, "contenttype": 5, "textalign": 0, "content": 0}, {"index": 9, "contenttype": 3, "headtextalign": 0, "bodytextalign": 16, "datamember": "总销售", "printhead": false, "printseparatorline": false, "headfont": "\"仿宋, 9pt\"", "bodyfont": "\"仿宋, 9pt\"", "config": "[{\"col_head\":\"data\",\"col_dataMember\":\"data\",\"col_singleLine\":\"true\",\"col_isPrint\":\"true\",\"col_align\":\"Left\"}]", "displayname": "商品信息"}, {"index": 10, "contenttype": 5, "textalign": 0, "content": 0}, {"index": 11, "contenttype": 3, "headtextalign": 0, "bodytextalign": 0, "datamember": "会员充值", "printhead": false, "printseparatorline": false, "headfont": "\"仿宋, 9pt\"", "bodyfont": "\"仿宋, 9pt\"", "config": "[{\"col_head\":\"data\",\"col_dataMember\":\"data\",\"col_singleLine\":\"true\",\"col_isPrint\":\"true\",\"col_align\":\"Left\"}]", "displayname": "商品信息"}, {"index": 12, "contenttype": 5, "textalign": 0, "content": 0}, {"index": 13, "contenttype": 3, "headtextalign": 0, "bodytextalign": 16, "datamember": "应收现金", "printhead": false, "printseparatorline": false, "headfont": "\"仿宋, 9pt\"", "bodyfont": "\"仿宋, 9pt\"", "config": "[{\"col_head\":\"data\",\"col_dataMember\":\"data\",\"col_singleLine\":\"true\",\"col_isPrint\":\"true\",\"col_align\":\"Left\"}]", "displayname": "商品信息"}, {"index": 14, "contenttype": 5, "textalign": 0, "content": 0}, {"index": 15, "contenttype": 3, "headtextalign": 0, "bodytextalign": 16, "datamember": "支付统计", "printhead": false, "printseparatorline": false, "headfont": "\"仿宋, 9pt\"", "bodyfont": "\"仿宋, 9pt\"", "config": "[{\"col_head\":\"data\",\"col_dataMember\":\"data\",\"col_singleLine\":\"true\",\"col_isPrint\":\"true\",\"col_align\":\"Left\"}]", "displayname": "商品信息"}, {"index": 16, "contenttype": 5, "textalign": 0, "content": 0}, {"index": 17, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 7pt\"", "content": "打印时间", "datamember": "打印时间", "format": "打印时间：{0}", "defaultcontent": "", "displayname": "文本"}]