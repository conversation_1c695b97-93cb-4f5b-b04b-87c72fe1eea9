using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgUtils.Model
{
    public class LoginDataResponse
    {
        //public ActivationCode activationCode { get; set; }
        public string employeeNumber { get; set; }
        public int hasWd { get; set; }
        public int id { get; set; }

        public string name { get; set; }
        public string nickname { get; set; }
        public string phone { get; set; }
        public string privilege { get; set; }
        public string sysUid { get; set; }
        public string sysSid { get; set; }
        //public bool ultimate { get; set; }
        public string uid { get; set; }
        public string token { get; set; }
        public string username { get; set; }
        public string employeeRemember { get; set; }

        public List<ShopList> shopList { get; set; }
        public string fingerprint { get; set; } = Guid.NewGuid().ToString("N");
        public int deviceCode { get { return CommonApp.Config?.deviceCode ?? 0; } }
        //public string userremember { get; set; }

        //public string createTime { get; set; }
        //public string reviseTime { get; set; }

        //[JsonProperty("partitionId")]
        //public string partitionId { get; set; }
        //public string subName { get; set; }
        //public string syncTime { get; set; }

    }
    public class ShopList
    {
        public int id { get; set; }
        public string guid { get; set; }
        public string name { get; set; }
        public string addr { get; set; }
        public string industry { get; set; }
        public string contacter { get; set; }
        public string tel { get; set; }
        public string phone { get; set; }
        public string qq { get; set; }
        public string wechat { get; set; }
        [JsonProperty("createAt")]
        public string create_at { get; set; }
        [JsonProperty("reviseAt")]
        public string revise_at { get; set; }
        public string syncTime { get; set; }
        public string settings { get; set; }
        public string is_deleted { get; set; }
        [JsonProperty("discountSettings")]
        public string discount_settings { get; set; }
        [JsonProperty("syncV")]
        public string fingerprint { get; set; }
        [JsonProperty("isSync")]
        public int is_synced { get; set; }
        public List<LoginAuthList> loginAuthList { get; set; }
        
    }
    public class LoginAuthList
    {
        public int uid { get; set; }
        public string employeeNumber { get; set; }
        public string name { get; set; }
        public string password { get; set; }
        public string phone { get; set; }
        public string privilege { get; set; }
        public string privilegeFunctionName { get; set; }
        public string role { get; set; }
        public string status { get; set; }
    }
}
