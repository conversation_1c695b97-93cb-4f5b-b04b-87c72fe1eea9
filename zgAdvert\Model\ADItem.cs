﻿using System;
namespace zgAdvert.Model
{
	
	public class ADItem
	{
		public long id { get; set; }
		public string img { get; set; }
		public string comments { get; set; }
		public int index { get; set; }
		public int templateId { get; set; }
		public int duration { get; set; } = 15;
		public int location { get; set; }
		public Int16 isDel { get; set; }
		public string html { get; set; }
  //      public DateTime startDate { get; set; }
		//public DateTime endDate { get; set; }

		public string MaterialType { get; set; } = "";
		public bool IsPalying { get; set; }
		public Int64 IsDefault { get; set; } = 1;
        
    }
}
