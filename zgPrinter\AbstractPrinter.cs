﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using zgPrinter.Model;

namespace zgPrinter
{
    /// <summary>
    /// 打印抽象类
    /// </summary>
    public abstract class AbstractPrinter
    {
        /// <summary>
        /// 行高，指令打印时 为 n/144英寸。驱动打印时为像素
        /// </summary>
        public int LineHeight { get; set; } = 30;

        /// <summary>
        /// 打印元素
        /// </summary>
        public List<PrintElement> PrintItems { get; set; } = new List<PrintElement>();

        /// <summary>
        /// 数据源
        /// </summary>
        public Dictionary<string, object> DataSource { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 普通打印
        /// </summary>
        /// <param name="printerName"></param>
        /// <param name="printCount"></param>
        /// <param name="isTag"></param>
        /// <param name="spaceLine"></param>
        public abstract void Print(string printerName, int printCount, bool isTag = false,int spaceLine = 4);

        /// <summary>
        /// 普通打印
        /// </summary>
        /// <param name="printerName"></param>
        /// <param name="printCount"></param>
        /// <param name="isTag"></param>
        /// <param name="spaceLine"></param>
        public abstract void Print(string printerName, IEnumerable<Tuple<int, byte[]>> printData, int sleepMS);

        /// <summary>
        /// 网口打印
        /// </summary>
        /// <param name="ip"></param>
        /// <param name="printCount"></param>
        /// <param name="isTag"></param>
        /// <param name="port"></param>
        /// <param name="spaceLine"></param>
        public abstract void NetPrint(string ip, int printCount, bool isTag = false, int port = 9100, int spaceLine = 2);

        /// <summary>
        /// 网口批量打印
        /// </summary>
        /// <param name="ip">打印机IP</param>
        /// <param name="printData">打印内容，份数，打印数据</param>
        /// <param name="sleepMS">打印间隔毫秒数</param>
        /// <param name="port">打印机端口号，默认9100</param>
        public abstract void NetPrint(string ip, IEnumerable<Tuple<int, byte[]>> printData, int sleepMS, int port = 9100);

        /// <summary>
        /// 读取配置文件
        /// </summary>
        /// <param name="data"></param>
        public void LoadConfig(List<JObject> data)
        {
            var elementList = Convert2PrintItem(data);
            this.PrintItems = elementList;
        }

        public static List<PrintElement> Convert2PrintItem(List<JObject> data)
        {
            List<PrintElement> elementList = new List<PrintElement>();
            foreach (var item in data)
            {
                var index = item.GetValue("index").Value<int>();
                var contentType = (EnumContentType)item.GetValue("contenttype").Value<int>();
                var displayGroup = item.Value<string>("displaygroup");
                //处理文本类型
                if (EnumContentType.String == contentType)
                {
                    var textalign = (EnumTextAlign)item.GetValue("textalign").Value<int>();
                    var contentfont = JsonConvert.DeserializeObject<Font>(item.GetValue("contentfont").Value<string>());
                    var datamember = item.GetValue("datamember").Value<string>() ?? string.Empty;
                    var content = item.GetValue("content").Value<string>();
                    var format = item.GetValue("format").Value<string>();
                    var defaultcontent = item.GetValue("defaultcontent").Value<string>();
                    zgPrinter.Model.PrintElementString printElement = new zgPrinter.Model.PrintElementString(index, content, contentfont, textalign, true);
                    printElement.DataMember = datamember;
                    printElement.DisplayGroup = displayGroup;
                    printElement.Format = format;
                    if (item.GetValue("wordwarp") != null)
                    {
                        printElement.WordWrap = item.GetValue("wordwarp").Value<bool>();
                    }
                    elementList.Add(printElement);

                }

                //处理图片类型
                if (EnumContentType.Image == contentType)
                {
                    zgPrinter.Model.PrintElement element = JsonConvert.DeserializeObject<zgPrinter.Model.PrintElementImage>(item.ToString(Formatting.None));
                    element.DisplayGroup = displayGroup;
                    elementList.Add(element);
                }

                //处理命令类型
                if (EnumContentType.Command == contentType)
                {
                    zgPrinter.Model.PrintElement element = JsonConvert.DeserializeObject<zgPrinter.Model.PrintElementCommand>(item.ToString(Formatting.None));
                    element.DisplayGroup = displayGroup;
                    elementList.Add(element);
                }

                //处理商品表格
                if (EnumContentType.GoodsTable == contentType)
                {
                    var headtextalign = (EnumTextAlign)item.GetValue("headtextalign").Value<int>();
                    var bodytextalign = (EnumTextAlign)item.GetValue("bodytextalign").Value<int>();
                    var printHeader = item.GetValue("printhead").Value<bool>();
                    var printseparatorline = item.GetValue("printseparatorline").Value<bool>();
                    var headFont = JsonConvert.DeserializeObject<Font>(item.GetValue("headfont").Value<string>());
                    var bodyFont = JsonConvert.DeserializeObject<Font>(item.GetValue("bodyfont").Value<string>());
                    var config = JsonConvert.DeserializeObject<DataTable>(item.GetValue("config").Value<string>());
                    var dataMember = item.GetValue("datamember").Value<string>() ?? string.Empty;


                    var element = new zgPrinter.Model.PrintElementGoodsDataTable(index, null, config);
                    element.DataStruct = config;
                    element.HeadFont = headFont;
                    element.BodyFont = bodyFont;
                    element.HeadTextAlign = headtextalign;
                    element.BodyTextAlign = bodytextalign;
                    element.PrintHead = printHeader;
                    element.PrintSeparatorLine = printseparatorline;
                    element.DataMember = dataMember;
                    element.DisplayGroup = displayGroup;
                    elementList.Add(element);
                }
            }

            return elementList;
        }
    }
}
