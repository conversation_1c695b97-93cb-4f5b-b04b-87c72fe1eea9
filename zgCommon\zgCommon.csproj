﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{DB048F54-D858-4A6A-802D-05893309DEB9}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>zgCommon</RootNamespace>
    <AssemblyName>zgCommon</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\out\Programs\Libs\</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\out\Programs\Libs\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\out\Programs\Libs\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>
    </ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CefSharp">
      <HintPath>..\out\Programs\cefLib\CefSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="CefSharp.BrowserSubprocess.Core">
      <HintPath>..\out\Programs\cefLib\CefSharp.BrowserSubprocess.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="CefSharp.Core">
      <HintPath>..\out\Programs\cefLib\CefSharp.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="CefSharp.WinForms">
      <HintPath>..\out\Programs\cefLib\CefSharp.WinForms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="M2Mqtt.Net">
      <HintPath>..\out\Programs\Libs\M2Mqtt.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="NAudio, Version=1.10.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\out\Programs\Libs\NAudio.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.SQLite, Version=1.0.113.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\System.Data.SQLite.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Speech" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="TreChina.Printer">
      <HintPath>..\out\Programs\Libs\TreChina.Printer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zgAdvert">
      <HintPath>..\out\Programs\Libs\zgAdvert.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zgBalance, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\zgBalance.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zgCustomerDisplay">
      <HintPath>..\out\Programs\Libs\zgCustomerDisplay.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zgLogging">
      <HintPath>..\out\Programs\Libs\zgLogging.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zgPrinter, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\zgPrinter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zgReadCard">
      <HintPath>..\out\Programs\Libs\zgReadCard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zgScanerHook">
      <HintPath>..\out\Programs\Libs\zgScanerHook.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zgSerialPort">
      <HintPath>..\out\Programs\Libs\zgSerialPort.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zgSQLite">
      <HintPath>..\out\Programs\Libs\zgSQLite.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zgUtils">
      <HintPath>..\out\Programs\Libs\zgUtils.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zxing" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Browser\CommonBrowserFun.cs" />
    <Compile Include="Browser\CsharpHttps.cs" />
    <Compile Include="Browser\DownloadHandler.cs" />
    <Compile Include="Browser\Form\BrowserBack.cs" />
    <Compile Include="Browser\Form\BrowserBase.cs" />
    <Compile Include="Browser\Form\BrowserShow.cs" />
    <Compile Include="Browser\KeyValue.cs" />
    <Compile Include="Browser\MyDownLoadFile.cs" />
    <Compile Include="Browser\Notify.cs" />
    <Compile Include="Browser\ActionLog.cs" />
    <Compile Include="Browser\SQLitePlugin.cs" />
    <Compile Include="Browser\ZgPrinterBrowserFun.cs" />
    <Compile Include="Configs\App.cs" />
    <Compile Include="Configs\CefHelper.cs" />
    <Compile Include="Configs\Resources.cs" />
    <Compile Include="Controllers\AssemblyController.cs" />
    <Compile Include="Controllers\ConfigController.cs" />
    <Compile Include="Controllers\FileController.cs" />
    <Compile Include="Controllers\ProgramController.cs" />
    <Compile Include="Controllers\ProgramIconControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controllers\ProgramIconControl.Designer.cs">
      <DependentUpon>ProgramIconControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Controllers\UnityMessageFilter.cs" />
    <Compile Include="Controllers\UnityModule.cs" />
    <Compile Include="DesktopForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DesktopForm.Designer.cs">
      <DependentUpon>DesktopForm.cs</DependentUpon>
    </Compile>
    <Compile Include="EventHandlers\AppDomainExceptionHandler.cs" />
    <Compile Include="EventHandlers\ApplicationExitHandler.cs" />
    <Compile Include="EventHandlers\ApplicationThreadExceptionHandler.cs" />
    <Compile Include="EventHandlers\ChromiumDisableMenuHandler.cs" />
    <Compile Include="EventHandlers\ChromiumRegisterResourceHandler.cs" />
    <Compile Include="EventHandlers\ChromiumWebBrowserLoadHandler.cs" />
    <Compile Include="EventHandlers\ChromiumWebBrowserMessageHandler.cs" />
    <Compile Include="EventHandlers\ResourceSchemeHandler.cs" />
    <Compile Include="EventHandlers\ResourceSchemeHandlerFactory.cs" />
    <Compile Include="FormBack.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormBack.Designer.cs">
      <DependentUpon>FormBack.cs</DependentUpon>
    </Compile>
    <Compile Include="FormMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormMain.Designer.cs">
      <DependentUpon>FormMain.cs</DependentUpon>
    </Compile>
    <Compile Include="FormShow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormShow.Designer.cs">
      <DependentUpon>FormShow.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Templates\FormCollection.cs" />
    <Compile Include="Templates\ProgramTemplateClass.cs" />
    <EmbeddedResource Include="Controllers\ProgramIconControl.resx">
      <DependentUpon>ProgramIconControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DesktopForm.resx">
      <DependentUpon>DesktopForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormBack.resx">
      <DependentUpon>FormBack.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormMain.resx">
      <DependentUpon>FormMain.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormShow.resx">
      <DependentUpon>FormShow.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="..\industry\**\*.*">
      <Link>Resources\www.zgpos.com\%(RecursiveDir)%(Filename)%(Extension)</Link>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>