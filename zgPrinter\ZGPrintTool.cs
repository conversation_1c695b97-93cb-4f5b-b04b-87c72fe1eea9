﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Xml;
using System.Xml.Linq;
using zgPrinter.Model;
using zgPrinter.Model.ZgLabel;
using zgPrinter.Printer.DrawImg;
using zgPrinter.Printer.ESCPOS;
using zgPrinter.Printer.ESCPOS.EpsonCommands;
using zgPrinter.Printer.ESCPOS.Interface;
using zgPrinter.Printer.TSPL;
using zgPrinter.Printer.Usb;
using zgPrinter.Properties;

namespace zgPrinter
{
    public class ZGPrintTool
    {
        #region Property

        /// <summary>
        /// 默认空行数
        /// </summary>
        private const int DEFAULT_SPACE_LINE = 4;

        /// <summary>
        /// 打印纸张尺寸类型
        /// </summary>
        EnumReceiptPaperSize enumReceiptPaperSize;
        private static SemaphoreSlim _semaphore = new SemaphoreSlim(1);

        /// <summary>
        /// 购物小票打印模板
        /// </summary>
        public List<PrintElement> ShppingReceiptElement { get; set; }

        private readonly UsbPrinter _usbPrinter = new UsbPrinter();

        private readonly string customerTemplateSavePath = Path.Combine(System.Environment.CurrentDirectory, "template", "bill");

        #endregion

        public ZGPrintTool()
        {
        }

        /// <summary>
        /// 基础打印 - 绘图
        /// </summary>
        /// <param name="dataSource"></param>
        /// <param name="templateName"></param>
        /// <param name="printerName"></param>
        /// <param name="printCount"></param>
        public void PrintCommonReceiptGraphics(JObject dataSource, string templateName, string printerName, int printCount = 1)
        {
            _semaphore.Wait();
            try
            {
                var spaceLine = dataSource["spaceline"] != null ? dataSource.Value<int>("spaceline") : DEFAULT_SPACE_LINE;
                enumReceiptPaperSize = (EnumReceiptPaperSize)dataSource.Value<int>("cols");
                var strLineHeight = dataSource["lineheight"] != null ? dataSource.Value<string>("lineheight") : "4";
                var paperSize = getPaperSize(printerName);
                var dicDataSource = convert2DataSourceBase(dataSource);

                //Resource.ResourceManager.GetObject(templateName);
                //var data = JsonConvert.DeserializeObject<List<JObject>>(Resource.ResourceManager.GetString(templateName));
                var data = JsonConvert.DeserializeObject<List<JObject>>(GetTemplateContent(templateName));

                var printer = new GraphicsPrinter(paperSize);
                printer.SpaceLine = spaceLine;
                printer.DataSource = dicDataSource;
                printer.PrintItems = AbstractPrinter.Convert2PrintItem(data);
                printer.LineHeight = Convert.ToInt32(strLineHeight) / 15;
                printer.Print(printerName, printCount, spaceLine: spaceLine);
            }
            catch (Exception ex)
            {

            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 基础打印 - ESCPOS
        /// </summary>
        /// <param name="dataSource"></param>
        /// <param name="templateName"></param>
        /// <param name="printerName"></param>
        /// <param name="printCount"></param>
        public void PrintCommonReceiptESCPOS(JObject dataSource, string templateName, string printerName, int printCount = 1)
        {
            var spaceLine = dataSource["spaceline"] != null ? dataSource.Value<int>("spaceline") : DEFAULT_SPACE_LINE;
            var ipAddress = dataSource["ip"] != null ? dataSource.Value<string>("ip") : string.Empty;
            var strLineHeight = dataSource["lineheight"] != null ? dataSource.Value<string>("lineheight") : "60";
            var dicDataSource = convert2DataSourceBase(dataSource);
            //var data = JsonConvert.DeserializeObject<List<JObject>>(Resource.ResourceManager.GetString(templateName));
            var data = JsonConvert.DeserializeObject<List<JObject>>(GetTemplateContent(templateName));

            var printer = new ESCPOSPrinter();
            printer.DataSource = dicDataSource;
            printer.PrintItems = AbstractPrinter.Convert2PrintItem(data);
            printer.LineHeight = Convert.ToInt32(strLineHeight);
            if (string.IsNullOrEmpty(ipAddress))
            {
                var printerNameArray = printerName.Split(':');
                //USB免驱打印
                if (printerNameArray.Length == 2 && "USB".Equals(printerNameArray[0]))
                {
                    var usbPrinterPath = printerNameArray[1];
                    var byteArray = printer.ConvertPrintItems2ByteArray(spaceLine, cutPaper: true);
                    _usbPrinter.Print(usbPrinterPath, byteArray, printCount);
                }
                else
                {
                    //usb驱动打印
                    printer.Print(printerName, printCount, spaceLine: spaceLine);
                }
            }
            else
            {
                //网口打印
                printer.NetPrint(ipAddress, printCount, spaceLine: spaceLine);
            }
        }

        public void PrintCommonReceiptESCPOS(JObject dataSource, List<PrintElement> template, string printerName, int printCount = 1)
        {
            var ipAddress = dataSource["ip"] != null ? dataSource.Value<string>("ip") : string.Empty;
            var strLineHeight = dataSource["lineheight"] != null ? dataSource.Value<string>("lineheight") : "60";
            var dicDataSource = convert2DataSourceBase(dataSource);

            var printer = new ESCPOSPrinter();
            printer.DataSource = dicDataSource;
            printer.PrintItems = template;
            printer.LineHeight = Convert.ToInt32(strLineHeight);
            if (string.IsNullOrEmpty(ipAddress))
            {
                var printerNameArray = printerName.Split(':');
                //USB免驱打印
                if (printerNameArray.Length == 2 && "USB".Equals(printerNameArray[0]))
                {
                    var usbPrinterPath = printerNameArray[1];
                    var byteArray = printer.ConvertPrintItems2ByteArray(cutPaper: true);
                    _usbPrinter.Print(usbPrinterPath, byteArray, printCount);
                }
                else
                {
                    //usb驱动打印
                    printer.Print(printerName, printCount, spaceLine: 5);
                }
            }
            else
            {
                //网口打印
                printer.NetPrint(ipAddress, printCount);
            }
        }


        public void PrintCommonReceiptESCPOS(PrintConfig config, JArray dataSourceArray)
        {
            var printer = new ESCPOSPrinter();
            //var templateContent = JsonConvert.DeserializeObject<List<JObject>>(Resource.ResourceManager.GetString(config.Template));
            var templateContent = JsonConvert.DeserializeObject<List<JObject>>(GetTemplateContent(config.Template));
            var printItems = AbstractPrinter.Convert2PrintItem(templateContent);
            var dataSourceBuffer = dataSourceArray.Select(i =>
            {
                var dataSource = convert2DataSourceBase(JObject.FromObject(i));
                printer.DataSource = dataSource;
                printer.PrintItems = printItems;
                printer.LineHeight = config.LineHeight;
                var printData = printer.ConvertPrintItems2ByteArray(config.SpaceLine, true);
                printer.ClearBuffer();
                return new Tuple<int, byte[]>(config.PrintCount, printData);
            });

            if ("ip".Equals(config.PrintType))
            {
                printer.NetPrint(config.IP, dataSourceBuffer, config.IntervalTime);
            }
            if ("usb".Equals(config.PrintType))
            {
                //usb驱动打印
                printer.Print(config.Printer, dataSourceBuffer, config.IntervalTime);
            }
        }

        /// <summary>
        /// 打印购物小票
        /// </summary>
        /// <param name="dataSource">数据源</param>
        /// <param name="printCount">打印份数</param>
        public void PrintReceipt(JObject dataSource, int printCount)
        {
            enumReceiptPaperSize = (EnumReceiptPaperSize)dataSource.Value<int>("cols");
            EnumReceiptType enumReceiptType = (EnumReceiptType)dataSource.Value<int>("printMode");
            var printerName = dataSource.Value<string>("printername");
            var method = this.GetType().GetMethod(string.Format("print{0}", enumReceiptType.ToString()));
            method.Invoke(this, new object[] { dataSource, printerName, printCount });
        }

        /// <summary>
        /// 小票打印-escpos
        /// </summary>
        /// <param name="dataSource"></param>
        /// <param name="printCount"></param>
        public void printShoppingReceiptESCPOS(JObject dataSource, string printerName, int printCount = 1)
        {
            var ipAddress = dataSource["ip"] != null ? dataSource.Value<string>("ip") : string.Empty;
            var strLineHeight = dataSource["lineheight"] != null ? dataSource.Value<string>("lineheight") : "60";
            var dicDataSource = convertShoppingReceiptDataSource(dataSource);

            var printer = new ESCPOSPrinter();
            printer.DataSource = dicDataSource;
            printer.PrintItems = this.ShppingReceiptElement;
            printer.LineHeight = Convert.ToInt32(strLineHeight);
            if (string.IsNullOrEmpty(ipAddress))
            {
                printer.Print(printerName, printCount);
            }
            else
            {
                printer.NetPrint(ipAddress, printCount);
            }
        }

        /// <summary>
        /// 小票打印-画布
        /// </summary>
        /// <param name="dataSource"></param>
        /// <param name="printCount"></param>
        public void printShoppingReceiptGraphics(JObject dataSource, string printerName, int printCount = 1)
        {
            var strLineHeight = dataSource["lineheight"] != null ? dataSource.Value<string>("lineheight") : "4";
            var paperSize = getPaperSize(printerName);
            var dicDataSource = convertShoppingReceiptDataSource(dataSource);

            var data = JsonConvert.DeserializeObject<List<JObject>>(Resource.shoppingReceiptTemplate);
            var customPageSize = new PaperSize("custom", paperSize.Width, paperSize.Height * 2);
            var printer = new GraphicsPrinter(customPageSize);
            printer.DataSource = dicDataSource;
            printer.PrintItems = AbstractPrinter.Convert2PrintItem(data);
            printer.LineHeight = Convert.ToInt32(strLineHeight) / 15;
            printer.Print(printerName, printCount);
        }

        /// <summary>
        /// 商品销售报表ESCPOS
        /// </summary>
        /// <param name="dataSource"></param>
        /// <param name="printerName"></param>
        /// <param name="printCount"></param>
        public void printGoodsSalesReportESCPOS(JObject dataSource, string printerName, int printCount = 1)
        {
            var ipAddress = dataSource["ip"] != null ? dataSource.Value<string>("ip") : string.Empty;
            var strLineHeight = dataSource["lineheight"] != null ? dataSource.Value<string>("lineheight") : "60";
            var dicDataSource = convert2DataSourceBase(dataSource);

            var data = JsonConvert.DeserializeObject<List<JObject>>(Resource.ResourceManager.GetString("goodsSalesReportTemplate"));
            var printer = new ESCPOSPrinter();
            printer.DataSource = dicDataSource;
            printer.PrintItems = AbstractPrinter.Convert2PrintItem(data);
            printer.LineHeight = Convert.ToInt32(strLineHeight);
            if (string.IsNullOrEmpty(ipAddress))
            {
                printer.Print(printerName, printCount);
            }
            else
            {
                printer.NetPrint(ipAddress, printCount);
            }
        }

        /// <summary>
        /// 商品销售报表ESCPOS
        /// </summary>
        /// <param name="dataSource"></param>
        /// <param name="printerName"></param>
        /// <param name="printCount"></param>
        public void printSalesReportESCPOS(JObject dataSource, string printerName, int printCount = 1)
        {
            var ipAddress = dataSource["ip"] != null ? dataSource.Value<string>("ip") : string.Empty;
            var strLineHeight = dataSource["lineheight"] != null ? dataSource.Value<string>("lineheight") : "60";
            var dicDataSource = convert2DataSourceBase(dataSource);

            var data = JsonConvert.DeserializeObject<List<JObject>>(Resource.ResourceManager.GetString("salesReportTemplate"));
            var printer = new ESCPOSPrinter();
            printer.DataSource = dicDataSource;
            printer.PrintItems = AbstractPrinter.Convert2PrintItem(data);
            printer.LineHeight = Convert.ToInt32(strLineHeight);
            if (string.IsNullOrEmpty(ipAddress))
            {
                printer.Print(printerName, printCount);
            }
            else
            {
                printer.NetPrint(ipAddress, printCount);
            }
        }

        public void OpenDrawer(string printerName)
        {
            var bytes = new Drawer().Open();
            var printerNameArray = printerName.Split(':');
            //USB免驱打印
            if (printerNameArray.Length == 2 && "USB".Equals(printerNameArray[0]))
            {
                var usbPrinterPath = printerNameArray[1];
                _usbPrinter.Print(usbPrinterPath, bytes, 1);
            }
            else
            {
                RawPrinterHelper.SendBytesToPrinter(printerName, bytes);
            }
        }

        /// <summary>
        /// 整理数据源-基础
        /// </summary>
        /// <param name="zgData"></param>
        /// <returns></returns>
        private Dictionary<string, object> convert2DataSourceBase(JObject zgData)
        {
            Dictionary<string, object> dataSource = new Dictionary<string, object>();
            foreach (var item in zgData)
            {
                if (item.Value is JArray)
                {
                    DataTable table = new DataTable();

                    var jArray = item.Value as JArray;
                    var firstRow = jArray[0] as JObject;
                    foreach (var p in firstRow.Children<JProperty>())
                    {
                        table.Columns.Add(p.Name);
                    }

                    foreach (JObject i in jArray)
                    {
                        var newRow = table.NewRow();

                        foreach (var p in i.Children<JProperty>())
                        {
                            newRow[p.Name] = p.Value.ToString();
                        }

                        table.Rows.Add(newRow);
                    }
                    dataSource[item.Key] = table;
                }
                else
                {
                    dataSource[item.Key] = zgData.GetValue(item.Key);
                }
            }
            return dataSource;
        }

        /// <summary>
        /// 小票打印数据源整理
        /// </summary>
        /// <param name="zgData"></param>
        /// <returns></returns>
        private Dictionary<string, object> convertShoppingReceiptDataSource(JObject zgData)
        {
            Dictionary<string, object> dataSource = new Dictionary<string, object>();
            foreach (var item in zgData)
            {
                if ("groups".Equals(item.Key))
                {
                    DataTable goods = new DataTable();
                    foreach (var i in item.Value)
                    {
                        var jp = (JProperty)i;
                        var rowData = jp.Value.ToString().Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                        if (rowData.Length == 0)
                        {
                            continue;
                        }
                        if ("l_0".Equals(jp.Name))
                        {
                            var column = rowData.Select(j => new DataColumn(j)).ToArray();
                            goods.Columns.AddRange(column);
                        }
                        else
                        {
                            var row = goods.NewRow();
                            for (int j = 0; j < rowData.Length; j++)
                            {
                                row[j] = rowData[j];
                            }
                            goods.Rows.Add(row);
                        }
                    }
                    dataSource[item.Key] = goods;
                }
                else
                {
                    dataSource[item.Key] = zgData.GetValue(item.Key);
                }
            }
            if (dataSource.ContainsKey("logo"))
            {
                var logoPath = dataSource["logo"].ToString();
                if (!string.IsNullOrEmpty(logoPath))
                {
                    dataSource["logo"] = new Bitmap(logoPath);
                }
                else
                {
                    dataSource.Remove("logo");
                }
            }
            if (dataSource.ContainsKey("qrcode"))
            {
                var qrcodePath = dataSource["qrcode"].ToString();
                if (!string.IsNullOrEmpty(qrcodePath))
                {
                    dataSource["qrcode"] = new Bitmap(qrcodePath);
                }
                else
                {
                    dataSource.Remove("qrcode");
                }
            }

            return dataSource;
        }

        /// <summary>
        /// 获取打印纸张
        /// </summary>
        /// <param name="printerName"></param>
        /// <returns></returns>
        private PaperSize getPaperSize(string printerName)
        {
            Graphics currentGraphics = Graphics.FromHwnd(IntPtr.Zero);
            var dpi = (int)currentGraphics.DpiX;

            PrintDocument printDoc = new PrintDocument();
            printDoc.PrinterSettings.PrinterName = printerName;
            foreach (PaperSize pz in printDoc.PrinterSettings.PaperSizes)
            {
                float widthMM = pz.Width * 25.4F / 100;
                if (enumReceiptPaperSize == EnumReceiptPaperSize.Paper50 && widthMM < 60)
                {
                    return pz;
                }
                if (enumReceiptPaperSize == EnumReceiptPaperSize.Paper80 && widthMM > 60 && widthMM < 90)
                {
                    return pz;
                }
            }

            throw new Exception("没有对应的纸张");
        }

        /// <summary>
        /// 获取模板内容，如果没有外挂的模板内容，则使用资源文件内的模板。
        /// </summary>
        /// <param name="templateName"></param>
        /// <returns></returns>
        private string GetTemplateContent(string templateName)
        {
            try
            {
                var templateContent = string.Empty;
                var templatePath = Path.Combine(this.customerTemplateSavePath, templateName + ".txt");
                if (File.Exists(templatePath))
                {
                    templateContent = File.ReadAllText(templatePath);
                }
                else
                {
                    templateContent = Resource.ResourceManager.GetString(templateName);
                }
                return templateContent;
            }
            catch (Exception ex)
            {
                return Resource.ResourceManager.GetString(templateName);
            }
            return string.Empty;
        }

        /// <summary>
        /// 标签打印
        /// </summary>
        /// <param name="dataSource"></param>
        /// <param name="printCount"></param>
        public void PrintLabel(byte[] dataSource, string printerName)
        {
            var printer = new UsbPrinter();
            printer.Print(printerName, dataSource, 1);
        }

        /// <summary>
        /// 自上而下打印标签内容
        /// </summary>
        /// <param name="printdata"></param>
        public void PrintLabelsSequentially(ZgLabel printdata)
        {
            int lineHeight = 10;
            int mmDots = 8;
            int textHeight = 24;
            int yOffset = printdata.YOffset+10;
            int rotation = 0;
            var lineCharCount = (printdata.Width * mmDots / 12) - 1;
            var printerNameArray = printdata.PrinterName.Split(':');

            var p = new TsplPrinter();
            p.SetSize($"{printdata.Width}mm", $"{printdata.Height}mm")
                .SetGap($"{printdata.GapM}mm")
                .SetDirection(printdata.Direction)
                .SetReference(0,0)
               // .Home()
                .CLS();


            foreach (var item in printdata.Items)
            {
                if (item.Type.Equals("text"))
                {
                    var xm = Convert.ToInt32(item.FontSize[0].ToString());
                    var ym = Convert.ToInt32(item.FontSize[1].ToString());
                    if (string.IsNullOrEmpty(item.Text))
                    {
                        yOffset += textHeight;
                        continue;
                    }
                    var lines = SplitText(item.Text, lineCharCount/xm);
                    //p.PrintText(printdata.XOffset, yOffset, item.Text, rotation, xm, ym);
                    //yOffset += (textHeight * ym);

                    foreach (var line in lines)
                    {
                        p.PrintText(printdata.XOffset, yOffset, line, rotation, xm, ym);
                        yOffset += (textHeight * ym);
                    }
                }
                else if (item.Type.Equals("barcode"))
                {
                    p.PrintBarCode(printdata.XOffset + 24, yOffset, printdata.BarcodeHeiht, "128", item.Text);
                    yOffset += (printdata.BarcodeHeiht + lineHeight + 10);
                }
                yOffset += lineHeight;
            }//140
            var cmdlist = p.Finish(printdata.PrintCount);
            //USB免驱打印
            if (printerNameArray.Length == 2 && "USB".Equals(printerNameArray[0]))
            {
                var usbPrinterPath = printerNameArray[1];
                _usbPrinter.Print(usbPrinterPath, cmdlist, 1);
            }
            else
            {
                RawPrinterHelper.SendBytesToPrinter(printdata.PrinterName, cmdlist);
            }
        }

        /// <summary>
        /// 自上而下打印标签内容
        /// </summary>
        /// <param name="printdata"></param>
        public void PrintLabelsSequentiallyVertical(ZgLabel printdata)
        {
            int lineHeight = 10;
            int mmDots = 8;
            int textHeight = 24;
            int yOffset = printdata.XOffset;
            int xOffset = printdata.Width * mmDots - 19 - printdata.YOffset;
            var lineCharCount = (printdata.Height * mmDots / 12) - 1;
            var printerNameArray = printdata.PrinterName.Split(':');
            var p = new TsplPrinter();
            p.SetSize($"{printdata.Width}mm", $"{printdata.Height}mm")
                .SetGap($"{printdata.GapM}mm")
                .SetDirection(printdata.Direction)
                .SetReference(0, 0)
                .CLS();


            foreach (var item in printdata.Items)
            {
                if (item.Type.Equals("text"))
                {
                    if (string.IsNullOrEmpty(item.Text))
                    {
                        xOffset -= textHeight;
                        continue;
                    }
                    var lines = SplitText(item.Text, lineCharCount);
                    var xm = Convert.ToInt32(item.FontSize[0].ToString());
                    var ym = Convert.ToInt32(item.FontSize[1].ToString());
                    foreach (var line in lines)
                    {
                        p.PrintText(xOffset, yOffset, line, printdata.Rotation, xm, ym);
                        xOffset -= (textHeight * ym);
                    }
                }
                else if (item.Type.Equals("barcode"))
                {
                    p.PrintBarCode(xOffset, yOffset + 12, printdata.BarcodeHeiht, "128", item.Text, printdata.Rotation);
                    xOffset -= (printdata.BarcodeHeiht + lineHeight);
                }
                xOffset -= lineHeight;
            }//140
            var cmdlist = p.Finish(printdata.PrintCount);
            //USB免驱打印
            if (printerNameArray.Length == 2 && "USB".Equals(printerNameArray[0]))
            {
                var usbPrinterPath = printerNameArray[1];
                _usbPrinter.Print(usbPrinterPath, cmdlist, 1);
            }
            else
            {
                RawPrinterHelper.SendBytesToPrinter(printdata.PrinterName, cmdlist);
            }
        }

        private ZgLabel _currentZgLabel = null;
        //public void PrintLabelsSequentially2(ZgLabel printdata)
        //{
        //    this._currentZgLabel = printdata;
        //    // PrintDocument.print里面，graphics.PageUnit = GraphicsUnit.Millimeter    
        //    var paperWidthPx = (int)SetupAPINativeMethods.MM2PX(printdata.Width);
        //    var paperHeightPx = (int)SetupAPINativeMethods.MM2PX(printdata.Height);
        //    PrintDocument printDocument = new PrintDocument();
        //    printDocument.PrinterSettings.PrinterName = printdata.PrinterName;
        //    printDocument.PrinterSettings.Copies = (short)printdata.PrintCount;
        //    printDocument.DefaultPageSettings.PaperSize = new PaperSize("LabelPaper", paperWidthPx, paperHeightPx);
        //    printDocument.PrintPage += PrintDocument_PrintPage;
        //    printDocument.PrintController = new StandardPrintController();
        //}

        //private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        //{
        //    var graphics = e.Graphics;
        //    var printableArea = e.PageSettings.PrintableArea;
        //    var yOffset = 0f + _currentZgLabel.YOffset;
        //    ZgLabelPrinter printer = new ZgLabelPrinter(graphics, printableArea.Width - _currentZgLabel.XOffset, printableArea.Height);

        //    foreach (var item in _currentZgLabel.Items)
        //    {
        //        item.X += _currentZgLabel.XOffset;
        //        item.Y = yOffset;
        //        if ("text".Equals(item.Type?.ToLower()))
        //        {
        //            yOffset += printer.PrintString(item);
        //        }
        //        else if ("barcode".Equals(item.Type?.ToLower()))
        //        {
        //            yOffset += printer.PrintBarcode(item, 140);
        //        }
        //    }
        //}

        private List<string> SplitText(string input, int maxByteLength)
        {
            List<string> lines = new List<string>();

            int startIndex = 0;
            while (startIndex < input.Length)
            {
                int byteCount = 0;
                int endIndex = startIndex;

                // 统计从 startIndex 开始的字符，直到达到最大字节长度
                while (endIndex < input.Length)
                {
                    byteCount += Encoding.Default.GetByteCount(input[endIndex].ToString());
                    if (byteCount > maxByteLength)
                    {
                        break;
                    }

                    endIndex++;
                }

                // 将拆分的内容添加到结果列表中
                lines.Add(input.Substring(startIndex, endIndex - startIndex));

                startIndex = endIndex;
            }

            return lines;
        }

        public ZgLabel Convert2ZgLabel(XDocument xmlDoc)
        {
            var elements = xmlDoc.Root.Elements();
            var configNode = xmlDoc.Root.Element("TCONFIG");
            var label = configNode.ConvertXmlNode<ZgLabel>();
            foreach (var element in elements)
            {
                if ("TCONFIG".Equals(element.Name.ToString()))
                {
                    continue;
                }
                var labelItem = element.ConvertXmlElement();
                label.Items.Add(labelItem);
            }
            return label;
        }


    }
}
