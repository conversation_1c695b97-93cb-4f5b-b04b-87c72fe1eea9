﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgSyncData.Model.Catering
{
    public class OrderFeed
    {
		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "chargeUnit")]
		public string ChargeUnit { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "createAt")]
		public string CreateAt { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "createBy")]
		public string CreateBy { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "discount")]
		public string Discount { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "discountMoney")]
		public decimal DiscountMoney { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "fingerprint")]
		public string Fingerprint { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "goodsFingerprint")]
		public string GoodsFingerprint { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "goodsName")]
		public string GoodsName { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "id")]
		public int Id { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "info1")]
		public string Info1 { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "info2")]
		public string Info2 { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "isDel")]
		public int IsDel { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "isStockManagement")]
		public int IsStockManagement { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "orderItemFingerprint")]
		public string OrderItemFingerprint { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "price")]
		public decimal Price { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "quantity")]
		public decimal Quantity { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "remark")]
		public string Remark { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "reviseAt")]
		public string ReviseAt { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "reviseBy")]
		public string ReviseBy { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "specs")]
		public string Specs { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "syncAt")]
		public string SyncAt { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "totalMoney")]
		public decimal TotalMoney { get; set; }

	}
}
