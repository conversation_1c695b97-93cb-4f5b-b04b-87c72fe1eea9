sonar.projectKey=POSVueChrome
sonar.projectName=POSVueChrome
sonar.projectVersion=1.9
sonar.working.directory=C:\\Users\\<USER>\\Documents\\POSVueChrome\\.sonarqube\\out\\.sonar
sonar.projectBaseDir=C:\\Users\\<USER>\\Documents\\POSVueChrome

82C5360C-EC89-4621-A783-A39337F7A3E0.sonar.projectKey=POSVueChrome:82C5360C-EC89-4621-A783-A39337F7A3E0
82C5360C-EC89-4621-A783-A39337F7A3E0.sonar.projectName=ESCPOS.Printer
82C5360C-EC89-4621-A783-A39337F7A3E0.sonar.projectBaseDir=C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer
82C5360C-EC89-4621-A783-A39337F7A3E0.sonar.sourceEncoding=utf-8
82C5360C-EC89-4621-A783-A39337F7A3E0.sonar.sources=\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Commands\\Alignment.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Commands\\BarCode.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Commands\\Drawer.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Commands\\EscPos.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Commands\\FontMode.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Commands\\FontHeight.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Commands\\FontWidth.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Commands\\Image.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Commands\\InitializePrint.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Commands\\LineHeight.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Commands\\PaperCut.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Commands\\QrCode.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Enums\\Fonts.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Enums\\Justifications.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Enums\\Positions.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Enums\\PrinterModeState.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Enums\\QrCodeSize.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Extensions\\PrinterExtensions.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Helper\\RawPrinterHelper.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Interfaces\\Command\\IAlignment.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Interfaces\\Command\\IBarCode.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Interfaces\\Command\\IDrawer.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Interfaces\\Command\\IFontMode.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Interfaces\\Command\\IFontHeight.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Interfaces\\Command\\IFontWidth.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Interfaces\\Command\\IImage.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Interfaces\\Command\\IInitializePrint.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Interfaces\\Command\\ILineHeight.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Interfaces\\Command\\IPaperCut.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Interfaces\\Command\\IPrintCommand.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Interfaces\\Command\\IQrCode.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Interfaces\\Printer\\IPrinter.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\POSPrinter.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\PrintNomalClass.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Properties\\AssemblyInfo.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\ESCPOS.Printer\\Icon.ico"

82C5360C-EC89-4621-A783-A39337F7A3E0.sonar.cs.roslyn.reportFilePath=C:\\Users\\<USER>\\Documents\\POSVueChrome\\bin\\x86\\Debug\\ESCPOS.Printer.dll.RoslynCA.json
82C5360C-EC89-4621-A783-A39337F7A3E0.sonar.cs.analyzer.projectOutPath=C:\\Users\\<USER>\\Documents\\POSVueChrome\\.sonarqube\\out\\0
82C5360C-EC89-4621-A783-A39337F7A3E0.sonar.cs.analyzer.projectOutPaths=\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\.sonarqube\\out\\0"
82C5360C-EC89-4621-A783-A39337F7A3E0.sonar.cs.roslyn.reportFilePaths=\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\bin\\x86\\Debug\\ESCPOS.Printer.dll.RoslynCA.json"

82C5360C-EC89-4621-A783-A39337F7A3E0.sonar.working.directory=C:\\Users\\<USER>\\Documents\\POSVueChrome\\.sonarqube\\out\\.sonar\\mod0
B04C2D59-4E43-4CFD-B2DF-F73B51472449.sonar.projectKey=POSVueChrome:B04C2D59-4E43-4CFD-B2DF-F73B51472449
B04C2D59-4E43-4CFD-B2DF-F73B51472449.sonar.projectName=TreChina.Printer
B04C2D59-4E43-4CFD-B2DF-F73B51472449.sonar.projectBaseDir=C:\\Users\\<USER>\\Documents\\POSVueChrome\\TreChina.Printer
B04C2D59-4E43-4CFD-B2DF-F73B51472449.sonar.sourceEncoding=utf-8
B04C2D59-4E43-4CFD-B2DF-F73B51472449.sonar.sources=\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\TreChina.Printer\\Helper\\BarCodeHelper.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\TreChina.Printer\\Helper\\PrintQueueHelper.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\TreChina.Printer\\Internal\\Printer.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\TreChina.Printer\\Internal\\Printer_70.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\TreChina.Printer\\IPrinter.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\TreChina.Printer\\Models\\FontSize.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\TreChina.Printer\\Models\\paperWidth.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\TreChina.Printer\\PrinterFactory.cs"

B04C2D59-4E43-4CFD-B2DF-F73B51472449.sonar.cs.roslyn.reportFilePath=C:\\Users\\<USER>\\Documents\\POSVueChrome\\TreChina.Printer\\bin\\Debug\\net452\\TreChina.Printer.dll.RoslynCA.json
B04C2D59-4E43-4CFD-B2DF-F73B51472449.sonar.cs.analyzer.projectOutPath=C:\\Users\\<USER>\\Documents\\POSVueChrome\\.sonarqube\\out\\1
B04C2D59-4E43-4CFD-B2DF-F73B51472449.sonar.cs.analyzer.projectOutPaths=\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\.sonarqube\\out\\1"
B04C2D59-4E43-4CFD-B2DF-F73B51472449.sonar.cs.roslyn.reportFilePaths=\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\TreChina.Printer\\bin\\Debug\\net452\\TreChina.Printer.dll.RoslynCA.json"

B04C2D59-4E43-4CFD-B2DF-F73B51472449.sonar.working.directory=C:\\Users\\<USER>\\Documents\\POSVueChrome\\.sonarqube\\out\\.sonar\\mod1
8FD02983-B60C-4D40-80EF-7F373BC8E50B.sonar.projectKey=POSVueChrome:8FD02983-B60C-4D40-80EF-7F373BC8E50B
8FD02983-B60C-4D40-80EF-7F373BC8E50B.sonar.projectName=XorPay.SDK
8FD02983-B60C-4D40-80EF-7F373BC8E50B.sonar.projectBaseDir=C:\\Users\\<USER>\\Documents\\POSVueChrome\\XorPay.SDK
8FD02983-B60C-4D40-80EF-7F373BC8E50B.sonar.sourceEncoding=utf-8
8FD02983-B60C-4D40-80EF-7F373BC8E50B.sonar.sources=\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\XorPay.SDK\\JsonHelper.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\XorPay.SDK\\LogHelper.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\XorPay.SDK\\PayConfig.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\XorPay.SDK\\PayCore.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\XorPay.SDK\\PayModel.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\XorPay.SDK\\PayRequest.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\XorPay.SDK\\Properties\\AssemblyInfo.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\XorPay.SDK\\NLog.config",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\XorPay.SDK\\NLog.xsd",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\XorPay.SDK\\packages.config"

8FD02983-B60C-4D40-80EF-7F373BC8E50B.sonar.cs.roslyn.reportFilePath=C:\\Users\\<USER>\\Documents\\POSVueChrome\\XorPay.SDK\\bin\\Debug\\XorPay.SDK.dll.RoslynCA.json
8FD02983-B60C-4D40-80EF-7F373BC8E50B.sonar.cs.analyzer.projectOutPath=C:\\Users\\<USER>\\Documents\\POSVueChrome\\.sonarqube\\out\\2
8FD02983-B60C-4D40-80EF-7F373BC8E50B.sonar.cs.analyzer.projectOutPaths=\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\.sonarqube\\out\\2"
8FD02983-B60C-4D40-80EF-7F373BC8E50B.sonar.cs.roslyn.reportFilePaths=\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\XorPay.SDK\\bin\\Debug\\XorPay.SDK.dll.RoslynCA.json"

8FD02983-B60C-4D40-80EF-7F373BC8E50B.sonar.working.directory=C:\\Users\\<USER>\\Documents\\POSVueChrome\\.sonarqube\\out\\.sonar\\mod2
827F565E-942B-4002-AE87-E4191012E1B2.sonar.projectKey=POSVueChrome:827F565E-942B-4002-AE87-E4191012E1B2
827F565E-942B-4002-AE87-E4191012E1B2.sonar.projectName=Plugin
827F565E-942B-4002-AE87-E4191012E1B2.sonar.projectBaseDir=C:\\Users\\<USER>\\Documents\\POSVueChrome\\SQLitePlugin
827F565E-942B-4002-AE87-E4191012E1B2.sonar.sourceEncoding=utf-8
827F565E-942B-4002-AE87-E4191012E1B2.sonar.sources=\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\SQLitePlugin\\SQLiteHelper.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\SQLitePlugin\\SQLitePlugin.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\SQLitePlugin\\Properties\\AssemblyInfo.cs",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\SQLitePlugin\\App.config",\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\SQLitePlugin\\packages.config"

827F565E-942B-4002-AE87-E4191012E1B2.sonar.cs.roslyn.reportFilePath=C:\\Users\\<USER>\\Documents\\POSVueChrome\\SQLitePlugin\\bin\\Debug\\Plugin.dll.RoslynCA.json
827F565E-942B-4002-AE87-E4191012E1B2.sonar.cs.analyzer.projectOutPath=C:\\Users\\<USER>\\Documents\\POSVueChrome\\.sonarqube\\out\\3
827F565E-942B-4002-AE87-E4191012E1B2.sonar.cs.analyzer.projectOutPaths=\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\.sonarqube\\out\\3"
827F565E-942B-4002-AE87-E4191012E1B2.sonar.cs.roslyn.reportFilePaths=\
"C:\\Users\\<USER>\\Documents\\POSVueChrome\\SQLitePlugin\\bin\\Debug\\Plugin.dll.RoslynCA.json"

827F565E-942B-4002-AE87-E4191012E1B2.sonar.working.directory=C:\\Users\\<USER>\\Documents\\POSVueChrome\\.sonarqube\\out\\.sonar\\mod3
sonar.host.url=http://***********:9000
sonar.visualstudio.enable=false

sonar.modules=82C5360C-EC89-4621-A783-A39337F7A3E0,B04C2D59-4E43-4CFD-B2DF-F73B51472449,8FD02983-B60C-4D40-80EF-7F373BC8E50B,827F565E-942B-4002-AE87-E4191012E1B2

