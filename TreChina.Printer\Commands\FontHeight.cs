﻿using ESCPOS.Printer.Extensions;
using ESCPOS.Printer.Interfaces.Command;

namespace ESCPOS.Printer.Commands
{
    public class FontHeight : IFontHeight
    {
        public byte[] Normal()
        {
            return new byte[] { 27, '!'.ToByte(), 0 };
        }

        public byte[] DoubleHeight2()
        {
            return new byte[] { 27, '!'.ToByte(), 16 };
        }

        public byte[] DoubleHeight3()
        {
            return new byte[] { 27, '!'.ToByte(), 32 };
        }
    }
}

