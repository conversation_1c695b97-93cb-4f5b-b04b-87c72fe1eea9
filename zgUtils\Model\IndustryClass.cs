﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using zgLogging;
using zgUtils.Controls;

namespace zgUtils.Model
{
    public class IndustryClass
    {
        public string name { get; set; }
        public string content { get; set; }
        public string ico { get; set; }
        public string classUrl { get; set; }
        public string className { get; set; }
        public string schema { get; set; }
        public string subName { get; set; }
        public string token { get ; set; }
        public static List<IndustryClass> GetIndustries()
        {
            List<IndustryClass> industries = new List<IndustryClass>();
            try
            {
                
                RequestBase request = new RequestBase(CommonApp.DefaultConfigURL, "/config/industry");
                
                industries =  NetworkCenter.Instance.SendRequest<RequestBase, ResponseBase<List<IndustryClass>>>(request)?.data;
                Log.Debug("业态List：" + JsonConvert.SerializeObject(industries));
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog("GetIndustries:" + ex.Message);
            }
            return industries;

        }

        public static IndustryClass GetIndustry(string fileName)
        {
            IndustryClass industry = new IndustryClass();
            try
            {
                //if (CommonApp.Industries == null || CommonApp.Industries.Count == 0)
                //{
                //    CommonApp.Industries = GetIndustries();
                //}
                industry = CommonApp.Industries?.Where(item => item.className == fileName).First();
            }
            catch (Exception ex) {
                Log.WriterExceptionLog(ex.Message);
            }
            

            return industry;
        }

        internal static IndustryClass GetIndustry(IndustryClass _industry)
        {
            IndustryClass industry = GetIndustry(_industry?.className);

            if (string.IsNullOrEmpty(industry?.className)) {
                industry = _industry;
            }
            return industry;
        }
    }
}