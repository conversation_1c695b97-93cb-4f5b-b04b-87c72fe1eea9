﻿using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgSerialPort.ACS
{
    public abstract class BaseACS
    {
        /// <summary>
        /// 获取数据后执行回调函数
        /// </summary>
        public Action<string> CallbackAction;

        /// <summary>
        /// 接收报文，处理报文事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        public abstract void CommDataReceived(Object sender, SerialDataReceivedEventArgs e);

        /// <summary>
        /// 指定的byte数组在源数组中的第一个出现位置
        /// </summary>
        /// <param name="data"></param>
        /// <param name="find"></param>
        /// <returns></returns>
        public int IndexOf(byte[] data, byte[] find)
        {
            for (int i = 0; i < data.Length - find.Length; i++)
            {
                if (data.Skip(i).Take(find.Length).SequenceEqual(find)) return i;
            }
            return -1;
        }

        /// <summary>
        /// 指定的byte数组在源数组中的最后一个出现位置
        /// </summary>
        /// <param name="data"></param>
        /// <param name="find"></param>
        /// <returns></returns>
        public int LastIndexOf(byte[] data, byte[] find)
        {
            int index = -1;
            for (int i = 0; i < data.Length - find.Length; i++)
            {
                if (data.Skip(i).Take(find.Length).SequenceEqual(find))
                {
                    index = i;
                }
            }
            return index;
        }
    }
}
