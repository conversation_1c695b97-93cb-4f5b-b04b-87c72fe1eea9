﻿using System.Windows.Forms;

using zgpos.ProgramTemplate;

namespace DefaultPorgrams
{
    public class WaitingClass : ProgramTemplateClass
    {
        public WaitingClass()
        {
            this.Name = "美业版";
            this.Description = "美业版 [via leon]";
            this.Icon = DefaultProgramResource.WaitingIcon;
        }

        public override string FileName => System.Reflection.Assembly.GetExecutingAssembly().ManifestModule.ScopeName;
        
        protected override Form CreateProgramForm()
        {
            return new DefaultProgramForm(
                this.Name,
                this.Icon,
                DefaultProgramResource.Waiting
                );
        }
    }
}
