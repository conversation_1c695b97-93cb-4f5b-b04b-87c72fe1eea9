﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgUtils.Model
{
    public class SettingInfo
    {
        public Setting setting;
        
        public SettingInfo()
        {
            setting = new Setting();
        }
        public SettingInfo(DataTable dataTable)
        {
            this.localData = dataTable;
            Dictionary<string, string> dic = dataTable.Rows.Cast<DataRow>().ToDictionary(x => x[0].ToString(), x => x[1].ToString());
            var json = JsonConvert.SerializeObject(dic);
            setting = JsonConvert.DeserializeObject<Setting>(json);
        }

        public DataTable localData { get; private set; }
        public UserInfo getUserinfo()
        {
            UserInfo userinfo = new UserInfo();
            userinfo.Accesstoken = setting.Accesstoken;
            userinfo.sysUid = setting.sysUid;
            userinfo.sysSid = setting.sysSid;
            //userinfo.Pass = setting.Pass;
            userinfo.uid = setting.uid;
            userinfo.name = setting.username;
            userinfo.employeenumber = setting.employeenumber;
            return userinfo;

        }
    }
    public class Setting
    {
        public string SetupPackge { get { return CommonApp.SetupPackge; } }
        public string systemName { get { return CommonApp.Config.systemName; } }
        public string subName { get { return CommonApp.Config.subName; } }
        [JsonProperty("userpwd")]
        public string Pass { get; set; }//          remark: '用户密码'
        [JsonProperty("usersid")]
        public string sysSid { get; set; }//          remark: '用户sid'
        [JsonProperty("usertoken")]
        public string Accesstoken { get; set; }//          remark: '用户token'
        [JsonProperty("useruid")]
        public string sysUid { get; set; }//          remark: '用户uid'
        [JsonProperty("lastSync")]
        public string lastSync { get; set; }
        public string stock { get; set; }//          remark: '无库存设置'
        public string cutsmallmoney { get; set; }//          remark: '抹零设置'
        public string username { get; set; }//          remark: '用户名'
        public long uid { get; set; }//          remark: '用户名ID'
        public string employeenumber { get; set; }//          remark: '工号'

        public bool? ultimate { get; set; }//          remark: '是否旗舰版权限'

        public string employeenumber_list { get; set; }//          remark: '工号列表'

        public string employeeremember { get; set; }//          remark: '员工是否记住密码'

        public string period { get; set; }//          remark: '是否是无限期限的'

        public string enddate { get; set; }//          remark: '有效期结束时间，时间戳'

        public string userremember { get; set; }//          remark: '是否记住密码'

        public string userlastime { get; set; }//          remark: '最近一次登陆时间'

        public string devicecode { get; set; }//          remark: '单号后缀'

        public string show_ad { get; set; }//          remark: '是否显示广告'

        public string remark_content { get; set; }//          remark: '小票备注内容'

        public string font_value { get; set; }//          remark: '小票正文字号设置'

        public string line_height { get; set; }//          remark: '小票行间距'

        public string print_cols { get; set; }//          remark: '小票打印规格'

        public string print_cols_label { get; set; }//          remark: '条码纸规格'

        public string print_small_ticket_afterpay { get; set; }//          remark: '支付完成是否打印小票'

        public string useCardReader { get; set; }//          remark: '是否读卡：0-不读，1-读'

        public string show_novice { get; set; }//          remark: '0不显示引导页1显示引导页'

        public string recycle { get; set; }//          remark: '启用商品回收站设置'

        public string voice_off { get; set; } = "0";//          remark: '语音播报开关'

        public string print_card_small_ticket_afterpay { get; set; }//          remark: '次卡支付完成是否打印小票'

        public string smallPrinterCopies { get; set; }//          remark: '小票打印份数'

        public string labelPrinterCopies { get; set; }//          remark: '条码打印份数'

        public string tipPrinterCopies { get; set; }//          remark: '标价签打印份数'

        public string memberInfoArr { get; set; } = "[]";//          remark: '打印会员信息'

        public string logAndCode { get; set; } = "[]";//          remark: '店铺logo及二维码'
        public string remarkSwitch { get; set; }//          remark: '备注能容开关'
        //public string version { get; set; }
        public string sales_order { get; set; }
        public string has_moneybox { get; set; }
        public string labelprint { get; set; }
        public string moneybox { get; set; }
        public string posprint { get; set; }
        public string tipprint { get; set; }
        // 预估利润：年月日
        public string estimatedProfit { get; set; }
        // 预估利润：小眼睛
        public string eye { get; set; }
        public string hasWeigh { get; set; } //是否启用电子秤(true/false)
        public string weighValue { get; set; } //电子秤端口
        public string weighTypeValue { get; set; } //电子秤类型
        public string weighStatus { get; set; } //电子秤状态
        public string showKexian { get; set; } //是否启用客显(true/false)
        public string kexianValue { get; set; } //客显端口
        public string setLabelDefault { get; set; } //条码默认打印份数开关
        public string printVipPrice { get; set; } // 打印会员价开关
        public string setTipDefault { get; set; } // 标价签默认打印份数开关
        public string labelItem { get; set; } // 条码需要打印的信息
        public string set_priceNotice { get; set; } // 负利润check
        public string isOnceOpen { get; set; } // true/false 是否是第一次打开称重商品
        public string printMode { get; set; } // 打印方式
        public string codePrintValue { get; set; } // 打印条码 codePrintValue 0:条码 1:主条码
        public string isTransverse { get; set; } // 打印预览 isTransverse 1横向 0 纵向
        public int? syncHistoryId { get; set; } // 最后一次成功云同步id
        public string tagModels { get; set; } 
        public int? tagIsTransverse { get; set; }
        public string tagPrint { get; set; }
        public string tagPrintCols { get; set; }
        public string info { get; set; }
    }
}
