﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAABVYAAAMACAYAAADPPjzCAAAABGdBTUEAALGPC/xhBQAAMVpJREFUeF7t
        3etSG1nWrtG+/6t0hyOIptSfAOssbbU2U2TKSZICTZCUpzEinij/cJVtjLqt14uV/zocDktJkiTdvt1/
        /iOpZzW9lqVo/vRwmP3330tJ0nj71//2+9f/TwAA4Jbiz1z71eqw+89/JPWkeM16v0ST/X53WM4mhz//
        /bckaaQdh9XX/0+Iv20DAODGjuPqnz+NA46kbhWvVaMq58Tnxmo+fXtzPfn14c22JGn4GVYBAO5sv9sc
        /t///V/jkCOpG8VrNF6rcE4Mq5vl89uba8OqJI0ywyoAQAuMq1J3M6pyiRhWt+vZYTb9bViVpJFmWAUA
        aMnxvtV//mkcdiS11OtrMl6bcAnDqiSNO8MqAECLPMxK6lZGVTK227VhVZJGnGEVAKBFHmYldScPqyIr
        rowwrErSeDOsAgC0zLgqtZ9Rle/Y73eH+dODYVWSRpphFQCgAzzMSmovD6viu07DasObbUnS8DOsAgB0
        hHFVun9GVX4iTjkvXh4b32xLkoafYRUAoEOMq9L9MqryUzGsrubTtzfYrgOQpNFlWAUA6BjjqnT7jKpc
        Qwyrm+WzB1hJ0kgzrAIAdNB+tTrs/vmncRCS9MNeX1vxGoNr2G0WhlVJGmmGVQCAjjKuSjfIqMqVxcln
        w6okjTPDKgBAhxlXpStmVOUG9vvdYf70YFiVpBFmWAUA6DjjqnSFjKrcSNyzunh5bHzDLUkadoZVAIAe
        MK5KP8ioyg3FsLqaT9/eZDu1KkmjyrAKANATMQzFk8wbhyNJjR2f/m9U5YZiWN2uZ+5ZlaQRZlgFAOiR
        eEiKcVW6rOOo+vqagVvbbteGVUkaYYZVAICeMa5KX2dU5Z7ic80DrCRpfBlWAQB6Jr7s1Lgqnc+oyr15
        gJUkjTPDKgBATxlXpY8ZVWmDB1hJ0jgzrAIA9Njx9OqfP40DkzS24rUQrwlow26zcM+qJI0swyoAQM+d
        xtV//mkcm6TB9/q5b1SlbXFS2rAqSePKsAoAMADHcXW1Mq5qfMWo+vq5b1Slbfv9zj2rkjSyDKsAAAMS
        A5N7VzWWjvepvn7OQxfEuO+eVUkaV4ZVAICB8VArjSEPqaKL3LMqSePKsAoAMEDuXdVgc58qHRZj//zp
        wbAqSSPJsAoAMFDuXdXgcp8qHRefm+5ZlaTxZFgFABg4VwNoCPnSf/oghtXN8vntDbdTq5I0+AyrAAAj
        EIOUqwHUy4ov/Teq0hfb7do9q5I0kgyrAAAjUV4N4PSq+tLxlKov/adn9vude1YlaSQZVgEARuZ0erVh
        yJK6klOq9FX8RcBqPn17021claRBZ1gFABih4+lVVwOoixVf+u+UKn22Xc9cByBJI8iwCgAwUsdx1elV
        dajylKpRlb6Lz2PXAUjS8DOsAgCM3HFgdfeqWsxdqgxNfC4vZ5PGN+GSpOFkWAUA4MjpVbVReUoVhsZ1
        AJI0/AyrAACcOL2qe+WUKkPnOgBJGn6GVQAAPjidXvVwK1274uFUTqkydK4DkKThZ1gFAKDR8fSqgVXX
        qjKoOqXKWOw2C9cBSNKAM6wCAPCpcmB1PYC+2/HL/g2qjNB+v3MdgCQNOMMqAAAXOQ6s7l9VIveoMnbx
        ub+aT9/egBtXJWlwGVYBAEgxsOqrDKrw13a7dh2AJA00wyoAAN9iYFU9gyp8FK+Hxctj4xtySVK/M6wC
        APAjBlYZVOG8eF1s1zOnViVpgBlWAQC4incD6z//NA5wGlCvv8cGVbhMPLzNQ6wkaXgZVgEAuKpyYN3/
        +WNgHWKvv6fxe2tQhcvFa8VDrCRpeBlWAQC4iePAutscRzjXBPS/4+nUGFRff08NqpDnIVaSNLwMqwAA
        3FSMcMeR1SnW/lU7nWpQhe+L14+HWEnSsDKsAgBwN8eB1SnWzud0KtxGPMTq+GbcqVVJGkSGVQAAWnEc
        WYtTrEbW9juNqe5OhZvxECtJGlaGVQAAWhUjnpG1nepjqkEVbiteY5vl89sbcuOqJPU+wyoAAJ3ROLK6
        k/V6vX4sjanQLqdWJWk4GVYBAOik08gad7KuVkbW71Z5AFV5Z6oxFdoTr7/VfPr2pty4Kkm9zrAKAEAv
        nIZWp1k/z6lU6Lztdn2YTX8bViWp5xlWAQDopfrQOsqxtTqiGlKhN+I1upxNGt+kS5L6k2EVAIBBOA2t
        xdUBH8bWPg+uxc+/PqL60n7orzi16q5VSep3hlUAAAarHB2bBtfOja5N42lUG1AjoP/itXy6a1WS1MsM
        qwAAjE51pIxOo2tZddh8LcbOeqdBtlbT9y1799+t/nhRbTyNgGFz16ok9TvDKgAANKiPnNcOIP63wF2r
        ktTfDKsAAADQEnetSlJ/M6wCAABAS+LU6umuVeOqJPUqwyoAAAC0yKlVSepnhlUAAABokVOrktTPDKsA
        AADQsv1u49SqJPUswyoAAAC0LE6tbpbPb2/WjauS1IsMqwAAANAB+/3usHh5NKxKUk8yrAIAAEBH7DaL
        w2z627gqST3IsAoAAAAdEVcCLGeTxjfwkqRuZVgFAACADtlu1x5kJUk9yLAKAAAAHRKnVlfz6dsbd+Oq
        JHU2wyoAAAB0zH63cWpVkjqeYRUAAAA6Jk6tbtczD7KSpA5nWAUAAIAOinF18fLY+GZektR+hlUAAADo
        KA+ykqTuZlgFAACAjvIgK0nqboZVAAAA6LB4kNXxSgDDqiR1KsMqAAAAdNxus/AgK0nqWIZVAAAA6DhX
        AkhS9zKsAgAAQA+4EkCSupVhFQAAAHogTq1u1zNXAkhSRzKsAgAAQE+4EkCSupNhFQAAAHrkdCVAw5t8
        SdL9MqwCAABAz+w2i8P86cGpVUlqMcMqAAAA9ExcCbBZPr+9uTeuSlIrGVYBAACgh/b73duVADGsGlcl
        6e4ZVgEAAKCnttu1KwEkqaUMqwAAANBTcSXAdj07zKa/jauSdOcMqwAAANBjMa6u5tO3N/rGVUm6W4ZV
        AAAA6Ln9buO+VUm6c4ZVAAAAGAD3rUrSfTOsAgAAwAC4b1WS7pthFQAAAAbCfauSdL8MqwAAADAgp/tW
        G0YASdL1MqwCAADAwMS46r5VSbpthlUAAAAYmLgSYLdZuG9Vkm6YYRUAAAAGyMOsJOm2GVYBAABgoDzM
        SpJul2EVAAAABizuW13OJo2jgCTp+xlWAQAAYOBiXF28PL6NAU6uStJVMqwCAADACMTDrOZPD4ZVSbpS
        hlUAAAAYgbhvNcZVD7OSpOtkWAUAAICRiHF1u54ZVyXpChlWAQAAYERiXF3Np2/DgHFVkr6dYRUAAABG
        Zr/fHZazyds4YFyVpG9lWAUAAIAR2u82f8dVSVI6wyoAAACMVIyri5fHxsFAkvR5hlUAAAAYMeOqJH0v
        wyoAAACM3Ha7PsyfHhqHA0lSc4ZVAAAAGLn/7fd/x9V4mJUHWknSlxlWAQAAgOO4utssjKuSdGGGVQAA
        AODoeHJ1PTOuStIFGVYBAACAE+OqJF2WYRUAAAB4pxxXZ9PfxlVJOpNhFQAAAPjgw7jaMCpI0pgzrAIA
        AACNnFyVpPMZVgEAAICzjKuS1JxhFQAAAPiUcVWSPmZYBQAAAL5kXJWk9xlWAQAAgIuU4+r86cG4Kmn0
        GVYBAACAixlXJektwyoAAACQEuPqbrP4O642DA6SNPQMqwAAAECacVXS2DOsAgAAAN9yvBZgu34bV2No
        MLBKGlGGVQAAAODbYlzd7zaHxcvj29hgXJU0kgyrAAAAwI/FuLqcTRrHB0kaYoZVAAAA4CrejatOrkoa
        eIZVAAAA4Gr2+91hNZ8eZtPfb+OqgVXSQDOsAgAAAFcV965uls9/x9WGQUKS+p5hFQAAALi6GFe365mT
        q5IGm2EVAAAAuIlyXJ0/PRhWJQ0uwyoAAABwMzGu7jaLw+Ll8W2MMLBKGkiGVQAAAOCmYlzd7zZ/x1VJ
        GkCGVQAAAOAuYlxdziZvo4R7VyX1PMMqAAAAcDdxenU1n3qolaTeZ1gFAAAA7urDQ62Mq5J6mGEVAAAA
        uDsPtZLU9wyrAAAAQCs+PNTK6VVJPcqwCgAAALRqv9+9v3e1YcCQpK5lWAUAAABa9+7e1RgtDKySOp5h
        FQAAAOiE47i6Xbt3VVIvMqwCAAAAnRL3ri5nE1cDSOp0hlUAAACgc+L06mb5/HdcNbBK6liGVQAAAKCT
        YlzdbRbuXZXUyQyrAAAAQGfFuFpeDXAcM4yrkjqSYRUAAADovP1+9/dqgBg1DKySWs6wCgAAAPRCeTXA
        4uXxbdhw96qkFjOsAgAAAL1RXg2wmk892EpSqxlWAQAAgN6JgXW7nnmwlaTWMqwCAAAAveTBVpLazLAK
        AAAA9JoHW0lqI8MqAAAA0HsebCXp3hlWAQAAgEHwYCtJ98ywCgAAAAyK06uS7pFhFQAAABgcp1cl3TrD
        KgAAADBYTq9KulWGVQAAAGDQnF6VdIsMqwAAAMAoNJ5erQ0lknRphlUAAABgNI6nV/e7v6dXYyAxsEr6
        RoZVAAAAYHTcvSrppxlWAQAAgFEqT69uls+H+dPD21hiXJV0YYZVAAAAYNQ+PNwqRhMDq6QvMqwCAAAA
        vHI9gKRMhlUAAACAQnl61fUAkr7KsAoAAABQEwPrdrs+LGeTt+sBnF6VVMuwCgAAAHDGcWBdz1wPIOlD
        hlUAAACAT5y9HsDAKo06wyoAAADABcqBdTWfvl0PEOOKcVUabYZVAAAAgIQYWHebhftXpZFnWAUAAABI
        Op5e3e+O96+6HkAaZ4ZVAAAAgG8qB1b3r0rjy7AKAAAA8ENn7181sEqDzbAKAAAAcCUxsG6367/3r8YA
        Y2CVBplhFQAAAODKPjzgKoYY46o0qAyrAAAAADdiYJWGm2EVAAAA4IbKB1zFwLp4eXwbWF0PIPU+wyoA
        AADAHZQD63Y9Ow6sx3HGwCr1NsMqAAAAwB1VB9b508PbSGNglXqXYRUAAACgBQZWqd8ZVgEAAABaZGCV
        +plhFQAAAKADGu9gjQysUiczrAIAAAB0SH1gnU1/vw05TrFKncqwCgAAANBB5cC62ywMrFIHM6wCAAAA
        dFh1YF3OJgZWqSMZVgEAAAB6IkbW7XZ9WM2nHnQltZxhFQAAAKBnjqdYd5vDZvlsYJVayrAKAAAA0FPl
        NQExsLqHVbpvhlUAAACAnvOgK+n+GVYBAAAABqIcWM/ew2pkla6WYRUAAABggKr3sDrFKl0/wyoAAADA
        gFWvCXCKVbpehlUAAACAkaieYo2B1SlW6fsZVgEAAABGpnqKdTmbfBxYjazSlxlWAQAAAEYqBlanWKXv
        ZVgFAAAA4HSKdbueHU+xnu5ijYys0ocMqwAAAACc1E+xLl4eXRUgNWRYBQAAAKBReYo1RtbVfOqqAKmS
        YRUAAACAL5Uja/nAK1cFaOwZVgEAAAC4WPWqgLiP9d1VAZGRVSPJsAoAAADAt1SvCvhwH2tkZNWAM6wC
        AAAA8GNGVo0twyoAAAAAV2Vk1RgyrAIAAABwMxePrIZW9SzDKgAAAAB3cW5kPQ2tRlb1KMMqAAAAAHd3
        Gllf220Wh9V8epg/PbgyQL3JsAoAAABAq2JkLYfW7XZ9HFldGaCuZ1gFAAAAoFNcGaA+ZFgFAAAAoLOq
        I+t2Pfv8ygBDq+6YYRUAAACAXqheGVCeZl3OJoZWtZJhFQAAAIBeqg6t8QCsxmsDIkOrbpBhFQAAAIBB
        qJ5mjaG1+hAsQ6uunWEVAAAAgMGpXxtgaNW1M6wCAAAAMHjfHlqNrTqTYRUAAACA0akPrdvt+sPDsIyt
        +izDKgAAAAC8+jC2rmenU63GVtUzrAIAAABAg3dD6ydj67vBzdg6mgyrAAAAAHChr8bW8lTrpydbDa6D
        yLAKAAAAAD/wbmzdbU5j66d3tkYG115nWAUAAACAKyvH1urgGg/IOndv65cnXKPKqKf2M6wCAAAAwJ28
        G1sbBtc44Xr2YVlR0+BaVv1+unmGVQAAAABo2YcTruXoWlwpcG50Nby2l2EVAAAAADrs3Oi62yzeDa/l
        6FofXlPja1n1++ut2sfIsAoAAAAAPdU4upanXYsrBi459do4wJ4ZFC+u6b/VZk0/x69q+O+cPl6GVQAA
        AAAYrur4+m6A3W3ODrD1EfbcEFutaYT8sqYxM1vTf/eCmn4NZeWvOX798XGI4mMSH5+o/HgZVgEAAABg
        5OoD7LsRtmmMrQyy9VG2HGarxUhZVg6Xn3Vu7Pys6o9R//HLn1d9HI2O1yq8/nrKX1/119z0cSk4sQoA
        AAAAfE/T8Hiu6mB565p+/Ho/ZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMq
        AAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACA
        JMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAA
        AACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmG
        VQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAA
        AEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsA
        AAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACS
        DKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAA
        AACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlW
        AQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAA
        JBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIA
        AAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgy
        rAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAA
        AEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgF
        AAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQ
        ZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAA
        AACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmw
        CgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAA
        IMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUA
        AAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECS
        YRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAA
        AECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMq
        AAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACA
        JMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAA
        AACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmG
        VQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAA
        AEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsA
        AAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACS
        DKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAA
        AACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAAJBlW
        AQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIAAAAA
        JBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgyrAIA
        AAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAAAEgy
        rAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgFAAAA
        AEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQZFgF
        AAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAAAACQ
        ZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmwCgAA
        AACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAEmGVQAAAACAJMMqAAAAAECSYRUAAAAAIMmw
        CgAAAACQZFgFAAAAAEgyrAIAAAAAJBlWAQAAAACSDKsAAAAAAElvw+p+tzmc2u8u7n/7/dkAAAAAAAZq
        +a/Zf/+9nE1/Hz5r/vTwrsXL47HlbHJqNZ8e2yyfD9v17K3t+sNga4QFAAAAAHrubVj9M/l1uLj//vvL
        Phtny0G2OsJ+Nr4CAAAAAHRMMaw2jKPpmkbYpmr/Xn18jeH13cnX8tSrwRUAAAAA6IYrDqvZmkbXqPJ9
        6qNredK1fsrV2AoAAAAA3FGLw+pnNQ2uUeX71E+4njvdCgAAAABwZR0dVj/ri7E1qo6thlYAAAAA4Mp6
        OKw2dWZsrV4jYGgFAAAAAK5kIMNqU5+MrYZWAAAAAOAHBjys1vtkaC2vDthtFkZWAAAAAOArIxpW650Z
        WeM063I2+XCaFQAAAACgMOJhtd6ZoTVOs26Wz0ZWAAAAAKBkWD2bkRUAAAAAaGZYvajaaVYjKwAAAACM
        mmE1nZEVAAAAAMbOsPqjKgNrVI6s5YOvDKwAAAAAMEiG1atVG1nnTw+H1Xx62G0WTrECAAAAwLAYVm9S
        ZWStXxVgYAUAAACA3jOs3jSnWAEAAABgiAyrd6vhFOvxLlYDKwAAAAD0jWH17jWcYnVNAAAAAAD0imG1
        1Soja3lNwHa7NrACAAAAQLcZVjtRZWCNawKWs4l7WAEAAACguwyrnao2sLqHFQAAAAA6ybDayQysAAAA
        ANBlhtXOVwysUdzDamAFAAAAgNYZVntT5RSrgRUAAAAAWmVY7V2VgdUVAQAAAADQCsNqbzOwAgAAAEBb
        DKu9rxhYy4dc7TYL4yoAAAAA3JZhdTAVp1djYF3OJgZWAAAAALgdw+rgqgysq/n0sN9tDKwAAAAAcF2G
        1UFWXA8Q354/PRw2y2cDKwAAAABcj2F10NUGVg+4AgAAAICrMKyOomJg9YArAAAAALgKw+qoqgys7l8F
        AAAAgG8zrI6uYlyNb5/uX3U9AAAAAABkGFZHW2VgdT0AAAAAAKQYVkdfMa66HgAAAAAALmZYVVHl9Op2
        PTOuAgAAAMB5hlVVKq4HiNOry9nE6VUAAAAAaGZYVUPF6dXqw60AAAAAgBPDqs5UO7263a6dXgUAAACA
        N4ZVfZHTqwAAAABQZ1jVBRWnV+Pb8XCr3Wbh9CoAAAAAY2ZYVSKnVwEAAAAgGFb1jZxeBQAAAGDcDKv6
        Zk6vAgAAADBehlX9sGJgXc4mh/1u4/QqAAAAAGNgWNUVqpxe3a5nxlUAAAAAhs6wqisV4+prs+nvw2o+
        dXoVAAAAgCEzrOrKFadXPdgKAAAAgAEzrOoGFeNqnF71YCsAAAAABsiwqhtWDKwebAUAAADAwBhWdeOK
        cdWDrQAAAAAYEMOq7lCMq6+5GgAAAACAgTCs6k4V42p829UAAAAAAPScYVXtFFcD7DYL4yoAAAAAfWRY
        VQvVrgYwrgIAAADQM4ZVtVgxrpZXAwAAAABATxhW1XLFvauLl8fDdrt2ehUAAACAPjCsqgMVVwPEvavb
        9cy4CgAAAEDXGVbVkYpxNa4GWM2nh/1+V3yOAgAAAEDnGFbVoYpxNb5d3rvq9CoAAAAAHWRYVXeLe1d3
        m4VxFQAAAICuMayqoxWnV927CgAAAEAHGVbV4YpxNe5d3SyfjasAAAAAdIVhVR2vGFfj2+W9qwAAAADQ
        MsOq+lXcu+qhVgAAAAC0zLCqHlWcXI17Vz3UCgAAAIAWGVbVwyYeagUAAABAqwyr6mnFQ61W8+lhv98V
        n88AAAAAcBeGVfU0D7UCAAAAoD2GVQ0jD7UCAAAA4I4MqxpAxcnVGFe327VxFQAAAIBbM6xqQBUPtdpt
        FsZVAAAAAG7JsKoBVdy7GuPqdj0zrgIAAABwK4ZVDbDJr8Ns+vuwmk+NqwAAAADcgmFVA6w4uRrfjnE1
        HmoFAAAAAFdkWNXwW84mxlUAAAAArsmwqoFXnFxdvDwex1VXAwAAAABwBYZVjaBiXD0+1Gq7Nq4CAAAA
        8FOGVY2oya/juLrbLIyrAAAAAPyEYVUjqxhXt+uZcRUAAACA7zKsamTFtQCvzaa/jasAAAAAfJdhVSOt
        GFc3y2fjKgAAAABZhlWNtMrJ1dV8etjvd8VrAgAAAAC+ZFjVyIuB9fWfxlUAAAAAEgyrUtlyNjnsd5vi
        tQEAAAAAZxlWpWPFydXFy6NxFQAAAICvGFalU8ZVAAAAAC5jWJXeVRlXt9v14X/7ffFaAQAAAIATw6p0
        rvnTg3EVAAAAgCaGVemzYlzdbRbGVQAAAACqDKvSp01+vZ1cXc+MqwAAAACUDKvSp8Wdq8ZVAAAAAN4z
        rEpfVoyrs+lv4yoAAAAAwbAqXZRxFQAAAIC/DKtSqmJc3SyfjasAAAAA42VYlVJVTq4aVwEAAABGy7Aq
        fatiXF3Np8ZVAAAAgPExrErfrjKu7ve74jUFAAAAwAgYVqUfFVcDvP7TuAoAAAAwKoZV6VoZVwEAAABG
        w7AqXbPlbGJcBQAAABg+w6p07YyrAAAAAINnWJVu0XFc3W2K1xkAAAAAA2NYlW6VcRUAAABgsAyr0k2a
        /Dr+c/HyaFwFAAAAGB7DqnSzjKsAAAAAQ2VYlW6acRUAAABgiAyr0s0zrgIAAAAMjWFVukvGVQAAAIAh
        MaxKd8u4CgAAADAUhlXprhlXAQAAAIbAsCq1lXEVAAAAoLcMq1KblePq//b74jUJAAAAQA8YVqW2i3F1
        u10bVwEAAAD6w7AqdaH504NxFQAAAKA/DKtSJ5r8Mq4CAAAA9IdhVepSxlUAAACAXjCsSl3LuAoAAADQ
        eYZVqYvFuLrbLIyrAAAAAN1kWJW6mnEVAAAAoLMMq1JnKx5oZVwFAAAA6BzDqtTZJr+MqwAAAADdZFiV
        Ol1lXN2uZ8ZVAAAAgG4wrEqdz7gKAAAA0DWGVakXGVcBAAAAusSwKvUm4yoAAABAVxhWpV5VjKuz6W/j
        KgAAAEB7DKtSLzOuAgAAALTJsCr1NuMqAAAAQFsMq1Jvq1wLsFk+G1cBAAAA7sewKvU+4yoAAADAvRlW
        pUFkXAUAAAC4J8OqNJiKcXU1nxpXAQAAAG7LsCoNKuMqAAAAwD0YVqXBVRlX9/td8VoHAAAA+CgOZt2r
        gTGsSkPOuAoAAADj1jRwRrEXbLfrY4vl6kMvf2Yfenp++VDT92v675U/Vvy4TT+fqGcMq9LQW84mxlUA
        AAAYuKahsj6clsNn00B6q879ePURtvy5Nv06OsqwKo2h47i62xSvewAAAKDvqsNjefq0OqA2jZl9qT64
        1k+5doRhVRp8k1/Hfy5eHo2rAAAA0FPVYTHe3w9lRL206tgav/7qx6MlhlVpFBlXAQAAoFeqw2E5pMa4
        OJYh9avKj0WLQ6thVRpNtXH1jv9DAwAAAFygHAarX9pvSL2scmStXx1wQ4ZVaYzNnx5OF0IDAAAA7amP
        qYbU6xQfxxuPrIZVaZRNfh3H1d1mYVwFAACAOzOm3rcbjayGVWm0FePqdj0zrgIAAMAdlKNejHzxZetN
        I6BuW3ldwBUGVsOqNNriztXXZtPfxlUAAAC4kXLAi9OS7kztTvH7EL8fPzjFaliVRl8xrm6Wz8ZVAAAA
        uJJyrHM6tft98xSrYVUafZWTq6v51LgKAAAAPxDvq92d2s/qd7F+wbAqqagyru53m+J/IwAAAIBLlIOq
        L/fvf/VrAs4wrEr62HI2Ma4CAADABaqDatNIp373ycBqWJVUK64GeP3n4uXxOK5ecPQdAAAARscJ1fF0
        5gSrYVVSQ8W4On96OOw2C+MqAAAAFAyq4606sL4yrEr6pMmv47i6Xc+MqwAAAIyaQVVl8fsfGVYlnS9O
        rhYPtdosn42rAAAAjE68F4485V/v++PEqqQLKsbV1XxaHncHAACAwTOo6nyGVUmXVJxcjW8vZ5PjQ60A
        AABgqI5f9v/63je+7L95VJMMq5IyFePq4uXx+Dd2rgYAAABgSHzZvy7PsCopW3F61UOtAAAAGBKDqnIZ
        ViV9p2Jc9VArAAAA+q48pepp/8plWJX0k4px1b2rAAAA9JFTqvp+hlVJPy1Or77+M+5djXHV6VUAAAD6
        wClV/SzDqqRrVFwN4N5VAAAAui7es3riv36eYVXStSrG1bgaYDWfHvb7XfF/WQAAANANvvRf18uwKuma
        FeNqfLu8d9XpVQAAALqg/NL/5pFMymZYlXTDXA0AAABA28ov/XdKVdfNsCrpVlWuBtgsn10NAAAAwN35
        0n/dLsOqpFtWuRpg8fJ4/D8zp1cBAAC4h/JL/42quk2GVUn3qBhXXQ0AAADAPZSjavMgJl0jw6qke1Zc
        DeDBVgAAANxCvM+Mq+icUtXtM6xKundOrwIAAHAj7lPV/TKsSmqj4u5Vp1cBAAC4hnhPaVTVfTOsSmqz
        yunVzfL5+OUaBlYAAAAyjKpqJ8OqpLYrTq/Gtxcvj4fdZmFcBQAA4CJGVbWXYVVSV6pcD7CaT10PAAAA
        wJc8+V/tZViV1KUqp1ddDwAAAMA58T7RqKp2M6xK6mK1gXW7nhlYAQAAODKqqhsZViV1uWJgjesByvtX
        DawAAADjZVRVdzKsSupDxenVGFiXs4mBFQAAYISMqupWhlVJfaoysMYJVlcEAAAAjINRVd3LsCqpjxlY
        AQAARiPe622364ZhS2ozw6qkPlcMrFE85GqzfDawAgAADEg5qr78mTUMW1KbGVYlDaEYWIuR9TSw7jYG
        VgAAgB4zqqrbGVYlDanawLqaTz3oCgAAoKfiwIxRVd3NsCppiFUGVvewAgAA9E+8fzOqqtsZViUNucrA
        GrkmAAAAoPvi/ZpRVd3PsCppLFVGVtcEAAAAdFO8P1ssVw0jltS1DKuSxlZlYC2vCXCKFQAAoH1GVfUr
        w6qksVYZWCOnWAEAANoT78G227UrANSjDKuS9OUpViMrAADAbcX7L6Oq+pVhVZL+Vg6slZF1OZsctuuZ
        kRUAAOBG4n2WUVX9y7AqSc1VBtbIVQEAAADXF++t3KuqfmZYlaSvq51ibRpZDa0AAAA58T4q7lVtHq2k
        rmdYlaTLKwdWIyvcVPk6+mkAAHSbe1XV7wyrkvS9vhpZ3cnKiFXHzWrxlw+nXl8jZXFKIV43UdxpXC0e
        JFctXmNNVb9P9d8//Xdff4zTj1n5edR/jgAA3Ef82cuoqn5nWJWkn3dmZK0++KoccKDPqgNkdBooKwNp
        OWhWR9B4LSxeHo/FayOK18klfXi9XVDTf6da+XOIn0/83Krj7PHnX46wDeMrAAA/F3+ucq+q+p9hVZKu
        W21kjWLIiQHnONrEYFMZaqBLys/LqD6alqdJy8G0HEvPjaTvXhfVqq+Rtmv4+VV/DfXxNX798XFoGl0B
        ALhcvC9yWlX9z7AqSbetNuCUY0050pQDjXGGWys/x8qqw2l50vSS0fTd53dUHSrPVf93uljTzzuqfJ/q
        x6EcXeuvZa9nAIDPxZ+TjKoaRoZVSbpfDWNNOdAYWrmG8vMm+mo4jc+7+nD67vM1qn7OVqt/vzH0yceh
        /PhVx9bqyVavZwCAN/FnIlcAaDgZViWpnRoGmnKcaToFZ5ghlJ8HUXU4PY6nF5w4/fRzsFr1++nzznzs
        vno9AwCMUfyFf/NAJfUxw6okdaOGYSaKYSbGsRjKTne0GlsHrfx9jarjaf2O0y+H06j6eVWt/v103Ro+
        1uXv0bsTrV7HAMCIxJ95XAGgYWVYlaRuVh1mPhlnYmhrGlsNNd1V/T2KyuE0utqp0+r3Ufs1/N7E72X5
        lyZOswIAQxd/xnEFgIaXYVWS+lPDOBOVA005tsYw58nl7Sk/1mXV4bQ8dfrj4TSqfj/1q4bfx/i9j8+H
        eP0aWQGAoYk/3zitquFlWJWk/lYdZyoDTVSOdGcH18pwU8bnqh+rsnK4jo/nt5+sX/99LKt+Hw272u95
        fI5UR9by8w0AoI/izzFOq2qYGVYlaVhVh7naWBOVw16MfVGMf+XoWr9WoD68lg1J068vqg6mUf1+0+po
        Wh9Oqx/vY02/J1H9+0lR7fOjfJ0erwsoXpMAAH3igVUaboZVSRpH1UGvWuX7VMfBcjAsT7u+G1+rA2xl
        hC2rj5Tnymj697+q/vM6/XwrQ2l1LP1qMI2qH69jTR/Tsvr3lbJVPpfK12V8nh5ff8VrDQCgy+LPK64A
        0HAzrEqSqmNgvdr3rQ+N5dhTFqNkjJNl5WAZxYD5bpw9U3X0/Kzyv1f9Mao/dvxcPhtJy+q/xmNNH4tq
        Tf+OdKtqn3vxeRuf2/E6MLACAF0Vf0ZxBYCGnWFVkvRV1VHns5r+3Ybqw+Z3a/pvN9b0c22q6d+Vulbt
        8zX+0iD+UiFOYhtYAYAuib8AdlpVw86wKkm6ZdXh8h41/RykoVb5vI+/bIiT2scH1DnFCgC0zGlVjSPD
        qiRJUr+rDaxxTYCBFQBok9OqGkeGVUmSpGHUMLC6hxUAuDenVTWeDKuSJEnDqxhYIwMrAHBPcfd78wgl
        DS3DqiRJ0nAzsAIAd+S0qsaVYVWSJGn4nbmDFQDgmpxW1bgyrEqSJI2nysC6nE0O2+3a6VUA4CqcVtX4
        MqxKkiSNr8rAuppPj6dLDKwAwE/EV8M0j0/SUDOsSpIkjbMYV4uBdf70cNgsn92/CgB8i9OqGmeGVUmS
        pHFXG1jjAVfGVQAgI/5y9uXPrGF4koacYVWSJElR5XoA968CABlOq2qcGVYlSZJUzfUAAEBC/DnBaVWN
        M8OqJEmS6lWuB1i8PB52m4VxFQD4IP58EF/l0jw6SUPPsCpJkqRzFQNrXA+wmk8P+93GwAoAnMSfC1wD
        oPFmWJUkSdJn1U6vergVAFCKv3RtHpykMWRYlSRJ0iU5vQoAVDitKhlWJUmSdGm106vuXgWA8Yo/A3ho
        lcadYVWSJEnZinH1dHp1vyveYgEAY+GhVZJhVZIkSd+tcno13lw5vQoA4+AaACkyrEqSJOknFePq/OnB
        g60AYCTiq1WahyZpTBlWJUmSdI0mHmwFAGMQ/x/vGgApMqxKkiTpWnmwFQAMnmsApDLDqiRJkq5Z5WqA
        zfLZuAoAA+MaAKnMsCpJkqRbVLsaAADoP9cASNUMq5IkSbpVlasB4k2Y06sA0G+uAZCqGVYlSZJ0y2Jc
        fS2uBtiuZ8ZVAOgx1wBI1QyrkiRJunXFuBpXA7h3FQD6yTUAUj3DqiRJku5RZVxdzibuXQWAnnENgFTP
        sCpJkqR7FgPr6z/j3tUYV51eBYB+iP/PfvkzaxiXpLFmWJUkSdK9K8bVuHd1t1kYVwGgB+IvRJvHJWms
        GVYlSZLUYh5qBQDd5xoAqSnDqiRJktrMQ60AoPMMq1JThlVJkiS1WeWhVqv51EOtAKCDYlh1v6pUz7Aq
        SZKkLlTcu7qcTYyrANAx2+26YVSSxp5hVZIkSV2pGFcXL4/HcdXVAADQDa4BkJoyrEqSJKlLFePq8aFW
        27VxFQBa5n5V6VyGVUmSJHWxya/juLrbLIyrANAi96tK5zKsSpIkqasV4+p2PTOuAkBL4nqe5lFJGnvF
        sCpJkiR1uRhXXy0lSdJ9e7sG4M9SUr0/y/8P78nyENKwA6oAAAAASUVORK5CYII=
</value>
  </data>
</root>