﻿using CefSharp;
using ESCPOS.Printer;
using Newtonsoft.Json.Linq;
using PrintCore;
using System;
using System.Drawing;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json;
using System.Speech.Synthesis;

using System.Data;
using System.Collections.Generic;


using zgUtils.Controls;
using zgAdvert.Model;
using System.Net;
using zgUtils.Model;
using zgUtils;
using zgSerialPort;
using zgSerialPort.Common;

namespace zgpos.Browser
{
    public class Notify: CommonBrowserFun
    {
        
        public Notify()
        {

        }
        
    }
}
