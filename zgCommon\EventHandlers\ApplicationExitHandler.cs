﻿using System;
using zgLogging;
using zgUtils.Controls;

namespace zgpos.EventHandlers
{
    public static class ApplicationExitHandler
    {
        public static void DoApplicationExit(object sender, EventArgs e)
        {
           Log.WriterNormalLog($"{nameof(ApplicationExitHandler)}: {nameof(DoApplicationExit)}");
           System.Diagnostics.Process.GetCurrentProcess().Kill();
        }
        
    }
}
