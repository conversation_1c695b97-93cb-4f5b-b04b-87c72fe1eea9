﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
#region 命名空间

using System.Threading;
using System.Runtime.InteropServices;
using System.Net;
using System.Collections;
using System.Drawing;
using Newtonsoft.Json;
using System.Reflection;
using System.IO;
using System.Configuration;
using System.Diagnostics;

#endregion

namespace zgUpdater
{
    public partial class FrmBatchDownload : Form
    {
        
        public Bitmap top;
        Dictionary<string, object> dics;
        #region 全局成员

        public string exePath = Assembly.GetEntryAssembly().Location;
        
        //存放下载列表
        List<SynFileInfo> m_SynFileInfoList;
        private Assembly _assembly = Assembly.GetExecutingAssembly();

        #endregion

        #region 构造函数

        public FrmBatchDownload(Dictionary<string, object> _dics)
        {
            
            InitializeComponent();
            CheckForIllegalCrossThreadCalls = false;
            m_SynFileInfoList = new List<SynFileInfo>();
            top = (System.Drawing.Bitmap)_dics["top"];
            dics = _dics;
        }

        
        /// <summary>
        /// 读取配置
        /// </summary>
        /// <param name="Key">配置Key</param>
        /// <returns>配置信息</returns>
        
        #endregion

        #region 窗体加载事件
        private void FrmBatchDownload_Load(object sender, EventArgs e)
        {
            if (File.Exists(exePath + ".old"))
            {
                File.Delete(exePath + ".old");
            }
            this.BackgroundImage = top;
            //初始化DataGridView相关属性
            InitDataGridView(dgvDownLoad);
            
        }

        #endregion

        #region 添加GridView列

        /// <summary>
        /// 正在同步列表
        /// </summary>
        void AddGridViewColumns(DataGridView dgv)
        {
            dgv.Columns.Add(new DataGridViewTextBoxColumn()
            {
                DataPropertyName = "DocID",
                HeaderText = "文件ID",
                Visible = false,
                Name = "DocID"
            });
            dgv.Columns.Add(new DataGridViewTextBoxColumn()
            {
                AutoSizeMode = DataGridViewAutoSizeColumnMode.None,
                DataPropertyName = "DocName",
                HeaderText = "文件名",
                Name = "DocName",
                Width = 300
            });
            //dgv.Columns.Add(new DataGridViewTextBoxColumn()
            //{
            //    DataPropertyName = "FileSize",
            //    HeaderText = "大小",
            //    Name = "FileSize",
            //});
            //dgv.Columns.Add(new DataGridViewTextBoxColumn()
            //{
            //    DataPropertyName = "SynSpeed",
            //    HeaderText = "速度",
            //    Name = "SynSpeed"
            //});
            dgv.Columns.Add(new DataGridViewTextBoxColumn()
            {
                DataPropertyName = "SynProgress",
                HeaderText = "进度",
                Name = "SynProgress",
                Width = 300
            });
            dgv.Columns.Add(new DataGridViewTextBoxColumn()
            {
                DataPropertyName = "DownPath",
                HeaderText = "下载地址",
                Visible = false,
                Name = "DownPath"
            });
            dgv.Columns.Add(new DataGridViewTextBoxColumn()
            {
                DataPropertyName = "SavePath",
                HeaderText = "保存地址",
                Visible = false,
                Name = "SavePath"
            });
            dgv.Columns.Add(new DataGridViewTextBoxColumn()
            {
                DataPropertyName = "Async",
                HeaderText = "是否异步",
                Visible = false,
                Name = "Async"
            });
        }

        #endregion

        

        #region 开始下载按钮单机事件

        private void btnStartDownLoad_Click(object sender, EventArgs e)
        {
            
        }

        #endregion

        #region 检查网络状态

        //检测网络状态
        [DllImport("wininet.dll")]
        extern static bool InternetGetConnectedState(out int connectionDescription, int reservedValue);
        /// <summary>
        /// 检测网络状态
        /// </summary>
        bool isConnected()
        {
            int I = 0;
            bool state = InternetGetConnectedState(out I, 0);
            return state;
        }

        #endregion

        #region 使用WebClient下载文件

        /// <summary>
        /// HTTP下载远程文件并保存本地的函数
        /// </summary>
        void StartDownLoad(object o)
        {
            Thread.Sleep(100);
            SynFileInfo m_SynFileInfo = (SynFileInfo)o;

            try
            {
                if (!ConfigController.HttpFileExist(m_SynFileInfo.DownPath)) return;
                string savePath = m_SynFileInfo.SavePath + ".tmp";
                m_SynFileInfo.LastTime = DateTime.Now;
                //再次new 避免WebClient不能I/O并发 
                WebClient client = new WebClient();
                if (m_SynFileInfo.Async)
                {
                    //异步下载
                    client.DownloadProgressChanged += new DownloadProgressChangedEventHandler(client_DownloadProgressChanged);
                    client.DownloadFileCompleted += new AsyncCompletedEventHandler(client_DownloadFileCompleted);
                    client.DownloadFileAsync(new Uri(m_SynFileInfo.DownPath), savePath, m_SynFileInfo);
                }
                else
                {
                    client.DownloadFile(new Uri(m_SynFileInfo.DownPath), savePath);
                    DownloadCompleted(m_SynFileInfo);
                }
            }
            catch (Exception ex) {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, string.Format("StartDownLoad:" + ex.Message));
            }
            
        }
        public static long FileSize(string filePath)
        {
            try
            {
                if (!File.Exists(filePath)) return 0;
                FileInfo fileInfo = new FileInfo(filePath);
                return fileInfo.Length;
            }
            catch (Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, ex.Message);
                return 0;
            }

        }
        
        /// <summary>
        /// 下载进度条
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        void client_DownloadProgressChanged(object sender, DownloadProgressChangedEventArgs e)
        {
            SynFileInfo m_SynFileInfo = (SynFileInfo)e.UserState;
            m_SynFileInfo.SynProgress = e.ProgressPercentage + "%";
            double secondCount = (DateTime.Now - m_SynFileInfo.LastTime).TotalSeconds;
            //m_SynFileInfo.SynSpeed = FileOperate.GetAutoSizeString(Convert.ToDouble(e.BytesReceived / secondCount), 2) + "/s";
            //更新DataGridView中相应数据显示下载进度
            m_SynFileInfo.RowObject.Cells["SynProgress"].Value = m_SynFileInfo.SynProgress;
            //更新DataGridView中相应数据显示下载速度(总进度的平均速度)
            //m_SynFileInfo.RowObject.Cells["SynSpeed"].Value = m_SynFileInfo.SynSpeed;
        }

        /// <summary>
        /// 下载完成调用
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        void client_DownloadFileCompleted(object sender, AsyncCompletedEventArgs e)
        {
            SynFileInfo m_SynFileInfo = (SynFileInfo)e.UserState;
            NLogger.Instance.WriteLog(NLog.LogLevel.Info, string.Format("下载{0}完成,开始覆盖。", m_SynFileInfo?.DocName));
            DownloadCompleted(m_SynFileInfo);
        }
        void DownloadCompleted(SynFileInfo m_SynFileInfo) {
            //到此则一个文件下载完毕
            if (FileSize(m_SynFileInfo.SavePath + ".tmp") > 0)
            {
                File.Copy(m_SynFileInfo.SavePath + ".tmp", m_SynFileInfo.SavePath, true);
                File.Delete(m_SynFileInfo.SavePath + ".tmp");
            }
            m_SynFileInfoList.Remove(m_SynFileInfo);
            if (m_SynFileInfoList.Count <= 0)
            {
                //此时所有文件下载完毕
                FrmClose(true);
            }
        }
        #endregion

        #region 需要下载文件实体类

        class SynFileInfo
        {
            public string DocID { get; set; }
            public string DocName { get; set; }
            public string DownPath { get; set; }
            public string Ver { get; set; }
            public string LocalPath { get; set; }
            //public long FileSize { get; set; }
            //public string SynSpeed { get; set; }
            public string SynProgress { get; set; }
            public string SavePath { get { 
                    return Environment.CurrentDirectory + (this.LocalPath ?? "\\Programs\\Libs\\") + this.DocName; } }
            public DataGridViewRow RowObject { get; set; }
            public bool Async { get; set; } = true;
            public DateTime LastTime { get; set; }
            public bool IsIndustry { get; set; } = false;
            
        }

        #endregion

        #region 初始化GridView

        void InitDataGridView(DataGViewX dgv)
        {
            dgv.AutoGenerateColumns = false;//是否自动创建列
            dgv.AllowUserToAddRows = false;//是否允许添加行(默认：true)
            dgv.AllowUserToDeleteRows = false;//是否允许删除行(默认：true)
            dgv.AllowUserToResizeColumns = false;//是否允许调整大小(默认：true)
            dgv.AllowUserToResizeRows = false;//是否允许调整行大小(默认：true)
            //dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;//列宽模式(当前填充)(默认：DataGridViewAutoSizeColumnsMode.None)
            dgv.ColumnHeadersHeight = 30;//列表头高度(默认：20)
            dgv.MultiSelect = false;//是否支持多选(默认：true)
            dgv.ReadOnly = true;//是否只读(默认：false)
            dgv.RowHeadersVisible = false;//行头是否显示(默认：true)
            dgv.ColumnHeadersVisible = false;
            dgv.DefaultCellStyle.BackColor = Color.White;
            dgv.SelectionChanged += Dgv_SelectionChanged;
            dgv.Dock = DockStyle.Fill;
            

        }

        private void Dgv_SelectionChanged(object sender, EventArgs e)
        {
            ((DataGViewX)sender).ClearSelection();
        }

        #endregion
        private void FrmBatchDownload_Shown(object sender, EventArgs e)
        {
            AutoUpdate();
        }
        #region 添加下载任务并显示到列表中

        #region 业态更新判断
        private void AddIndustry(List<SynFileInfo> synFileInfo)
        {
            try
            {
                if (String.IsNullOrEmpty(ConfigController.ProgramFile)) {
                    return;
                }

                string ver = ConfigController.GetUpdateVer(ConfigController.DefaultConfigURL + @"/upload/getUpdFlgByUid");
                //NLogger.Instance.WriteLog(NLog.LogLevel.Info, string.Format("AddIndustry ver:" + ver));
                var url = string.Format("{0}{1}", ConfigController.UpdateURL, ConfigController.ProgramFile.Replace(".dll", "").ToLower());
                if (!string.IsNullOrEmpty(ver))
                {
                    url = string.Format("{0}{1}/ver/{2}", ConfigController.UpdateURL, ConfigController.ProgramFile.Replace(".dll", "").ToLower(), ver);
                    NLogger.Instance.WriteLog(NLog.LogLevel.Info, string.Format("AddIndustry:" + url + "/" + ConfigController.ProgramFile));
                    if (!ConfigController.HttpFileExist(url + "/" + ConfigController.ProgramFile))
                    {
                        url = "";
                    }
                    if (!String.IsNullOrEmpty(url))
                    {
                        synFileInfo.Add(new SynFileInfo()
                        {
                            DocName = ConfigController.ProgramFile,
                            DownPath = url,
                            LocalPath = @"\Programs\",
                            Ver = ver,
                            IsIndustry = true
                        });
                    }
                }else if (dics.ContainsKey("ProgramFile") && dics.ContainsKey("ProgramFileUrl"))
                {
                    synFileInfo.Add(new SynFileInfo()
                    {
                        DocName = dics["ProgramFile"].ToString(),
                        DownPath = dics["ProgramFileUrl"].ToString().Replace(dics["ProgramFile"].ToString(), ""),//"https://dev.zhangguizhinang.com/zgzn/catering",
                        LocalPath = @"\Programs\",
                        Ver = "",
                        IsIndustry = true
                    });
                }else if (!File.Exists(Environment.CurrentDirectory + @"\Programs\" + ConfigController.ProgramFile))
                {
                    synFileInfo.Add(new SynFileInfo()
                    {
                        DocName = ConfigController.ProgramFile,
                        DownPath = url,
                        LocalPath = @"\Programs\",
                        Ver = "",
                        IsIndustry = true
                    });
                }
            }
            catch (Exception ex) {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, string.Format("AddIndustry:{0}",ex.Message));
            }
        }


        #endregion
        void AddBatchDownload()
        {
            //清空行数据
            dgvDownLoad.Rows.Clear();
            //添加列表(建立多个任务)
            List<ArrayList> arrayListList = new List<ArrayList>();
            try
            {
                //再次new 避免WebClient不能I/O并发 
                string dllInfo = "[]";
                try
                {
                    WebClient client = new WebClient();
                    dllInfo = client.DownloadString(string.Format("{0}Libs\\{1}", ConfigController.UpdateURL, "ver.json"));

                }
                catch { }
                List<SynFileInfo> synFileInfo = JsonConvert.DeserializeObject<List<SynFileInfo>>(dllInfo);
                AddIndustry(synFileInfo);
                //NLogger.Instance.WriteLog(NLog.LogLevel.Info, string.Format("DownloadList:{0}", JsonConvert.SerializeObject(synFileInfo)));
                foreach (SynFileInfo synFile in synFileInfo)
                {
                    if (CheckUpdate(synFile))
                    {
                        AddDownloadFile(synFile);
                    }
                }
                dgvDownLoad.ClearSelection();
            }
            catch (Exception ex) {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, ex.Message);
            }

        }

        
        private void AddDownloadFile(SynFileInfo synFile)
        {
            try
            {
                synFile.DownPath = synFile.DownPath + "/" + synFile.DocName;
                if (ConfigController.HttpFileExist(synFile.DownPath))
                {
                    synFile.DocID = dgvDownLoad.Rows.Count.ToString();
                    synFile.SynProgress = "0";
                    synFile.Async = true;
                    NLogger.Instance.WriteLog(NLog.LogLevel.Info, "AddBatchDownload:" + JsonConvert.SerializeObject(synFile));
                    int rowIndex = dgvDownLoad.Rows.Add(new ArrayList()
                    {
                        synFile.DocID,
                        synFile.DocName,
                        "0%",
                        synFile.DownPath,//"https://dev.zhangguizhinang.com/zgzn/Libs/zgPrinter.dll",
                        synFile.SavePath,
                        true
                        }.ToArray());
                    synFile.RowObject = dgvDownLoad.Rows[rowIndex];
                    m_SynFileInfoList.Add(synFile);
                }
                else {
                    NLogger.Instance.WriteLog(NLog.LogLevel.Info, "文件不存在:" + JsonConvert.SerializeObject(synFile));
                }
            }
            catch (Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, ex.Message);
            }
            
        }

        private static bool CheckUpdate(SynFileInfo synFileInfo)
        {
            bool result = true;
            var currentVersion ="0.0.0.0";
            try
            {
                string filePath = synFileInfo.SavePath.Replace(".new", "");
                
                if (File.Exists(filePath))
                {
                    byte[] filedata = File.ReadAllBytes(filePath);
                    currentVersion = Assembly.Load(filedata).GetName().Version.ToString();
                    result = !String.IsNullOrEmpty(synFileInfo.Ver) && !new Version(synFileInfo.Ver).Equals(new Version(currentVersion));

                    if (synFileInfo.IsIndustry) {
                        ConfigController.IndustryVer = !String.IsNullOrEmpty(synFileInfo.Ver)?synFileInfo.Ver: currentVersion;
                        ConfigController.IndustryUpdated = result;
                        ConfigController.IndustryUpdatedVerInfo = result?string.Format("版本升级:{0}=》{1}", currentVersion, synFileInfo.Ver):"";
                    }

                }

                NLogger.Instance.WriteLog(NLog.LogLevel.Info, string.Format("{0}:{1}",synFileInfo?.SavePath , currentVersion));
            }
            catch (Exception ex) {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, synFileInfo?.SavePath + ex.Message);
            }
            return result;
        }
        #endregion
        
        public void SetMaxThread()
        {
            int maxThread = 1;
            try
            {
                if (!ThreadPool.SetMaxThreads(maxThread, 30))
                {
                    //maxThread = 1;
                    //ThreadPool.SetMaxThreads(maxThread, 30);
                }
                NLogger.Instance.WriteLog(NLog.LogLevel.Info, string.Format("设置最大线程数 : {0}", maxThread));
            }
            catch (Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, string.Format("设置最大线程数失败 : Message:{0}", ex.Message));

            }

        }
        private void FrmClose(bool issuccess) {
            try {
                
                this.Invoke(new Action(() =>
                {
                    this.ProgressLabel.Text = issuccess ? "系统加载完成. (〃'▽'〃)" : "网络异常，系统更新失败. (〃'▽'〃)";
                    Application.DoEvents();
                }));
            } catch { }
            
            if (this == null) return;
            ThreadPool.QueueUserWorkItem(new WaitCallback(
                (Mathilda) =>
                {
                    try
                    {
                        while (this.Opacity > 0)
                        {
                            Thread.Sleep(50);
                            this.Opacity -= 0.1;
                        }
                        if (File.Exists(exePath + ".new"))
                        {
                            File.Move(exePath, exePath + ".old");
                            File.Move(exePath + ".new", exePath);
                            this.DialogResult = DialogResult.OK;
                            
                        }
                        //if (dics.ContainsKey("reload")) { this.DialogResult = DialogResult.OK; }
                        this.Close();
                        
                    }
                    catch
                    { }
                }));
        }
        
        private void AutoUpdate()
        {
            //判断网络连接是否正常
            try
            {
                //添加DataGridView相关列信息
                AddGridViewColumns(dgvDownLoad);
                //新建任务
                AddBatchDownload();

                //判断是否还存在任务
                if (m_SynFileInfoList.Count > 0)
                {
                    NLogger.Instance.WriteLog(NLog.LogLevel.Info, "下载任务数：" + m_SynFileInfoList.Count);
                    //设置最大活动线程数以及可等待线程数
                    SetMaxThread();
                    List<SynFileInfo> m_SynFileInfoList_cp = m_SynFileInfoList;
                    foreach (SynFileInfo m_SynFileInfo in m_SynFileInfoList_cp)
                    {
                        //启动下载任务
                        ThreadPool.QueueUserWorkItem(new WaitCallback(StartDownLoad), m_SynFileInfo);

                    }

                }
                else
                {
                    FrmClose(true);
                }


            }
            catch (Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, string.Format("FrmShown:{0}", ex.Message));
                FrmClose(false);
            }
        }

    }

    

}
