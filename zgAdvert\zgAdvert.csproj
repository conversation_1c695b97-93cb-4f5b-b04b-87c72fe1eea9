﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{4239A961-8203-4C34-A5F2-8A9F7C7CD31A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>zgAdvert</RootNamespace>
    <AssemblyName>zgAdvert</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\out\Programs\Libs\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\out\Programs\Libs\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CefSharp">
      <HintPath>..\out\Programs\cefLib\CefSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="CefSharp.BrowserSubprocess.Core">
      <HintPath>..\out\Programs\cefLib\CefSharp.BrowserSubprocess.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="CefSharp.Core">
      <HintPath>..\out\Programs\cefLib\CefSharp.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="CefSharp.WinForms">
      <HintPath>..\out\Programs\cefLib\CefSharp.WinForms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="zgLogging, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\zgLogging.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zgUtils, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\zgUtils.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AdvertFactory.cs" />
    <Compile Include="Controls\AdvertHtmlControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\AdvertHtmlControl.Designer.cs">
      <DependentUpon>AdvertHtmlControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Files\FileManager.cs" />
    <Compile Include="Model\ADItem.cs" />
    <Compile Include="Model\Advert.cs" />
    <Compile Include="Model\News.cs" />
    <Compile Include="Model\Poster.cs" />
    <Compile Include="Model\Templates.cs" />
    <Compile Include="Network\GetAdsRequest.cs" />
    <Compile Include="Network\GetAdsResponse.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Controls\AdvertHtmlControl.resx">
      <DependentUpon>AdvertHtmlControl.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Extensions\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>