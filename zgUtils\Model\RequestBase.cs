﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using zgLogging;
using zgUtils.Controls;
using zgUtils.Extensions;
using zgUtils.Security;

namespace zgUtils.Model
{
    public class RequestBase
    {
        
        public string BaseUrl { get; set; }
        public string ActionUrl { get; set; }
        public virtual string RequestBodyJson { get; }
        public string Method { get; set; }

        public virtual string PostContent()
        {
            return string.Format("{0}", "{\"deviceId\":\""+ CommonApp.DeviceId + "\"}");
        }


        public string CreateURL()
        {
            string url = "";
            string deviceId = "";
            string version = "";
            string exeVer = "";
            string Authorization = "";            
            try
            {
                deviceId = CommonApp.DeviceId;
                version = CommonApp.Version;
                Authorization = CommonApp.Authorization;
                exeVer = CommonApp.Assembly.GetName().Version.ToString();
                string requestBodyJson = "{}";
                //if (!string.IsNullOrWhiteSpace(this.RequestBodyJson))
                //{
                //    requestBodyJson = this.RequestBodyJson;
                //}
                string timestamp = DateTime.Now.GetTimestamp();
                string sequence = DateTime.Now.DateTimeToStamp().ToString();
                string text = this.CreateSign(requestBodyJson, sequence, timestamp, deviceId, version);
                string arg = Uri.EscapeUriString(string.Format("sequence={0}&timestamp={1}&deviceId={2}&version={3}&exeVer={4}&sign={5}", new object[]
                {
                sequence,
                timestamp,
                deviceId,
                version,
                exeVer,
                text
                }));
                url = string.Format("{0}{1}?{2}", this.BaseUrl, this.ActionUrl,arg);
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
                Log.WriterExceptionLog(string.Format("URL 参数：{0}-{1}-{2}", deviceId, version, Authorization));
            }
            
            return url.Replace("??", "?");
        }
        private string CreateSign(string requestBodyJson, string sequence, string timestamp, string uuid,string version)
        {
            return string.Format("requestBodyJson={0}&sequence={1}&timestamp={2}&uuid={3}&version={4}", new object[]
            {
                requestBodyJson,
                sequence,
                timestamp,
                uuid,
                version
            }).GetMd5Hash();
        }


        public string ToJson(object targert)
        {
            return JsonConvert.SerializeObject(targert);
        }

        public string ToJson()
        {
            return JsonConvert.SerializeObject(this);
        }

        public RequestBase()
        {
            //this.DeviceId = FingerPrint.Value();
        }
        public RequestBase(string configURL)
        {
            this.BaseUrl = configURL;
        }
        public RequestBase(string baseUrl, string actionUrl) : this(baseUrl)
        {
            this.ActionUrl = actionUrl;
        }
    }
    public class RequestBase<TRequestBody> : RequestBase where TRequestBody : new()
    {
        public TRequestBody RequestBody { get; set; }

        private RequestBase()
        {
            this.RequestBody = Activator.CreateInstance<TRequestBody>();
        }
        public RequestBase(string baseUrl,string actionUrl) :this()
        {
            this.BaseUrl = baseUrl;
            this.ActionUrl = actionUrl;
        }
        public RequestBase(string baseUrl, string actionUrl, TRequestBody requestBody) : this(baseUrl, actionUrl)
        {
            this.RequestBody = requestBody;
        }
        public override string RequestBodyJson
        {
            get
            {
                return base.ToJson(this.RequestBody);
            }
        }

        public override string PostContent()
        {
            return string.Format("{0}", base.ToJson(this.RequestBody));
        }
    }
    public class RequestJsonStr : RequestBase {
        public RequestJsonStr(string baseUrl, string actionUrl) 
        {
            this.BaseUrl = baseUrl;
            this.ActionUrl = actionUrl;
        }
        public RequestJsonStr(string baseUrl, string actionUrl, string requestBody) : this(baseUrl, actionUrl)
        {
            this.RequestBody = requestBody;
        }
        public string RequestBody { get; set; }
        public override string PostContent()
        {
            return string.Format("{0}", this.RequestBody);
        }
    }
    public class RequestDeviceInfo : RequestBase
    {
        public SysProp RequestBody { get; set; }
        private RequestDeviceInfo()
        {
            RequestBody = SysProp.Instance;
        }
        public RequestDeviceInfo(string baseUrl) : this()
        {
            this.BaseUrl = baseUrl;
            this.ActionUrl = "";
        }
        public override string RequestBodyJson
        {
            get
            {
                return base.ToJson(this.RequestBody);
            }
        }

        public override string PostContent()
        {
            Dictionary<string, string> result = new Dictionary<string, string>();
            try
            {
                result.Add("deviceId", CommonApp.DeviceId);
                result.Add("info", base.ToJson(this.RequestBody));

            }
            catch{ 
                
            }
            return base.ToJson(result);
        }
    }
}
