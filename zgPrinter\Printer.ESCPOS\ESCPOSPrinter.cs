﻿using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Imaging;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Timers;
using zgLogging;
using zgPrinter.Model;
using zgPrinter.Printer.ESCPOS.EpsonCommands;

namespace zgPrinter.Printer.ESCPOS
{
    public class ESCPOSPrinter : AbstractPrinter
    {
        /// <summary>
        /// 网口打印失败报文队列
        /// </summary>
        private static readonly ConcurrentQueue<ResendContent> _failPrintContent = new ConcurrentQueue<ResendContent>();
        private static readonly ConcurrentDictionary<string, Socket> _dicSocket = new ConcurrentDictionary<string, Socket>(StringComparer.OrdinalIgnoreCase);
        private static System.Timers.Timer _resendTimer = new System.Timers.Timer(10 * 1000);
        private static System.Timers.Timer _keepAliveTimer = new System.Timers.Timer(10 * 1000);
        private static SemaphoreSlim _semaphore = new SemaphoreSlim(1);
        private byte[] _buffer;
        private readonly EscPos _command;
        private readonly Dictionary<int, int> _dicSeparator = new Dictionary<int, int>() { { 32, 32 }, { 48, 63 } };
        private readonly List<string> _leftPadTemplate = new List<string>() {
            "HBShoppingReceiptTemplate",
            "HBShoppingReceiptTemplate80",
            "SCShoppingReceiptTemplate",
            "tableform",
            "tableform80",
            "salesReportTemplate",
            "salesReportTemplate80",
            "CYRetreatFoodTemplate",
            "CYRetreatFoodTemplate80",
            "preSettlement80",
            "preSettlement",
            "retreatProductTemplate",
            "retreatProductTemplate80",
            "retreatFoodTemplate",
            "retreatFoodTemplate80",
            "statement",
            "statement80",
            "CYProductionNoteTemplate",
            "productionNoteTemplate",
            "SCBuyTimeCardTemplate",
            "FZRetreatProductTemplate",
            "FZShoppingReceiptTemplate",
            "goodsSalesReportTemplate",
            "shoppingReceiptTemplate",
            "pickup",
            "mailing"
        };
        private static readonly ManualResetEvent _timeoutObject = new ManualResetEvent(false);


        static ESCPOSPrinter()
        {
            initTimer();
        }

        /// <summary>
        /// 构造函数，代码页
        /// </summary>
        /// <param name="codePage"></param>
        public ESCPOSPrinter(string codePage = "GB18030")
        {
            _command = new EscPos(codePage);
        }

        public override void Print(string printerName, int printCount, bool isTag = false,int spaceLine = 4)
        {
            _buffer = ConvertPrintItems2ByteArray(spaceLine,true);
            if (_buffer == null)
            {
                return;
            }

            for (int i = 0; i < printCount; i++)
            {
                if (!RawPrinterHelper.SendBytesToPrinter(printerName, _buffer))
                    throw new ArgumentException("Unable to access printer : " + printerName);
            }
        }

        public override void Print(string printerName, IEnumerable<Tuple<int, byte[]>> printData, int sleepMS = 0)
        {
            foreach (var item in printData)
            {
                var printCount = item.Item1;
                for (int i = 0; i < printCount; i++)
                {
                    if (!RawPrinterHelper.SendBytesToPrinter(printerName, item.Item2))
                    {
                        throw new ArgumentException("Unable to access printer : " + printerName);
                    }
                    if (sleepMS > 0) {
                        Thread.Sleep(sleepMS);
                    }
                }
            }
        }

        public override void NetPrint(string ip, int printCount, bool isTag = false, int port = 9100, int spaceLine = 2)
        {
            _buffer = ConvertPrintItems2ByteArray(spaceLine);
            if (_buffer == null)
            {
                return;
            }
            try
            {
                var socket = getSocket(ip, port);
                if (socket == null) {
                    return;
                }
                socketSendData(socket, _buffer, printCount);
            }
            catch (Exception ex)
            {
                Log.WriterNormalLog($"NetPrint ex = {ex.Message}");
                enqueueRestQueue(ip, port, _buffer, printCount);
                throw;
            }

        }

        /// <summary>
        /// 网口批量打印
        /// </summary>
        /// <param name="ip">打印机IP</param>
        /// <param name="printData">打印内容，份数，打印数据</param>
        /// <param name="sleepMS">打印间隔毫秒数</param>
        /// <param name="port">打印机端口号，默认9100</param>
        public override void NetPrint(string ip,IEnumerable<Tuple<int,byte[]>> printData, int sleepMS, int port = 9100)
        {
            foreach (var item in printData)
            {
                var printCount = item.Item1;
                try
                {
                    var socket = getSocket(ip, port);
                    socketSendData(socket, item.Item2, printCount);
                    Thread.Sleep(sleepMS);
                }
                catch (SocketException ex)
                {
                    Log.WriterNormalLog($"NetPrint ex = {ex.Message}");
                    enqueueRestQueue(ip, port, _buffer, printCount);
                    throw;
                }
                catch (Exception ex)
                {
                    Log.WriterNormalLog($"NetPrint ex = {ex.Message}");
                    enqueueRestQueue(ip, port, _buffer, printCount);
                    throw;
                }
            }
        }

        /// <summary>
        /// 打开钱箱
        /// </summary>
        public void OpenDrawer()
        {
            _command.OpenDrawer();
        }

        public void ClearBuffer()
        {
            this._buffer = new byte[0];
        }
        
        /// <summary>
        /// 转换
        /// </summary>
        /// <param name="spaceLine"></param>
        /// <param name="cutPaper"></param>
        /// <returns></returns>
        public byte[] ConvertPrintItems2ByteArray(int spaceLine = 2, bool cutPaper = false)
        {
            _command.Clear();
            setDefaultTextSetting();
            _command.SetLineHeight((Byte)this.LineHeight);
            foreach (var item in PrintItems)
            {
                if (!string.IsNullOrEmpty(item.DisplayGroup) && !DataSource.ContainsKey(item.DisplayGroup))
                {
                    continue;
                }
                if (item.ContentType == EnumContentType.Command)
                {
                    printCmd(item as PrintElementCommand, DataSource);
                }
                if (item.ContentType == EnumContentType.GoodsTable)
                {
                    setDefaultTextSetting();
                    if (DataSource.ContainsKey("template"))
                    {
                        if (_leftPadTemplate.Contains(DataSource["template"].ToString()))
                        {
                            printGoodsTableTemp(item as PrintElementGoodsDataTable, DataSource);
                        }
                        else
                        {
                            printGoodsTable(item as PrintElementGoodsDataTable, DataSource);
                        }
                    }
                    else
                    {
                        printGoodsTableTemp(item as PrintElementGoodsDataTable, DataSource);
                    }
                }
                if (item.ContentType == EnumContentType.Image)
                {
                    printImg(item as PrintElementImage, DataSource);
                    _command.SetLineHeight((Byte)this.LineHeight);
                }
                if (item.ContentType == EnumContentType.String)
                {
                    printStringLine(item as PrintElementString, DataSource);
                }

            }
            _command.NewLines(spaceLine);
            if (cutPaper)
            {
                _command.PartialPaperCut();
            }
            _buffer = _command.PrintContent;
            return _buffer;
        }

        #region 打印元素

        /// <summary>
        /// 打印文本行
        /// </summary>
        /// <param name="item"></param>
        /// <param name="dataSource"></param>

        void printStringLine(PrintElementString item, Dictionary<string, object> dataSource)
        {
            setDefaultTextSetting();
            //如果没有文本or没有绑定的数据源则退出
            if (item.Content == null || !dataSource.ContainsKey(item.DataMember))
            {
                return;
            }
            var content = string.Format(item.Format, dataSource[item.DataMember].ToString());
            setAlign(item.TextAlign);
            setFont(item.ContentFont, dataSource);
            string[] ss = content.Split(new string[] { "↵" }, StringSplitOptions.None);
            foreach (var str in ss)
            {
                _command.Append(str);
            }
        }

        /// <summary>
        /// 打印图片
        /// </summary>
        /// <param name="item"></param>
        /// <param name="dataSource"></param>
        void printImg(PrintElementImage item, Dictionary<string, object> dataSource)
        {
            Bitmap img;
            if (!dataSource.ContainsKey(item.DataMember))
            {
                return;
            }
            if (dataSource[item.DataMember] is Bitmap)
            {
                img = dataSource[item.DataMember] as Bitmap;
            }
            else
            {
                img = new Bitmap(dataSource[item.DataMember].ToString());
            }

            var enumReceiptPaperSize = (EnumReceiptPaperSize)(Convert.ToInt32(dataSource["cols"]));
            var imgSize = enumReceiptPaperSize == EnumReceiptPaperSize.Paper50 ? ImageProcess.PAPER_SIZE_58 : ImageProcess.PAPER_SIZE_80;
            var newBitMap = new ImageProcess().Process(img, imgSize);
            _command.AlignCenter();
            _command.Image(newBitMap);
        }

        /// <summary>
        /// 打印商品列表,专门处理使用getContent处理字段的小票
        /// </summary>
        /// <param name="item"></param>
        /// <param name="dataSource"></param>
        void printGoodsTableTemp(PrintElementGoodsDataTable item, Dictionary<string, object> dataSource)
        {
            var enumReceiptPaperSize = (EnumReceiptPaperSize)(Convert.ToInt32(dataSource["cols"]));
            var columnSpace = (int)enumReceiptPaperSize;
            var dataStruct = item.DataStruct;
            if (!dataSource.ContainsKey(item.DataMember))
            {
                return;
            }
            setFont(item.BodyFont, dataSource);

            var spaceScale = 1;
            if (item.BodyFont.Size >= 20) {
                spaceScale = 3;
            }
            else if (item.BodyFont.Size >= 12) {
                spaceScale = 2;
            }
            var dt = dataSource[item.DataMember] as DataTable;
            if (item.PrintHead)
            {
                List<string> rowContent = new List<string>();
                foreach (DataRow configRow in dataStruct.Rows)
                {
                    var isPrint = Convert.ToBoolean(configRow["col_isPrint"]);
                    var isSingleLine = Convert.ToBoolean(configRow["col_singleLine"]);
                    if (!isPrint || isSingleLine)
                    {
                        continue;
                    }
                    var title = configRow["col_head"].ToString().rigthPadContent(columnSpace, Convert.ToDouble(configRow["col_width"]) / (100*spaceScale));
                    rowContent.Add(title);
                }
                var rowContentStr = string.Join("", rowContent);
                _command.Append(rowContentStr);

                if (item.PrintSeparatorLine)
                {
                    _command.Append(_command.Separator(_dicSeparator[columnSpace]));
                }
            }

            setFont(item.BodyFont, dataSource);
            foreach (DataRow row in dt.Rows)
            {
                List<string> rowContent = new List<string>();
                for (int i = 0; i < dataStruct.Rows.Count; i++)
                {
                    var configRow = dataStruct.Rows[i];
                    var isSignleLine = Convert.ToBoolean(configRow["col_singleLine"]);
                    var colName = configRow["col_dataMember"].ToString();

                    if (!dt.Columns.Contains(colName))
                    {
                        continue;
                    }
                    var colValue = row[colName].ToString();
                    if (string.IsNullOrEmpty(colValue))
                    {
                        continue;
                    }
                    if (isSignleLine)
                    {
                        if (!string.IsNullOrEmpty(colValue))
                        {
                            _command.Append(colValue);
                        }
                    }
                    else
                    {
                        var title = colValue.rigthPadContent(columnSpace, Convert.ToDouble(configRow["col_width"]) / (100 * spaceScale));
                        rowContent.Add(title);
                    }
                }
                var rowContentStr = string.Join("", rowContent);
                _command.Append(rowContentStr);
            }
        }

        void printGoodsTable(PrintElementGoodsDataTable item, Dictionary<string, object> dataSource)
        {
            var enumReceiptPaperSize = (EnumReceiptPaperSize)(Convert.ToInt32(dataSource["cols"]));
            var columnSpace = (int)enumReceiptPaperSize;
            var dataStruct = item.DataStruct;
            if (!dataSource.ContainsKey(item.DataMember))
            {
                return;
            }
            setFont(item.BodyFont, dataSource);
            var dt = dataSource[item.DataMember] as DataTable;
            if (item.PrintHead)
            {
                List<string> rowContent = new List<string>();
                foreach (DataRow configRow in dataStruct.Rows)
                {
                    var isPrint = Convert.ToBoolean(configRow["col_isPrint"]);
                    var isSingleLine = Convert.ToBoolean(configRow["col_singleLine"]);
                    if (!isPrint || isSingleLine)
                    {
                        continue;
                    }
                    var widthSpace = columnSpace * Convert.ToInt32(configRow["col_width"]) / 100;
                    rowContent.Add(configRow["col_head"].ToString().PadRight(widthSpace, ' '));
                }
                var rowContentStr = string.Join("\t", rowContent);
                _command.Append(rowContentStr);

                if (item.PrintSeparatorLine)
                {
                    _command.Append(_command.Separator(_dicSeparator[columnSpace]));
                }
            }
            setFont(item.BodyFont, dataSource);
            foreach (DataRow row in dt.Rows)
            {
                List<string> rowContent = new List<string>();
                for (int i = 0; i < dataStruct.Rows.Count; i++)
                {
                    var configRow = dataStruct.Rows[i];
                    var isSignleLine = Convert.ToBoolean(configRow["col_singleLine"]);
                    var colName = configRow["col_dataMember"].ToString();
                    if (!dt.Columns.Contains(colName) || string.IsNullOrEmpty(row[colName].ToString()))
                    {
                        continue;
                    }
                    if (isSignleLine)
                    {
                        if (!string.IsNullOrEmpty(row[colName].ToString()))
                        {
                            _command.Append(row[colName].ToString());
                        }
                    }
                    else
                    {
                        var widthSpace = columnSpace * Convert.ToInt32(configRow["col_width"]) / 100;
                        rowContent.Add(row[colName].ToString().PadRight(widthSpace, ' '));
                    }
                }
                var rowContentStr = string.Join("\t", rowContent);
                _command.Append(rowContentStr);
            }
        }

        /// <summary>
        /// 打印命令
        /// </summary>
        /// <param name="item"></param>
        void printCmd(PrintElementCommand item, Dictionary<string, object> dataSource)
        {
            var enumReceiptPaperSize = Convert.ToInt32(dataSource["cols"]);
            switch (item.Content)
            {
                case EnumPrintCommand.AddSeparatorLine:
                    setAlign(item.TextAlign);
                    _command.Append(_command.Separator(_dicSeparator[enumReceiptPaperSize]));
                    break;
                case EnumPrintCommand.AddEmptyLine:
                    _command.NewLine();
                    break;
                default:
                    break;
            }
        }

        #endregion

        #region 工具函数

        void setDefaultTextSetting()
        {
            _command.AlignLeft();
            _command.BoldMode(EnumPrinterModeState.Off);
            _command.NormalWidth();
        }

        void setAlign(EnumTextAlign enumTextAlign)
        {
            switch (enumTextAlign)
            {
                case EnumTextAlign.Left:
                    _command.AlignLeft();
                    break;
                case EnumTextAlign.Center:
                    _command.AlignCenter();
                    break;
                case EnumTextAlign.Right:
                    _command.AlignRight();
                    break;
                default:
                    break;
            }
        }

        void setFont(Font font)
        {
            if (font.Bold)
            {
                _command.BoldMode(EnumPrinterModeState.On);
            }
            var fontSize = font.Size;
            if (fontSize >= 20)
            {
                _command.DoubleWidth3();
            }
            else if (fontSize >= 12)
            {
                _command.DoubleWidth2();
            }
            else
            {
                _command.NormalWidth();
            }
        }

        void setFont(Font font, Dictionary<string, object> dataSource)
        {
            if (dataSource.ContainsKey("gfont"))
            {
               var globalFont = JsonConvert.DeserializeObject<Font>(dataSource["gfont"].ToString());
                setFont(globalFont);
            }
            else {
                setFont(font);
            }
        }

        #endregion

        #region Socket相关

        #region Timer

        /// <summary>
        /// 初始化计时器
        /// </summary>
        private static void initTimer()
        {
            _resendTimer.AutoReset = true;
            _resendTimer.Enabled = true;
            _resendTimer.Elapsed += new System.Timers.ElapsedEventHandler(resendTimer_Elapsed);
            _resendTimer.Start();

            _keepAliveTimer.AutoReset = true;
            _keepAliveTimer.Enabled = true;
            _keepAliveTimer.Elapsed += new System.Timers.ElapsedEventHandler(keepAliveTimer_Elapsed);
            _keepAliveTimer.Start();
        }

        /// <summary>
        /// 重发定时器执行逻辑
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private static void resendTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            try
            {
                _resendTimer.Stop();
                List<ResendContent> retryContent = new List<ResendContent>();
                int index = 0;
                while (!_failPrintContent.IsEmpty)
                {
                    ResendContent item = null;
                    try
                    {
                        if (_failPrintContent.TryDequeue(out item))
                        {
                            Log.WriterNormalLog("failPrintContent.Send.1");
                            var socket = getSocket(item.IP, item.Port);
                            if (socket != null)
                            {
                                socketSendData(socket, item.Buffer, item.PrintCount);
                            }
                        }
                        else
                        {
                            Log.WriterNormalLog("failPrintContent.Send.3");
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.WriterNormalLog($"failPrintContent.Send error:{ex.Message}");
                        if (item != null)
                        {
                            retryContent.Add(item);
                        }
                    }

                    index++;
                    if (index > 50)
                    {
                        break;
                    }
                }
                foreach (var item in retryContent)
                {
                    _failPrintContent.Enqueue(item);
                }
            }
            finally
            {
                _resendTimer.Start();
            }
        }

        /// <summary>
        /// 心跳定时器
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private static void keepAliveTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            var buffer = new InitializePrint().Initialize();
            foreach (var item in _dicSocket)
            {
                try
                {
                    var key = item.Key.Split(':');
                    var socket = getSocket(key[0], int.Parse(key[1]));
                    if (socket != null) {
                        socket.Send(buffer);
                    }
                }
                catch (Exception ex)
                {
                    Socket tSocket;
                    _dicSocket.TryRemove(item.Key,out tSocket);
                    Log.WriterNormalLog("keepAliveTimer_Elapsed.error:" + ex.Message);
                }
            }
        }

        #endregion

        /// <summary>
        /// 获取连接
        /// </summary>
        /// <param name="ip"></param>
        /// <param name="port"></param>
        /// <returns></returns>
        private static Socket getSocket(string ip, int port)
        {
            try
            {
                _semaphore.Wait();
                var key = $"{ip}:{port}";
                if (!_dicSocket.ContainsKey(key))
                {
                    var clientSocket = createSocket(ip, port, true);
                    _dicSocket[key] = clientSocket;
                }
                Socket currentSocket = null;
                _dicSocket.TryGetValue(key, out currentSocket);
                if (currentSocket == null) {
                    _dicSocket.TryRemove(key,out currentSocket);
                    Log.WriterNormalLog($"getSocket.currentSocket is null {ip}:{port}");
                    return null;
                }

                if (!currentSocket.Connected)
                {
                    currentSocket.Shutdown(SocketShutdown.Both);
                    currentSocket.Disconnect(true);
                    currentSocket.Close();
                    _dicSocket.TryRemove(key, out currentSocket);
                    var clientSocket = createSocket(ip, port, true);
                    _dicSocket.TryAdd(key, clientSocket);
                }
                return _dicSocket[key];
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 创建连接
        /// </summary>
        /// <param name="ip"></param>
        /// <param name="port"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        private static Socket createSocket(string ip, int port = 9100, bool content = false)
        {
            var clientSocket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
            clientSocket.IOControl(IOControlCode.KeepAliveValues, getKeepAliveData(), null);
            clientSocket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.KeepAlive, true);
            int contentTimeOutMS = 2 * 1000;
            if (content)
            {
                try
                {
                    _timeoutObject.Reset();
                    clientSocket.BeginConnect(ip, port, manualResetEventCallBack, clientSocket);
                    if (!_timeoutObject.WaitOne(contentTimeOutMS, false))
                    {
                        clientSocket.Close();
                        clientSocket = null;
                    }
                }
                catch (Exception ex)
                {
                    Log.WriterExceptionLog("createSocket:" + ex.Message);
                    clientSocket = null;
                }

            }
            return clientSocket;
        }

        /// <summary>
        /// socket发送消息
        /// </summary>
        /// <param name="client"></param>
        /// <param name="content"></param>
        /// <param name="printCount"></param>
        private static void socketSendData(Socket client, byte[] content, int printCount = 0)
        {
            if (client.Connected)
            {
                for (int i = 0; i < printCount; i++)
                {
                    var newBuffer = content.AddBytes(new byte[] { 0x0A }).AddBytes(new PaperCut().Full());
                    var sendCount = client.Send(newBuffer);
                    Log.WriterNormalLog("socketSendData.Send.2 =" + sendCount);
                    Thread.Sleep(1000);
                }
            }
            else
            {
                throw new Exception("无法连接打印机");
            }
        }

        /// <summary>
        /// 加入失败队列
        /// </summary>
        /// <param name="ip"></param>
        /// <param name="port"></param>
        /// <param name="buffer"></param>
        /// <param name="printCount"></param>
        private void enqueueRestQueue(string ip, int port, byte[] buffer, int printCount)
        {
            ResendContent resendContent = new ResendContent();
            resendContent.IP = ip;
            resendContent.Port = port;
            resendContent.PrintCount = printCount;
            resendContent.Buffer = buffer;
            _failPrintContent.Enqueue(resendContent);
        }

        /// <summary>
        /// keep alive数据
        /// </summary>
        /// <returns></returns>
        private static byte[] getKeepAliveData()
        {
            uint dummy = 0;
            byte[] inOptionValues = new byte[Marshal.SizeOf(dummy) * 3];
            BitConverter.GetBytes((uint)1).CopyTo(inOptionValues, 0);
            BitConverter.GetBytes((uint)1000 * 60).CopyTo(inOptionValues, Marshal.SizeOf(dummy));//keep-alive间隔
            BitConverter.GetBytes((uint)1000).CopyTo(inOptionValues, Marshal.SizeOf(dummy) * 2);// 尝试间隔
            return inOptionValues;
        }

        private static void manualResetEventCallBack(IAsyncResult asyncresult)
        {
            //使阻塞的线程继续        
            _timeoutObject.Set();
        }

        #endregion
    }
}
