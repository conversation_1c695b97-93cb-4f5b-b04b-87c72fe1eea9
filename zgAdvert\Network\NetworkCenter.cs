﻿using Newtonsoft.Json;
using System;
using System.IO;
using System.Net;
using System.Text;
using zgLogging;
using zgUtils.Controls;

namespace zgAdvert.Network
{
	public class NetworkCenter
	{
		public static NetworkCenter Instance
		{
			get
			{
				if (NetworkCenter.instance == null)
				{
					NetworkCenter.instance = new NetworkCenter();
				}
				return NetworkCenter.instance;
			}
		}

		private HttpWebRequest GetRequest<TRequest>(TRequest request) where TRequest : RequestBase
		{
			string text = request.CreateURL();
			Log.WriterNormalLog(string.Format("win客户端发起请求-->HTTP URL:{0}", text));
			HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(text);
			httpWebRequest.Method = "Post";
			httpWebRequest.ContentType = "application/json; charset=UTF-8";
			httpWebRequest.Headers.Add("Authorization", request.Authorization);
			httpWebRequest.Timeout = 10000;
			Stream requestStream = httpWebRequest.GetRequestStream();
			string text2 = request.PostContent();
			Log.WriterNormalLog(text2);
			byte[] bytes = Encoding.UTF8.GetBytes(text2);
			if (bytes != null)
			{
				requestStream.Write(bytes, 0, bytes.Length);
			}
			requestStream.Close();
			return httpWebRequest;
		}

		private TResponse InitResponse<TResponse>(Stream streamResponse, TResponse responseResult) where TResponse : ResponseBase, new()
		{
			try
			{
				using (MemoryStream memoryStream = new MemoryStream())
				{
					streamResponse.CopyTo(memoryStream);
					byte[] bytes = memoryStream.ToArray();
					string @string = Encoding.UTF8.GetString(bytes);
					if (!string.IsNullOrWhiteSpace(@string))
					{
						Log.WriterNormalLog(string.Format("请求成功服务端回复{0}", @string));
						responseResult = JsonConvert.DeserializeObject<TResponse>(@string);
					}
					else
					{
						responseResult.Message = "返回空";
					}
				}
			}
			catch (Exception ex)
			{
				Log.WriterExceptionLog(string.Format("请求失败信息:" + ex.Message, new object[0]));
				responseResult.Message = ex.Message;
			}
			return responseResult;
		}

		public void SendRequest<TRequest, TResponse>(TRequest request, Action<TResponse> response) where TRequest : RequestBase where TResponse : ResponseBase, new()
		{
			TResponse responseResult = Activator.CreateInstance<TResponse>();
			responseResult.Code = -801;
			try
			{
				HttpWebRequest webRequest = this.GetRequest<TRequest>(request);
				webRequest.BeginGetResponse(delegate (IAsyncResult result)
				{
					try
					{
						Stream responseStream = ((HttpWebResponse)webRequest.EndGetResponse(result)).GetResponseStream();
						responseResult = this.InitResponse<TResponse>(responseStream, responseResult);
						response(responseResult);
						webRequest.Abort();
					}
					catch (Exception ex2)
					{
						webRequest.Abort();
						Log.WriterExceptionLog(string.Format("请求失败信息:" + ex2.Message, new object[0]));
						responseResult.Message = ex2.Message;
						response(responseResult);
					}
				}, webRequest);
			}
			catch (Exception ex)
			{
				responseResult.Message = ex.Message;
				response(responseResult);
			}
		}

		public TResponse SendRequest<TRequest, TResponse>(TRequest request) where TRequest : RequestBase where TResponse : ResponseBase, new()
		{
			TResponse tresponse = Activator.CreateInstance<TResponse>();
			tresponse.Code = -801;
			HttpWebRequest httpWebRequest = null;
			try
			{
				httpWebRequest = this.GetRequest<TRequest>(request);
				Stream responseStream = ((HttpWebResponse)httpWebRequest.GetResponse()).GetResponseStream();
				this.InitResponse<TResponse>(responseStream, tresponse);
			}
			catch (Exception ex)
			{
				tresponse.Message = ex.Message;
			}
			if (httpWebRequest != null)
			{
				httpWebRequest.Abort();
			}
			return tresponse;
		}

		public void GetUrlRequest(string url)
		{
			HttpWebRequest webRequest = (HttpWebRequest)WebRequest.Create(url);
			webRequest.Method = "Get";
			webRequest.ContentType = "application/x-www-form-urlencoded; charset=UTF-8";
			webRequest.Timeout = 10000;
			Log.WriterExceptionLog(string.Format("调用展现量上报请求接口:{0}", url));
			try
			{
				webRequest.BeginGetResponse(delegate (IAsyncResult response)
				{
					try
					{
						Stream responseStream = ((HttpWebResponse)webRequest.EndGetResponse(response)).GetResponseStream();
						using (MemoryStream memoryStream = new MemoryStream())
						{
							responseStream.CopyTo(memoryStream);
							byte[] bytes = memoryStream.ToArray();
							string @string = Encoding.UTF8.GetString(bytes);
							if (@string != null)
							{
								Log.WriterNormalLog(@string);
							}
						}
					}
					catch (Exception ex2)
					{
						Log.WriterExceptionLog("上报展示记录异常:" + ex2.Message);
					}
				}, webRequest);
			}
			catch (Exception ex)
			{
				Log.WriterExceptionLog("上报展示记录异常:" + ex.Message);
			}
		}

		private static NetworkCenter instance;
	}
}
