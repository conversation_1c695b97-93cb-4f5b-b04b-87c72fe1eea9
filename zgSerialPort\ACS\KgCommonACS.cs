﻿using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using zgSerialPort.Common;

namespace zgSerialPort.ACS
{
    [ACSAttribute(ModelName = "公斤电子秤通用", IsRelease = false, Index = 99)]
    public class KgCommonACS : BaseACS
    {
        public override void CommDataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            var serialPort = (System.IO.Ports.SerialPort)sender;
            if (serialPort.IsOpen)
            {
                try
                {
                    var reader = serialPort.ReadLine();
                    var reg = new Regex(@"(\d+(\.\d+)?)");
                    var msg = reg.Match(reader).ToString();
                    decimal kgValue;
                    if (Decimal.TryParse(msg, out kgValue)) {
                        CallbackAction((kgValue*1000).ToString());
                    }
                }
                catch { }
            }
        }
    }
}
