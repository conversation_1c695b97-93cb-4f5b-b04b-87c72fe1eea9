﻿using zgPrinter.Printer.ESCPOS.Interface;

namespace zgPrinter.Printer.ESCPOS.EpsonCommands
{
    public class FontWidth : IFontWidth
    {
        public byte[] Normal()
        {
            return new byte[] { 27, '!'.ToByte(), 0, 29, '!'.ToByte(), 00 };
        }

        public byte[] DoubleWidth2()
        {
            return new byte[] { 0x1D, 0x21, 0x11 };
        }

        public byte[] DoubleWidth3()
        {
            return new byte[] { 0x1D, 0x21, 0x22 };
        }
    }
}

