﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.Text;
using System.Linq;

namespace PrintCore
{
    public class Printer : IPrinter
    {

        #region fields
        PrintDocument _printDoc = new PrintDocument();

        /// <summary>
        /// 打印对象打印宽度(根据英寸换算而来,paperWidth * 3.937)
        /// </summary>
        readonly int _paperWidth;
        readonly int _paperHight;
        const float _charProportion = 0.7352f;
        static float _lineHeightProportion = 1.6f;
        const string _fontName = "仿宋";

        IList<Action<Graphics>> _printActions = new List<Action<Graphics>>();

        /// <summary>
        /// 当前的打印高度，当调用换行或者图片打印时会增加此字段值
        /// </summary>
        float _currentheight = 0;

        float NewLineOffset { get; set; } = (int)FontSize.Normal * _lineHeightProportion;

        #endregion

        #region ctor

        /// <summary>
        /// 初始化机打印对象
        /// </summary>
        /// <param name="PrinterName">打印机名称</param>
        /// <param name="paperWidth">打印纸宽度</param>
        /// <param name="paperHight">打印纸高度</param>
        internal Printer(string PrinterName, double paperWidth, double paperHight)
        {
            
             //3.937为一个打印单位(打印单位:80(实际宽度72.1),58（实际宽度48）)
            _paperWidth = Convert.ToInt32(Math.Ceiling(paperWidth * 3.937));
            _paperHight = Convert.ToInt32(Math.Ceiling(paperHight * 3.937));
            _printDoc.PrinterSettings.PrinterName = PrinterName;
            _printDoc.PrintPage += PrintPageDetails;
            _printDoc.DefaultPageSettings.PaperSize = new PaperSize("DataOrder", _paperWidth, _paperHight);
            _printDoc.PrintController = new StandardPrintController();
        }
        public void setLandscape(bool isLandscape) {
            _printDoc.DefaultPageSettings.Landscape = isLandscape;
            if (isLandscape) {
                _printDoc.DefaultPageSettings.PaperSize = new PaperSize("DataOrder", _printDoc.DefaultPageSettings.PaperSize.Height, _printDoc.DefaultPageSettings.PaperSize.Width);

            }
        }
        public void setLineHeight(int lineheight)
        {
            switch (lineheight)
            {
                case 20:
                    _lineHeightProportion = 1.3f;
                    break;
                case 40:
                    _lineHeightProportion = 1.4f;
                    break;
                case 70:
                    _lineHeightProportion = 1.7f;
                    break;
                case 80:
                    _lineHeightProportion = 1.9f;
                    break;
                default:
                    _lineHeightProportion = 1.6f;
                    break;
            }
        }
        public void setSetting(float lineHeight) {
            //_printDoc.OriginAtMargins = true;
            //_printDoc.DefaultPageSettings.Margins = new Margins(0, 0, 0, 0);
            _lineHeightProportion = lineHeight;
            //NewLineOffset = (int)FontSize.micro * _lineHeightProportion;
        }

        #endregion

        #region eventHandler
        void PrintPageDetails(object sender, PrintPageEventArgs e)
        {
            foreach (var item in _printActions)
            {
                item(e.Graphics);
            }
        }
        #endregion

        #region IPrinterImplement
        public void NewRow(FontSize fontSize = FontSize.Normal)
        {
            _printActions.Add((g) =>
            {
                _currentheight += NewLineOffset;
                NewLineOffset = (int)fontSize * _lineHeightProportion;
            });
        }

        public void PrintText(string content, FontSize fontSize = FontSize.Normal, StringAlignment stringAlignment = StringAlignment.Near, float width = 1, float offset = 0)
        {
            _printActions.Add((g) =>
            {
                float contentWidth = width == 1 ? _paperWidth * (1 - offset) : width * _paperWidth;
                string newContent = ContentWarp(content, fontSize, contentWidth, out var rowNum);
                var font = new Font(_fontName, (int)fontSize, FontStyle.Bold);
                var point = new PointF(offset * _paperWidth, _currentheight);
                var size = new SizeF(contentWidth, (int)fontSize * _lineHeightProportion * rowNum);
                var layoutRectangle = new RectangleF(point, size);
                var format = new StringFormat
                {
                    Alignment = stringAlignment,
                    FormatFlags = StringFormatFlags.NoWrap
                };
                g.DrawString(newContent, font, Brushes.Black, layoutRectangle, format);
                float thisHeightOffset = rowNum * (int)fontSize * _lineHeightProportion;
                
                if (thisHeightOffset > NewLineOffset) NewLineOffset = thisHeightOffset;
            });
        }

        /// <summary>
        /// 打印文本
        /// </summary>
        /// <param name="content">要打印的文本</param>
        /// <param name="fontSize">要打印的文本的字号</param>
        /// <param name="stringAlignment">文本的水平浮动方向</param>
        /// <param name="width"></param>
        /// <param name="offset"></param>
        /// <param name="titleFontSize">文本的title文字的字号，会影响矩形的高度</param>
        public void PrintTextHeightUseTitileHeight(string content, FontSize fontSize = FontSize.Normal, StringAlignment stringAlignment = StringAlignment.Near, float width = 1, float offset = 0, FontSize titleFontSize = FontSize.Normal)
        {
            _printActions.Add((g) =>
            {
                float contentWidth = width == 1 ? _paperWidth * (1 - offset) : width * _paperWidth;
                string newContent = ContentWarp(content, fontSize, contentWidth, out var rowNum);
                var font = new Font(_fontName, (int)fontSize, FontStyle.Bold);
                var point = new PointF(offset * _paperWidth, _currentheight);
                var size = new SizeF(contentWidth, (int)fontSize * _lineHeightProportion * rowNum);
                //垂直居中
                if (rowNum == 1)
                {
                    var yOffset = (size.Height - font.Height) / 2;
                    if (yOffset > 0)
                    {
                        point.Y += yOffset;
                    }
                } 
                var layoutRectangle = new RectangleF(point, size);
                var format = new StringFormat
                {
                    Alignment = stringAlignment,
                    LineAlignment = StringAlignment.Center,
                    FormatFlags = StringFormatFlags.NoWrap
                };
                g.DrawString(newContent, font, Brushes.Black, layoutRectangle, format);
                //参考框
                //Pen blackPen = new Pen(Color.Black, 1);
                //g.DrawRectangle(blackPen, new Rectangle((int)(offset * _paperWidth), (int)_currentheight, (int)contentWidth, (int)((int)titleFontSize * _lineHeightProportion * rowNum)));
                
                float thisHeightOffset = rowNum * (int)fontSize * _lineHeightProportion;
                if (thisHeightOffset > NewLineOffset) NewLineOffset = thisHeightOffset;
            });
        }

        public float PrintTitle(string content, FontSize fontSize = FontSize.Normal, float offset = 0)
        {
            float nowWidth = 0;
            float contentWidth = _paperWidth * (1 - offset);
            float newOffset = offset;
            foreach (char item in content)
            {
                int code = Convert.ToInt32(item);
                float charWidth = code < 128 ? _charProportion * (int)fontSize : _charProportion * (int)fontSize * 2;
                nowWidth += charWidth;
            }
            if (nowWidth > 0) newOffset = nowWidth / _paperWidth;
            _printActions.Add((g) =>
            {
                var font = new Font(_fontName, (int)fontSize, FontStyle.Bold);
                var point = new PointF(offset * _paperWidth, _currentheight);
                var size = new SizeF(contentWidth, (int)fontSize * _lineHeightProportion);
                var layoutRectangle = new RectangleF(point, size);
                var format = new StringFormat
                {
                    Alignment = StringAlignment.Near,
                    FormatFlags = StringFormatFlags.NoWrap
                };
                g.DrawString(content, font, Brushes.Black, layoutRectangle, format);

            });
            return newOffset;

        }

        public void Finish()
        {
            _printDoc.Print();
            _printDoc.Dispose();
            _printDoc = new PrintDocument();
            _printActions.Clear();
        }
        public void PrintImage(string code, StringAlignment stringAlignment = StringAlignment.Near)
        {
            var bitmap = PrintCore.BarcodeHelper.Generate2(code, 170, 55);
            PrintImage(bitmap, StringAlignment.Center);
        }
        public void PrintImage(Image image, StringAlignment stringAlignment = StringAlignment.Near)
        {
            _printActions.Add((g) =>
            {
                int x = 0;
                switch (stringAlignment)
                {
                    case StringAlignment.Near:
                        break;
                    case StringAlignment.Center:
                        x = (_paperWidth - image.Width) / 2;
                        break;
                    case StringAlignment.Far:
                        x = _paperWidth - image.Width;
                        break;
                    default:
                        break;
                }
                var point = new Point(x, Convert.ToInt32(_currentheight));
                var size = new Size(image.Width, image.Height);
                var rectangle = new Rectangle(point, size);
                g.DrawImage(image, rectangle);
                NewLineOffset = image.Height;
            });
        }
        public void PrintQrcode(Image image, StringAlignment stringAlignment = StringAlignment.Near)
        {
            _printActions.Add((g) =>
            {
                int x = 0;
                int width = 120;
                var size = new Size(width, width);
                Bitmap bitmap = new Bitmap(image, size);
                switch (stringAlignment)
                {
                    case StringAlignment.Near:
                        break;
                    case StringAlignment.Center:
                        x = (_paperWidth - width) / 2;
                        break;
                    case StringAlignment.Far:
                        x = _paperWidth - width;
                        break;
                    default:
                        break;
                }
                var point = new Point(x, Convert.ToInt32(_currentheight));
                
                var rectangle = new Rectangle(point, size);
                g.DrawImage(bitmap, rectangle);
                NewLineOffset = bitmap.Height;
            });
        }

        public void PrintLine(FontSize fontSize = FontSize.Normal)
        {
            int charNum = (int)(_paperWidth / ((int)fontSize * _charProportion));
            var builder = new StringBuilder();
            for (int i = 0; i < charNum; i++)
            {
                builder.Append('-');
            }
            PrintText(builder.ToString(), fontSize, StringAlignment.Center);
        }
        #endregion

        #region methods
        /// <summary>
        /// 对内容进行分行，并返回行数
        /// </summary>
        /// <param name="content">内容</param>
        /// <param name="fontSize">文字大小</param>
        /// <param name="width">内容区宽度</param>
        /// <returns>行数</returns>
        static string ContentWarp(string content, FontSize fontSize, float width, out int row)
        {
            content = content.Replace(Environment.NewLine, string.Empty);

            //0.7282 字符比例
            var builder = new StringBuilder();
            float nowWidth = 0;
            row = 1;
            var wideCharWidth = _charProportion * (int)fontSize * 2;
            foreach (char item in content)
            {
                int code = Convert.ToInt32(item);
                float charWidth = code < 128 ? _charProportion * (int)fontSize : wideCharWidth;
                nowWidth += charWidth;
                if (nowWidth > (width - wideCharWidth))
                {
                    builder.Append(Environment.NewLine);
                    nowWidth = charWidth;
                    row++;
                }
                builder.Append(item);
            }
            return builder.ToString();
        }
        public int ContentRowNum(string content, FontSize fontSize = FontSize.Normal, StringAlignment stringAlignment = StringAlignment.Near, float width = 1, float offset = 0)
        {
            float contentWidth = width == 1 ? _paperWidth * (1 - offset) : width * _paperWidth;
            string newContent = ContentWarp(content, fontSize, contentWidth, out var rowNum);
            return rowNum;
        }
        #endregion
    }

}
