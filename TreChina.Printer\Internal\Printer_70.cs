﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.Text;
using Newtonsoft.Json.Linq;

namespace PrintCore
{
    public class Printer_70 : IPrinter
    {

        #region fields
        PrintDocument _printDoc = new PrintDocument();

        /// <summary>
        /// 打印对象打印宽度(根据英寸换算而来,paperWidth * 3.937)
        /// </summary>
        readonly int _paperWidth;
        readonly int _paperHight;
        const float _charProportion = 0.9f;
        const float _firstHeightProportion = 0.8f;
        const float _lineHeightProportion = 2.5f;
        const string _fontName = "宋体";

        IList<Action<Graphics>> _printActions = new List<Action<Graphics>>();

        /// <summary>
        /// 当前的打印高度，当调用换行或者图片打印时会增加此字段值
        /// </summary>
        float _currentheight = 0;

        float NewLineOffset { get; set; } = (int)FontSize.Normal * _lineHeightProportion;

        #endregion

        #region ctor

        /// <summary>
        /// 初始化机打印对象
        /// </summary>
        /// <param name="PrinterName">打印机名称</param>
        /// <param name="paperWidth">打印纸宽度</param>
        /// <param name="paperHight">打印纸高度</param>
        internal Printer_70(string PrinterName)
        {
            double paperWidth = 72d;
            double paperHight = 40d;
             //3.937为一个打印单位(打印单位:80(实际宽度72.1),58（实际宽度48）)
            _paperWidth = Convert.ToInt32(Math.Ceiling(paperWidth * 3.937));
            _paperHight = Convert.ToInt32(Math.Ceiling(paperHight * 3.937));
            _printDoc.PrinterSettings.PrinterName = PrinterName;
            _printDoc.PrintPage += PrintPageDetails;
            _printDoc.DefaultPageSettings.PaperSize = new PaperSize("", _paperWidth, _paperHight);
            _printDoc.PrintController = new StandardPrintController();
        }

        public void Print(JToken data)
        {

            PrintName(data["store_name"].ToString());
            string name = data["name"].ToString();
            if (name.Length > 20)
            {
                name = name.Substring(0, 20);
            }
            PrintProductName(name.ToString());
            PrintImage(data["code"].ToString());
            PrintPrice(data);
            PrintOther(data);
            Finish();
        }

        private void PrintOther(JToken data)
        {
            String local = data["local"]!=null?data["local"].ToString():"";
            String unit = data["unit_name"] != null ? data["unit_name"].ToString() : "";
            String spec = data["spec"] != null ? data["spec"].ToString() : "";
            String level = data["level"] != null ? data["level"].ToString() : "";
            String specsName = data["specsName"] != null ? data["specsName"].ToString() : string.Empty;
            //产地
            _printActions.Add((g) =>
            {
                g.DrawString(local, new Font(_fontName,
                    (int)FontSize.Normal, FontStyle.Regular),
                    Brushes.Black, new RectangleF(new PointF(20, 70),
                    new SizeF(_paperWidth, (int)FontSize.Normal * _lineHeightProportion)),
                    new StringFormat());
            });
            //单位
            _printActions.Add((g) =>
            {
                g.DrawString(unit, new Font(_fontName,
                    (int)FontSize.Normal, FontStyle.Regular),
                    Brushes.Black, new RectangleF(new PointF(90, 70),
                    new SizeF(_paperWidth, (int)FontSize.Normal * _lineHeightProportion)),
                    new StringFormat());
            });
            //规格
            _printActions.Add((g) =>
            {
                g.DrawString(spec, new Font(_fontName,
                    (int)FontSize.Normal, FontStyle.Regular),
                    Brushes.Black, new RectangleF(new PointF(20, 85),
                    new SizeF(_paperWidth, (int)FontSize.Normal * _lineHeightProportion)),
                    new StringFormat());
            });
            //等级
            _printActions.Add((g) =>
            {
                g.DrawString(level, new Font(_fontName, (int)FontSize.Normal, FontStyle.Regular),
                    Brushes.Black, new RectangleF(new PointF(90, 85),
                    new SizeF(_paperWidth, (int)FontSize.Normal * _lineHeightProportion)),
                    new StringFormat());
            });
            //规格
            _printActions.Add((g) =>
            {
                g.DrawString(specsName, new Font(_fontName, (int)FontSize.Normal, FontStyle.Regular),
                    Brushes.Black, new RectangleF(new PointF(20, 85),
                    new SizeF(_paperWidth, (int)FontSize.Normal * _lineHeightProportion)),
                    new StringFormat());
            });
        }

        private void PrintPrice(JToken data)
        {
            string sale_price = data["sale_price"] != null ? data["sale_price"].ToString() : "";
            string vip_price = data["vip_price"] != null ? data["vip_price"].ToString() : "";
            string sale_name = data["sale_name"] != null ? data["sale_name"].ToString() : "";
            string vip_name = data["vip_name"] != null ? data["vip_name"].ToString() : "";
            var sale_price_fontsize = data.Value<int?>("sale_price_fontsize");
            var vip_price_fontsize = data.Value<int?>("vip_price_fontsize");
            sale_price_fontsize = sale_price_fontsize.HasValue ? sale_price_fontsize.Value : (int)FontSize.Normal;
            vip_price_fontsize = vip_price_fontsize.HasValue ? vip_price_fontsize.Value : (int)FontSize.Huge;
            if (!vip_name.Equals(""))
            {
                _printActions.Add((g) =>
                {
                    g.DrawString(sale_name + "：", new Font(_fontName,
                        (int)FontSize.Small, FontStyle.Regular),
                        Brushes.Black, new RectangleF(new PointF(152, 72),
                        new SizeF(_paperWidth, (int)FontSize.Small * _lineHeightProportion)),
                        new StringFormat());
                });
                _printActions.Add((g) =>
                {
                    g.DrawString(sale_price, new Font(_fontName,
                        sale_price_fontsize.Value, FontStyle.Regular),
                        Brushes.Black, new RectangleF(new PointF(190, 72),
                        new SizeF(_paperWidth, (int)FontSize.Normal * _lineHeightProportion)),
                        new StringFormat());
                });
                _printActions.Add((g) =>
                {
                    g.DrawString(vip_name + "：", new Font(_fontName,
                        (int)FontSize.Small, FontStyle.Regular),
                        Brushes.Black, new RectangleF(new PointF(152, 90),
                        new SizeF(_paperWidth, (int)FontSize.Small * _lineHeightProportion)),
                        new StringFormat());
                });
                _printActions.Add((g) =>
                {
                    g.DrawString(vip_price, new Font(_fontName, vip_price_fontsize.Value, FontStyle.Bold),
                        Brushes.Black, new RectangleF(new PointF(160, 110),
                        new SizeF(_paperWidth, (int)FontSize.Huge * _lineHeightProportion)),
                        new StringFormat());
                });
            }
            else
            {
                _printActions.Add((g) =>
                {
                    g.DrawString(sale_name + "：", new Font(_fontName,
                        (int)FontSize.Normal, FontStyle.Regular),
                        Brushes.Black, new RectangleF(new PointF(152, 90),
                        new SizeF(_paperWidth, (int)FontSize.Normal * _lineHeightProportion)),
                        new StringFormat());
                });
                _printActions.Add((g) =>
                {
                    g.DrawString(sale_price, new Font(_fontName, sale_price_fontsize.Value, FontStyle.Bold),
                        Brushes.Black, new RectangleF(new PointF(160, 110),
                        new SizeF(_paperWidth, (int)FontSize.Huge * _lineHeightProportion)),
                        new StringFormat());
                });
            }


        }

        private void PrintProductName(string data)
        {
            _printActions.Add((g) =>
            {
                _currentheight += NewLineOffset;
                NewLineOffset = (int)FontSize.Normal * 0.5f;
            });
            PrintText(data, fontSize:FontSize.Normal,offset: 0.12f);
        }

        private void PrintName(string data)
        {
            _printActions.Add((g) =>
            {
                _currentheight += 18;
                NewLineOffset = (int)FontSize.Small;
            });
           
            PrintText(data, fontSize: FontSize.Small ,offset: 0.0f);          
        }

        #endregion

        #region eventHandler
        void PrintPageDetails(object sender, PrintPageEventArgs e)
        {
            foreach (var item in _printActions)
            {
                item(e.Graphics);
            }
        }
        #endregion

        #region IPrinterImplement
        public void NewRow(FontSize fontSize = FontSize.Normal)
        {
            _printActions.Add((g) =>
            {
                _currentheight += NewLineOffset;
                NewLineOffset = (int)fontSize * 2.0f;
            });
        }
        public void NewFirstRow()
        {
            _printActions.Add((g) =>
            {
                _currentheight += NewLineOffset;
                NewLineOffset = (int)FontSize.Normal * _firstHeightProportion;
            });
        }

        public void PrintText(string content, FontSize fontSize = FontSize.Normal, StringAlignment stringAlignment = StringAlignment.Near, float width = 1, float offset = 0)
        {
            _printActions.Add((g) =>
            {
                float contentWidth = width == 1 ? _paperWidth * (1 - offset) : width * _paperWidth;
                string newContent = ContentWarp(content, fontSize, contentWidth, out var rowNum);
                var font = new Font(_fontName, (int)fontSize, FontStyle.Regular);
                var point = new PointF(offset * _paperWidth, _currentheight);
                var size = new SizeF(contentWidth, (int)fontSize * _lineHeightProportion * rowNum);
                var layoutRectangle = new RectangleF(point, size);
                var format = new StringFormat
                {
                    Alignment = stringAlignment,
                    FormatFlags = StringFormatFlags.NoWrap
                };
                g.DrawString(newContent, font, Brushes.Black, layoutRectangle, format);
                float thisHeightOffset = rowNum * (int)fontSize * _lineHeightProportion;
                if (thisHeightOffset > NewLineOffset) NewLineOffset = thisHeightOffset;
            });
        }

        public void Finish()
        {
            _printDoc.Print();
            _printDoc.Dispose();
            _printDoc = new PrintDocument();
            _printActions.Clear();
        }

        public void PrintImage(String code)
        {
            Bitmap image = null;
            try
            {
                image = PrintCore.BarcodeHelper.Generate2_Format(code, 148, 36, code.Length == 13 ? ZXing.BarcodeFormat.EAN_13 : ZXing.BarcodeFormat.CODE_128);
            }
            catch
            {
                image = PrintCore.BarcodeHelper.Generate2_Format(code, 148, 36, ZXing.BarcodeFormat.CODE_128);
            }
            int _imageoffsetheight = 103;
            _printActions.Add((g) =>
            {
                int x = 10;

                var point = new Point(x, _imageoffsetheight);
                var size = new Size(image.Width, image.Height);
                var rectangle = new Rectangle(point, size);
                g.DrawImage(image, rectangle);
                NewLineOffset = image.Height;
            });
        }

        public void PrintLine(FontSize fontSize = FontSize.Normal)
        {
            int charNum = (int)(_paperWidth / ((int)fontSize * _charProportion));
            var builder = new StringBuilder();
            for (int i = 0; i < charNum; i++)
            {
                builder.Append('-');
            }
            PrintText(builder.ToString(), fontSize, StringAlignment.Center);
        }
        #endregion

        #region methods
        /// <summary>
        /// 对内容进行分行，并返回行数
        /// </summary>
        /// <param name="content">内容</param>
        /// <param name="fontSize">文字大小</param>
        /// <param name="width">内容区宽度</param>
        /// <returns>行数</returns>
        static string ContentWarp(string content, FontSize fontSize, float width, out int row)
        {
            content = content.Replace(Environment.NewLine, string.Empty);

            //0.7282 字符比例
            var builder = new StringBuilder();
            float nowWidth = 0;
            row = 1;
            foreach (char item in content)
            {
                int code = Convert.ToInt32(item);
                float charWidth = code < 128 ? _charProportion * (int)fontSize : _charProportion * (int)fontSize * 2;
                nowWidth += charWidth;
                if (nowWidth > width)
                {
                    builder.Append(Environment.NewLine);
                    nowWidth = charWidth;
                    row++;
                }
                builder.Append(item);
            }
            return builder.ToString();
        }

        public void PrintImage(Image image, StringAlignment stringAlignment = StringAlignment.Near)
        {
            throw new NotImplementedException();
        }

        #endregion
    }

}
