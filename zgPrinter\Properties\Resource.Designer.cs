﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace zgPrinter.Properties {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("zgPrinter.Properties.Resource", typeof(Resource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 1,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;交接班报表&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;
        ///}, {
        ///	&quot;index&quot;: 2,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 2,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 7pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;开始时间&quot;,
        ///	&quot;datamember&quot;: &quot;开始时间&quot;,
        ///	&quot;format&quot;: &quot;开始时间：{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;
        ///}, {
        ///	&quot;index&quot;: [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string changeShiftsTemplate {
            get {
                return ResourceManager.GetString("changeShiftsTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;转桌单&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 30,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 22pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;流水号&quot;,
        ///	&quot;datamember&quot;: &quot;流水号&quot;,
        ///	&quot;format&quot;: &quot;流水号：{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;word [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string changeTableform {
            get {
                return ResourceManager.GetString("changeTableform", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 1,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;交接班报表&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;
        ///}, {
        ///	&quot;index&quot;: 2,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 2,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 7pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;开始时间&quot;,
        ///	&quot;datamember&quot;: &quot;开始时间&quot;,
        ///	&quot;format&quot;: &quot;开始时间：{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;
        ///}, {
        ///	&quot;index&quot;: [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string CYChangeShiftsTemplate {
            get {
                return ResourceManager.GetString("CYChangeShiftsTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;制作单&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 11,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 10.5pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;partNum&quot;,
        ///	&quot;datamember&quot;: &quot;partNum&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 30,
        ///	&quot;contenttype&quot;: 5,
        ///	 [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string CYProductionNoteTemplate {
            get {
                return ResourceManager.GetString("CYProductionNoteTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 
        ///[{
        ///	&quot;index&quot;: 9,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;退菜单&quot;,
        ///	&quot;datamember&quot;: &quot;name&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///},{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;门店名称&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 5 [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string CYRetreatFoodTemplate {
            get {
                return ResourceManager.GetString("CYRetreatFoodTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 9,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;退菜单80&quot;,
        ///	&quot;datamember&quot;: &quot;name&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///},{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;门店名称&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 5 [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string CYRetreatFoodTemplate80 {
            get {
                return ResourceManager.GetString("CYRetreatFoodTemplate80", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;积分兑换&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 30,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 7pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;交易时间&quot;,
        ///	&quot;datamember&quot;: &quot;交易时间&quot;,
        ///	&quot;format&quot;: &quot;交易时间：{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot; [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string exchangeTemplate {
            get {
                return ResourceManager.GetString("exchangeTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 
        ///[{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;退菜单&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 30,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 10.5pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;桌号&quot;,
        ///	&quot;datamember&quot;: &quot;桌号&quot;,
        ///	&quot;format&quot;: &quot;桌号：{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;di [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string FZRetreatProductTemplate {
            get {
                return ResourceManager.GetString("FZRetreatProductTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 12pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;澎湖山庄家家悦&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 30,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 7pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;挂单时间&quot;,
        ///	&quot;datamember&quot;: &quot;挂单时间&quot;,
        ///	&quot;format&quot;: &quot;挂单时间：{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        /// [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string FZShoppingReceiptTemplate {
            get {
                return ResourceManager.GetString("FZShoppingReceiptTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 1,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;商品销售报表&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 2,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 3,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 7pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;开始时间&quot;,
        ///	&quot;datamember&quot;: &quot;开始时间&quot;,
        ///	&quot;format&quot;: &quot;开始时间：{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string goodsSalesReportTemplate {
            get {
                return ResourceManager.GetString("goodsSalesReportTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;我的小店&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 11,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 30,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 9pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;销售单号&quot;,
        ///	&quot;datamember&quot;: &quot;销售单号&quot;,
        ///	&quot;format&quot;: &quot;销售单号：{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string HBMemberChargeTemplate {
            get {
                return ResourceManager.GetString("HBMemberChargeTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 4,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;datamember&quot;: &quot;logo&quot;,
        ///	&quot;displayname&quot;: &quot;&quot;,
        ///	&quot;imgpath&quot;: &quot;&quot;
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;澎湖山庄家家悦&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 21,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 30,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;:  [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string HBShoppingReceiptTemplate {
            get {
                return ResourceManager.GetString("HBShoppingReceiptTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 4,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;datamember&quot;: &quot;logo&quot;,
        ///	&quot;displayname&quot;: &quot;&quot;,
        ///	&quot;imgpath&quot;: &quot;&quot;
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;澎湖山庄家家悦&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 21,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 30,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;:  [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string HBShoppingReceiptTemplate80 {
            get {
                return ResourceManager.GetString("HBShoppingReceiptTemplate80", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;寄件单&quot;,
        ///	&quot;datamember&quot;: &quot;title&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 21,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 30,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 9pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;收银员&quot;,
        ///	&quot;datamember&quot;: &quot;收银 [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string mailing {
            get {
                return ResourceManager.GetString("mailing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;取件单&quot;,
        ///	&quot;datamember&quot;: &quot;title&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 21,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 30,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 9pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;收银员&quot;,
        ///	&quot;datamember&quot;: &quot;收银 [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string pickup {
            get {
                return ResourceManager.GetString("pickup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 9,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;预结单&quot;,
        ///	&quot;datamember&quot;: &quot;name&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///},{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;我的店铺&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string preSettlement {
            get {
                return ResourceManager.GetString("preSettlement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 9,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;预结单&quot;,
        ///	&quot;datamember&quot;: &quot;name&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///},{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;我的店铺&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string preSettlement80 {
            get {
                return ResourceManager.GetString("preSettlement80", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Byte[] 类型的本地化资源。
        /// </summary>
        internal static byte[] priceTagTemplate {
            get {
                object obj = ResourceManager.GetObject("priceTagTemplate", resourceCulture);
                return ((byte[])(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;制作单&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 30,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 40,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;流水号&quot;,
        ///	&quot;datamember&quot;: &quot;流水号&quot;,
        ///	&quot;format&quot;: &quot;流水号：{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;dis [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string productionNoteTemplate {
            get {
                return ResourceManager.GetString("productionNoteTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 
        ///[{
        ///	&quot;index&quot;: 9,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;退菜单80&quot;,
        ///	&quot;datamember&quot;: &quot;name&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///},{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;退菜单&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;:  [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string retreatFoodTemplate {
            get {
                return ResourceManager.GetString("retreatFoodTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 
        ///[{
        ///	&quot;index&quot;: 9,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;退菜单80&quot;,
        ///	&quot;datamember&quot;: &quot;name&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///},{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;退菜单&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;:  [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string retreatFoodTemplate80 {
            get {
                return ResourceManager.GetString("retreatFoodTemplate80", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 
        ///[{
        ///	&quot;index&quot;: 9,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;退菜单&quot;,
        ///	&quot;datamember&quot;: &quot;name&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///},{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;门店名称&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 5 [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string retreatProductTemplate {
            get {
                return ResourceManager.GetString("retreatProductTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 
        ///[{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;退菜单&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 19,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///},{
        ///	&quot;index&quot;: 30,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 10.5pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;桌号&quot;,
        ///	&quot;datamember&quot;: &quot;桌号&quot;,
        ///	&quot;format&quot;: &quot;桌号：{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;dis [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string retreatProductTemplate80 {
            get {
                return ResourceManager.GetString("retreatProductTemplate80", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 1,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 9pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;我的店铺&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 2,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 3,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 9pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;流水号&quot;,
        ///	&quot;datamember&quot;: &quot;流水号&quot;,
        ///	&quot;format&quot;: &quot;流水号：{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string salesReportTemplate {
            get {
                return ResourceManager.GetString("salesReportTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 1,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 12pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;我的店铺&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 2,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 3,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 9pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;流水号&quot;,
        ///	&quot;datamember&quot;: &quot;流水号&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;:  [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string salesReportTemplate80 {
            get {
                return ResourceManager.GetString("salesReportTemplate80", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;商品销售报表&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 30,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 7pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;交易时间&quot;,
        ///	&quot;datamember&quot;: &quot;交易时间&quot;,
        ///	&quot;format&quot;: &quot;交易时间：{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displaynam [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string SCBuyTimeCardTemplate {
            get {
                return ResourceManager.GetString("SCBuyTimeCardTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 4,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;datamember&quot;: &quot;logo&quot;,
        ///	&quot;displayname&quot;: &quot;&quot;,
        ///	&quot;imgpath&quot;: &quot;D:\\111.png&quot;
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 12pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;澎湖山庄家家悦&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 30,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 40,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot; [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string SCShoppingReceiptTemplate {
            get {
                return ResourceManager.GetString("SCShoppingReceiptTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;次卡消费&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 30,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 7pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;交易时间&quot;,
        ///	&quot;datamember&quot;: &quot;交易时间&quot;,
        ///	&quot;format&quot;: &quot;交易时间：{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot; [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string SCTimeCardUsedTemplate {
            get {
                return ResourceManager.GetString("SCTimeCardUsedTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 1,
        ///	&quot;contenttype&quot;: 4,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;datamember&quot;: &quot;logo&quot;,
        ///	&quot;displayname&quot;: &quot;&quot;,
        ///	&quot;imgpath&quot;: &quot;&quot;
        ///}, {
        ///	&quot;index&quot;: 2,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿宋, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;澎湖山庄家家悦&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 2,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;content&quot;: 1
        ///}, {
        ///	&quot;index&quot;: 3,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 16,
        ///	&quot;contentfont&quot;: &quot;\&quot;仿 [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string shoppingReceiptTemplate {
            get {
                return ResourceManager.GetString("shoppingReceiptTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 1,
        ///	&quot;contenttype&quot;: 4,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;datamember&quot;: &quot;logo&quot;,
        ///	&quot;displayname&quot;: &quot;&quot;,
        ///	&quot;imgpath&quot;: &quot;&quot;
        ///},{
        ///	&quot;index&quot;: 9,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;结账单&quot;,
        ///	&quot;datamember&quot;: &quot;name&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///},{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;我的店铺&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	 [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string statement {
            get {
                return ResourceManager.GetString("statement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 1,
        ///	&quot;contenttype&quot;: 4,
        ///	&quot;textalign&quot;: 0,
        ///	&quot;datamember&quot;: &quot;logo&quot;,
        ///	&quot;displayname&quot;: &quot;&quot;,
        ///	&quot;imgpath&quot;: &quot;&quot;
        ///},{
        ///	&quot;index&quot;: 9,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;结账单&quot;,
        ///	&quot;datamember&quot;: &quot;name&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///},{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;我的店铺&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	 [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string statement80 {
            get {
                return ResourceManager.GetString("statement80", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 9,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;压桌单&quot;,
        ///	&quot;datamember&quot;: &quot;name&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///},{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;压桌单58&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalig [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string tableform {
            get {
                return ResourceManager.GetString("tableform", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 [{
        ///	&quot;index&quot;: 9,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt, style=Bold\&quot;&quot;,
        ///	&quot;content&quot;: &quot;压桌单&quot;,
        ///	&quot;datamember&quot;: &quot;name&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///},{
        ///	&quot;index&quot;: 10,
        ///	&quot;contenttype&quot;: 1,
        ///	&quot;textalign&quot;: 32,
        ///	&quot;contentfont&quot;: &quot;\&quot;宋体, 12pt\&quot;&quot;,
        ///	&quot;content&quot;: &quot;压桌单80&quot;,
        ///	&quot;datamember&quot;: &quot;storename&quot;,
        ///	&quot;format&quot;: &quot;{0}&quot;,
        ///	&quot;defaultcontent&quot;: &quot;&quot;,
        ///	&quot;displayname&quot;: &quot;文本&quot;,
        ///	&quot;wordwarp&quot;: false
        ///}, {
        ///	&quot;index&quot;: 20,
        ///	&quot;contenttype&quot;: 5,
        ///	&quot;textalig [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string tableform80 {
            get {
                return ResourceManager.GetString("tableform80", resourceCulture);
            }
        }
    }
}
