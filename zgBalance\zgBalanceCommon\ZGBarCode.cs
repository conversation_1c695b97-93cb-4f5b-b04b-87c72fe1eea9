﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgBalanceCommon
{
    /// <summary>
    /// 掌柜智囊条码工具
    /// </summary>
    public class ZGBarCode
    {
        /// <summary>
        /// 获取EAN13类型条码的校验位
        /// </summary>
        /// <param name="barCode"></param>
        /// <returns></returns>
        public static string GetCheckCodeEAN13(string barCode) {
            if (barCode.Length != 13) {
                throw new Exception("条码长度不是13位");
            }
            var charArray = barCode.ToCharArray();
            Array.Reverse(charArray);
            double sum = 0;
            for (int i = 1; i < 13; i++)
            {
                sum += i % 2 == 0 ? Convert.ToInt32(charArray[i].ToString()) : Convert.ToInt32(charArray[i].ToString()) * 3;
            }

            var result = Math.Ceiling(sum / 10d) * 10 - sum;


            return result.ToString();
        }
    }
}
