﻿using zgSerialPort.Common;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace zgSerialPort.ACS
{
    /// <summary>
    /// 鸿海工业科技 电子秤
    /// </summary>
    [ACSAttribute(ModelName = "鸿海电子秤",IsRelease = true, Index = 2)]
    public class HongHaiACS:BaseACS
    {

        public override void CommDataReceived(Object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                var serialPort = (System.IO.Ports.SerialPort)sender;
                if (serialPort == null || !serialPort.IsOpen)
                {
                    return;
                }
                //线程停止50毫秒，大概率可以读取一条完整报文
                Thread.Sleep(50);
                //Comm.BytesToRead中为要读入的字节长度
                int len = serialPort.BytesToRead;
                Byte[] readBuffer = new Byte[len];
                serialPort.Read(readBuffer, 0, len); //将数据读入缓存
                var hexStr = HexHelper.ByteToHexStr(readBuffer);
                var hexArray = hexStr.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                //TODO：校验协议有效性
                if (hexArray.Length == 16)
                {
                    if ("53".Equals(hexArray[2]))//53:稳定 55:不稳定
                    {
                        var showValue = new string[6];
                        Array.Copy(hexArray, 4, showValue, 0, 6);
                        var r = HexHelper.GetChsFromHex(string.Join("", showValue)).Replace(".", "");
                        CallbackAction(r);
                    }
                }
            }
            catch (Exception)
            {

            }
        }

        
    }
}
