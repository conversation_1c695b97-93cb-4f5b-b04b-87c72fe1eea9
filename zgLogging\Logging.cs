﻿using System;
using System.IO;
using System.Diagnostics;

namespace zgLogging
{
	public class Log
	{
		public static DateTime LastLogDate { get; set; }

		public static string LogDirectory
		{
			get
			{
				return Path.Combine(System.AppDomain.CurrentDomain.BaseDirectory,Log._logDirectory, BaseDirectory ??"" );
			}
		}

		public static string BaseDirectory { get; set; }

		public static void CreateLogListener(string directory=null)
		{
			//zgLogging.NLogger.Instance.Shutdown();
			BaseDirectory = directory;
			
			zgLogging.NLogger.Instance.Init(Log.LogDirectory, zgLogging.NLogger.LogLevel.Info);

		}

		public static void WriterExceptionLog(string log)
		{
			StackTrace stackTrace = new StackTrace(true);
			Type declaringType = stackTrace.GetFrame(1).GetMethod().DeclaringType;
			int fileLineNumber = stackTrace.GetFrame(1).GetFileLineNumber();
			string name = stackTrace.GetFrame(1).GetMethod().Name;
			log = string.Concat(new object[]
			{
				"类:",
				declaringType.FullName,
				"函数:",
				name,
				"行:",
				fileLineNumber,
				"中出现异常,异常信息如下:",
				log
			});
			Error("异常:" + log);
		}

		public static void WriterNormalLog(string log)
		{
			
			zgLogging.NLogger.Instance.WriteLog(zgLogging.NLogger.LogLevel.Info, log);
			//Log.WriterLog(log);
		}


        public static void Fatal(string msg)
        {
			zgLogging.NLogger.Instance.WriteLog(zgLogging.NLogger.LogLevel.Fatal, msg);

		}

        private static string _logDirectory = "Logs\\";

        public static void CloseLogListener()
        {
			zgLogging.NLogger.Instance.Shutdown();

		}
		/// <summary>
		/// 写入一条调试日志
		/// </summary>
		/// <param name="LogMessage">日志信息</param>
		/// <param name="LogValues">参数值</param>
		public static void Debug(string LogMessage, params object[] LogValues) => Debug(string.Format(LogMessage, LogValues)); 
		public static void Debug(string msg)
        {
			zgLogging.NLogger.Instance.WriteLog(zgLogging.NLogger.LogLevel.Debug, msg);
		}

		/// <summary>
		/// 写入一条信息日志
		/// </summary>
		/// <param name="LogMessage">日志信息</param>
		/// <param name="LogValues">参数值</param>
		public static void Info(string LogMessage, params object[] LogValues) => Info(string.Format(LogMessage, LogValues));
		public static void Info(string msg)
        {
			zgLogging.NLogger.Instance.WriteLog(zgLogging.NLogger.LogLevel.Info, msg);
		}

		/// <summary>
		/// 写入一条错误日志
		/// </summary>
		/// <param name="LogMessage">日志信息</param>
		/// <param name="LogValues">参数值</param>
		public static void Error(string LogMessage, params object[] LogValues) => Error(string.Format(LogMessage, LogValues));
		public static void Error(string msg)
        {
			zgLogging.NLogger.Instance.WriteLog(zgLogging.NLogger.LogLevel.Error, msg);
		}

		/// <summary>
		/// 写入一条警告日志
		/// </summary>
		/// <param name="LogMessage">日志信息</param>
		/// <param name="LogValues">参数值</param>
		public static void Warn(string LogMessage, params object[] LogValues) => Warn(string.Format(LogMessage, LogValues));
		public static void Warn(string msg)
		{
            throw new NotImplementedException();
        }
    }
}
