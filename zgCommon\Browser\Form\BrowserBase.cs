﻿using CefSharp;
using CefSharp.WinForms;
using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using zgLogging;
using zgUtils.Controls;

namespace zgpos
{
    public class BrowserBase
    {

        public Form mainform;
        public ChromiumWebBrowser browser;
        public virtual void BeforeInit()
        {
            
        }
        public virtual void AfterInit()
        {
            
        }
        public BrowserBase(Form form,String address)
        {
            BeforeInit();
            mainform = form;
            Log.Info("BrowserBase(Form form,String address)：" + address);
            try
            {
                // 初始化浏览器
                // BrowserSettings 必须在 Controls.Add 之前
                BrowserSettings browserSettings = new BrowserSettings
                {
                    // FileAccessFromFileUrls 必须 Enabled
                    // 不然 AJAX 请求 file:// 会显示:
                    // Cross origin requests are only supported for protocol schemes: http, data, chrome, chrome-extension, https.
                    FileAccessFromFileUrls = CefState.Enabled,
                    UniversalAccessFromFileUrls = CefState.Enabled,
                    DefaultEncoding = "UTF-8"
                };
                Log.Info("Cefsharp 开启跨域请求支持");
                browserSettings.WebSecurity = CefState.Disabled; // 开启跨域请求支持     
                browser = new ChromiumWebBrowser(address);
                browser.Dock = DockStyle.Fill;
                browser.FocusHandler = null;
                browser.BrowserSettings = browserSettings;
                // 设置是否启动js交互，假如需要原生与js方法互调，则需要设置为true
                browser.JavascriptObjectRepository.Settings.LegacyBindingEnabled = true;
                AfterInit();
            }
            catch (Exception ex) {
                Log.Error("BrowserBase(Form form,String address)：" + ex.Message);
            }
            
        }
        /// <summary>
        /// 浏览器执行JS代码获取返回值
        /// </summary>
        /// <param name="script"></param>
        /// <param name="defaultValue"></param>
        /// <param name="timeout"></param>
        /// <returns></returns>
        /// <example>同步：EvaluateScript("5555 * 19999 + 88888", 0, TimeSpan.FromSeconds(3)).GetAwaiter().GetResult();</example>
        public JavascriptResponse EvaluateScript(string script, TimeSpan timeout)
        {
            JavascriptResponse result = null;
            if (browser!=null && browser.IsBrowserInitialized && !browser.IsDisposed && !browser.Disposing)
            {
                try
                {
                    result = browser.EvaluateScriptAsync(script, timeout).GetAwaiter().GetResult();                    
                }
                catch (Exception e)
                {
                    var msg = e.InnerException?.Message ?? e.Message;
                    Log.WriterNormalLog(msg);
                }
            }
            return result;
        }
        /// <summary>
        /// 浏览器执行JS代码
        /// </summary>
        /// <param name="jsCodeStr"></param>
        public void RunJS(string jsCodeStr)
        {
            mainform.BeginInvoke((MethodInvoker)delegate
            {
                if (browser.IsBrowserInitialized)
                {
                    Log.WriterNormalLog("RunJs:" + jsCodeStr);
                    browser.EvaluateScriptAsync(jsCodeStr);
                    Log.WriterNormalLog("RunJs:END");
                }
            });
        }

        internal void CloseApp()
        {
            try
            {
                //释放浏览器对象
                if (browser != null && !browser.Disposing)
                {
                    if(browser.IsBrowserInitialized)browser.GetBrowser().CloseBrowser(true);
                   
                    //Cef.Shutdown();
                }
            }
            catch (Exception e)
            {
                Log.WriterNormalLog(e.Message);
            }
        }
    }
}