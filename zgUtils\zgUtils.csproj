﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{34410408-BDE1-4700-A317-BD455CA550EB}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>zgUtils</RootNamespace>
    <AssemblyName>zgUtils</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <OutputPath>..\out\Programs\Libs\</OutputPath>
    <Optimize>false</Optimize>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <OutputPath>..\out\Programs\Libs\</OutputPath>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="M2Mqtt.Net, Version=4.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\M2Mqtt.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
      <EmbedInteropTypes>False</EmbedInteropTypes>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.SQLite, Version=1.0.113.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\System.Data.SQLite.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Security" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="TreChina.Printer, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\TreChina.Printer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zgLogging, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\zgLogging.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zgSQLite, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\zgSQLite.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zxing" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Config\CommonApp.cs" />
    <Compile Include="Controls\AliMqttCore.cs" />
    <Compile Include="Controls\ApiHelper.cs" />
    <Compile Include="Controls\SysProp.cs" />
    <Compile Include="Controls\ControlExtensions.cs" />
    <Compile Include="Controls\NetworkCenter.cs" />
    <Compile Include="Extensions\StringExtensions.cs" />
    <Compile Include="Extensions\TimeExtension.cs" />
    <Compile Include="Files\DownLoadResult.cs" />
    <Compile Include="Files\EDownLoadState.cs" />
    <Compile Include="Files\FileDownLoader.cs" />
    <Compile Include="Files\FileManager.cs" />
    <Compile Include="Controls\MqttCore.cs" />
    <Compile Include="Controls\PrimaryScreen.cs" />
    <Compile Include="Extensions\BitmapExtension.cs" />
    <Compile Include="Controls\GlobalConstant.cs" />
    <Compile Include="Controls\IniFile.cs" />
    <Compile Include="Files\XmlManager.cs" />
    <Compile Include="Model\ConfigManager.cs" />
    <Compile Include="Model\IndustryClass.cs" />
    <Compile Include="Model\LoginRequest.cs" />
    <Compile Include="Model\Clerk.cs" />
    <Compile Include="Model\Goods.cs" />
    <Compile Include="Model\LoginResponse.cs" />
    <Compile Include="Model\MqttOptions.cs" />
    <Compile Include="Model\PackageBase.cs" />
    <Compile Include="Model\RequestBase.cs" />
    <Compile Include="Model\ResponseBase.cs" />
    <Compile Include="Model\SettingInfo.cs" />
    <Compile Include="Model\SlotTypes.cs" />
    <Compile Include="Model\StoreInfo.cs" />
    <Compile Include="Model\SyncOptions.cs" />
    <Compile Include="Model\UserInfo.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Controls\Utils.cs" />
    <Compile Include="Security\AESHelper.cs" />
    <Compile Include="Security\FingerPrint.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="EventHandlers\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>