[strings]
; General
100=File download
101=Do you want to cancel the download?
102=%1 (%2 of %3)
103=%1 KB
104=%1 KB of %2 KB (%3%)

; Status information
110=Getting file information...
111=Redirecting to %1
112=Sending request...
113=Resolving %1
114=Connected to %1
115=Receiving...
116=Connecting to %1

; Error messages
120=Error connecting to the internet.\n\n%1
121=Error opening %1.\n\nThe server returned status code %2.
122=Error reading URL.\n\n%1
123=Error writing file %1.\n\n%2
124=Error opening file %1.\n\n%2
125='%1' is an invalid URL.
126=Error opening %1.\n\n%2
127=Error sending request.\n\n%1
128=Unsupported protocol. Only HTTP and FTP protocols are supported.
129=Failed to connect to %1.\n\n%2
130=Failed to query status code.\n\n%1
131=Error requesting file.\n\n%1

; Other
144=About...
146=Download
147=Setup is now downloading additional files to your computer.

; labels
160=File:
161=Speed:
162=Status:
163=Elapsed Time:
164=Remaining Time:
165=Current File:
166=Overall Progress:
167=Cancel
168=OK
169=User Name and Password
170=User Name:
171=Password:
