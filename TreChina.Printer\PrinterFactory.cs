﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PrintCore
{
    public static class PrinterFactory
    {
        public static Printer GetPrinter(string printerName, double paperWidth, double? pagerHeight = null)
        {
            if (string.IsNullOrEmpty(printerName)) throw new ArgumentException(nameof(printerName));
            return new Printer(printerName, paperWidth, pagerHeight ?? 10000);
        }
        public static Printer_70 GetPrinter_70(string printerName)
        {
            if (string.IsNullOrEmpty(printerName)) throw new ArgumentException(nameof(printerName));
            return new Printer_70(printerName);
        }
        public static Printer GetPrinter(string printerName, PaperWidth paperWidth, double? pagerHeight = null)
        {
            switch (paperWidth)
            {
                case PaperWidth.Paper80mm:
                    //80打印纸扣去两边内距实际可打的宽度为72.1
                    return GetPrinter(printerName, 72.1, pagerHeight);
                case PaperWidth.Paper76mm:
                    //76打印纸扣去两边内距实际可打的宽度为63.5
                    return GetPrinter(printerName, 63.5, pagerHeight);
                case PaperWidth.Paper58mm:
                    //58打印纸扣去两边内距实际可打的宽度为48
                    return GetPrinter(printerName, 48, pagerHeight);
                default:
                    throw new ArgumentException(nameof(paperWidth));
            }

        }
        public static void PrintLogo(string printerName,int cols , string filename, string logoPath) {

            if (string.IsNullOrEmpty(filename)) return;
            string fullFilename = System.IO.Path.Combine(logoPath, "logo.png");
            PaperWidth paperWidth = (cols == 48 ? PaperWidth.Paper80mm : PaperWidth.Paper58mm);
            var printer = PrinterFactory.GetPrinter(printerName, paperWidth);
            printer.PrintImage(new Bitmap(fullFilename), System.Drawing.StringAlignment.Center);
            printer.Finish();
        }
        public static void PrintQrcode(string printerName, int cols, string filename, string logoPath)
        {

            if (string.IsNullOrEmpty(filename)) return;
            string fullFilename = System.IO.Path.Combine(logoPath, "qrcode.png");
            PaperWidth paperWidth = (cols == 48 ? PaperWidth.Paper80mm : PaperWidth.Paper58mm);
            var printer = PrinterFactory.GetPrinter(printerName, paperWidth);
            printer.PrintQrcode(new Bitmap(fullFilename), System.Drawing.StringAlignment.Center);
            printer.NewRow();
            printer.NewRow();
            printer.NewRow();
            printer.NewRow();
            printer.NewRow();
            printer.NewRow();
            printer.PrintText("");
            printer.Finish();
        }

        public static void PrintPOS(ESCPOS.Printer.PrintCommonClass printCommonClass, string logoPath)
        {
            int cols = int.Parse(printCommonClass.cols);
            PaperWidth paperWidth = (cols == 48 ? PaperWidth.Paper80mm : PaperWidth.Paper58mm);
            FontSize mysize = FontSize.micro;
            String size = printCommonClass.size;
            switch (size)
            {
                case "3":
                    mysize = FontSize.SubMicro;
                    break;
                case "2":
                    mysize = FontSize.micro;
                    break;
                case "1":
                    mysize = FontSize.SubSmall;
                    break;
                default:
                    mysize = FontSize.micro;
                    break;
            }
            
            var printer = PrinterFactory.GetPrinter(printCommonClass.printername, paperWidth);
            printer.setLineHeight(printCommonClass.lineheight);
            //printer.setSetting(1.3f);
            if (!string.IsNullOrEmpty(printCommonClass.logo)) {
                printer.PrintImage(new Bitmap(System.IO.Path.Combine(logoPath, "logo.png")), System.Drawing.StringAlignment.Center);
                printer.NewRow();
                printer.NewRow();

            }
            //////////////////////////////////////////////////////////////
            ///
            printer.PrintText(printCommonClass.storename, fontSize: FontSize.Large, StringAlignment.Center);
            printer.NewRow(mysize);
            printer.NewRow(mysize);
            int cnt = 0;
            
            foreach (Dictionary<string, object> dataSet in printCommonClass.groups)
            {
                if (cnt++ > 0)
                {
                    printer.PrintLine();
                    printer.NewRow(mysize);
                }
                Dictionary<string, object> style = null;
                if (dataSet.ContainsKey("style"))
                {
                    style = (Dictionary<string, object>)dataSet["style"];
                }
                foreach (KeyValuePair<string, object> item in dataSet)  //查找某个字段与值
                {
                    
                    if (item.Key == "style") continue;
                    String text = (item.Key.StartsWith("l_") ? "" : (item.Key + "：")) + Convert.ToString(item.Value);
                    
                    if (style != null && style.ContainsKey("size"))
                    {                       
                        string title = "：";
                        string value = "";
                        if (text.Split('：').Length > 0) title = text.Split('：')[0] + title;
                        if (text.Split('：').Length > 1) value = text.Split('：')[1];
                            
                        printer.PrintText(value, fontSize: FontSize.SubNormal, offset: printer.PrintTitle(title, mysize));
                        
                    }
                    else
                    {
                        printer.PrintText(text, mysize);
                    }
                    printer.NewRow();

                }

            }
            
            //////////////////////////////////////////////////////////////
            if (!string.IsNullOrEmpty(printCommonClass.qrcode)) {
                printer.PrintQrcode(new Bitmap(System.IO.Path.Combine(logoPath, "qrcode.png")), System.Drawing.StringAlignment.Center);
                
            }
            printer.NewRow();
            printer.NewRow();
            printer.NewRow();
            printer.NewRow();
            printer.NewRow();
            printer.NewRow();
            printer.NewRow();
            printer.NewRow();
            printer.PrintText("");
            printer.Finish();
        }
    }
}
