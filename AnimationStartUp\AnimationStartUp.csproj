﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{3443552E-A068-4B7D-9398-970C628485FC}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>AnimationStartUp</RootNamespace>
    <AssemblyName>AnimationStartUp</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\out\StartUps\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\Release\StartUps\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CommonAnimationForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CommonAnimationForm.Designer.cs">
      <DependentUpon>CommonAnimationForm.cs</DependentUpon>
    </Compile>
    <Compile Include="SerpentineStartUpClass.cs" />
    <Compile Include="PeriodicLatticeStartUpClass.cs" />
    <Compile Include="MagicSquareStartUpClass.cs" />
    <Compile Include="LiquidStartUpClass.cs" />
    <Compile Include="GirlStartUpClass.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="StartUpResource.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>StartUpResource.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="CommonAnimationForm.resx">
      <DependentUpon>CommonAnimationForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="StartUpResource.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>StartUpResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Girl.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\MagicSquare.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\PeriodicLattice.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\GirlPreview.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Liquid.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\LiquidPreview.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\MagicSquarePreview.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\PeriodicLatticePreview.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Serpentine.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\SerpentinePreview.jpg" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Templates\StartUpTemplate\StartUpTemplate.csproj">
      <Project>{c618e603-dd32-4278-bebf-6b1807e3a790}</Project>
      <Name>StartUpTemplate</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>