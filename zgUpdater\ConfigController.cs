﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace zgUpdater
{
    public static class ConfigController
    {
        /// <summary>
        /// 程序插件目录
        /// </summary>
        public static readonly string ProgramDirectory = PathCombine(Environment.CurrentDirectory, "Programs");
        private static string _accesstoken;
        public static string uuid { get; set; }
        public static string PostUrl = "";
        public static string Authorization { get => _accesstoken?.Length > 10 ? (!string.IsNullOrEmpty(_accesstoken) && _accesstoken.StartsWith("Bearer ") ? _accesstoken : "Bearer " + _accesstoken) : ""; set => _accesstoken = string.IsNullOrEmpty(value) ? "" : value; }

        internal static string GetLogDir()
        {
            var dir = Environment.CurrentDirectory + "\\Logs" + GetUserLogDir();
            return dir;
        }

        private static string GetUserLogDir()
        {
            var dir = "";
            if (userinfo == null)
            {
                SetUserInfo();
            }
            if (userinfo != null && !string.IsNullOrEmpty(ConfigController.ProgramFile)) {
                dir = dir + "\\" + ConfigController.ProgramFile.ToLower().Replace(".dll","") + "-" + userinfo.sysUid + "-" + userinfo.sysSid;

            }
            return dir;
        }

        internal static void init(Dictionary<string, object> dics)
        {
            
            try
            {
                ProgramFile = GetIndustryName();
                ProgramFilePath = Path.Combine(ProgramDirectory, ProgramFile);
                if (dics.ContainsKey("Authorization"))
                {
                    Authorization = dics["Authorization"].ToString();
                }
                Authorization = GetIndustryToken();
                var LogPath = GetLogDir();
                NLogger.Instance.Init(LogPath);
               
                if (!string.IsNullOrEmpty(LogPath))
                {
                    if (dics.ContainsKey("LogPath"))
                    {
                        dics["LogPath"] = LogPath;
                    }
                    else
                    {
                        dics.Add("LogPath", LogPath);
                    }


                }
                if (dics.ContainsKey("UpdateURL"))
                {
                    UpdateURL = dics["UpdateURL"].ToString();
                }
                else
                {

                    var uUrl = dics.ContainsKey("ProgramFileUrl") ? dics["ProgramFileUrl"].ToString() : "";
                    uUrl = uUrl.Substring(0, uUrl.LastIndexOf('/'));
                    uUrl = uUrl.Substring(0, uUrl.LastIndexOf('/') + 1);
                    UpdateURL = uUrl;
                }
                DefaultConfigURL = AESHelper.StringDecoding(dics["DefaultConfigURL"].ToString());
            }
            catch (Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Info, string.Format("创建工作目录遇到异常：{0}", ex.Message));
            }

        }

        public static string IndustryVer { get; set; }
        public static string DefaultConfigURL { get; set; }
        public static string ProgramFile { get; set;}
        public static string ProgramFilePath { get; set;}
        public static string UpdateURL { get; set;}
        public static bool IndustryUpdated { get; set; } = false;
        public static UserInfo userinfo { get; private set; }
        public static string IndustryUpdatedVerInfo { get;  set; }

        /// <summary>
        /// 安全的合并路径
        /// </summary>
        /// <param name="DirectoryPath"></param>
        /// <param name="FileName"></param>
        /// <returns></returns>
        public static string PathCombine(string DirectoryPath, string FileName)
        {
            if (!DirectoryPath.EndsWith("\\")) DirectoryPath += "\\";
            return (DirectoryPath + FileName).Replace("\\\\", "\\");
        }
        /// <summary>
        /// 读取配置
        /// </summary>
        /// <param name="Key">配置Key</param>
        /// <returns>配置信息</returns>
        public static string GetConfig(string Key)
        {
            //zgLogging.Log.Debug("获取配置项Key: {0}", Key);
            string ConfigValue = string.Empty;
            try
            {
                Configuration UnityConfig = ConfigurationManager.OpenExeConfiguration(Application.ExecutablePath);
                KeyValueConfigurationElement ConfigurationElement = UnityConfig.AppSettings.Settings[Key];
                ConfigValue = ConfigurationElement?.Value ?? string.Empty;
                //zgLogging.Log.Debug(">>> ConfigValue[\"{0}\"] = {1}", Key, ConfigValue.Length<=1024?ConfigValue: "0x" + ConfigValue.GetHashCode().ToString("X"));
                return ConfigValue;
            }
            catch (Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error,string.Format("读取配置失败 : Key = {0}, Message:{1}",Key, ex.Message));
                return string.Empty;
            }
        }
        private static string GetConfig(string Key, string ConfigFilePath)
        {
            string ConfigValue = string.Empty;
            try
            {
                if (!File.Exists(ConfigFilePath)) return ConfigValue;
                Configuration UnityConfig = ConfigurationManager.OpenExeConfiguration(ConfigFilePath);
                KeyValueConfigurationElement ConfigurationElement = UnityConfig.AppSettings.Settings[Key];
                ConfigValue = ConfigurationElement?.Value ?? string.Empty;
                return ConfigValue;
            }
            catch (Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, string.Format("读取配置失败 : Key = {0}, Message:{1}", Key, ex.Message));
                return ConfigValue;
            }
        }
        public static string GetIndustryName()
        {
            string IniPath = Path.Combine(ProgramDirectory, "config");
            string IndustryName = "";
            try
            {
                if (File.Exists(IniPath))
                {
                    IniFile iniFile = new IniFile(IniPath);
                    IndustryName = iniFile.Read("industryname", "zgUtils");
                }
                else
                {
                    IndustryName = GetConfig("ProgramFile");
                }
            }
            catch (Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, ex.Message);
            }

            return IndustryName;
        }
        public static string GetIndustryToken()
        {
            string IniPath = Path.Combine(ProgramDirectory, "config");
            string token = "";
            try
            {
                if (File.Exists(IniPath))
                {
                    IniFile iniFile = new IniFile(IniPath);
                    token = iniFile.Read("token", "zgUtils");
                }
                else
                {
                    token = GetConfig("Authorization");
                }
            }
            catch (Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, ex.Message);
            }
            return token.Equals("")? Authorization:token;
        }
        //判断网络文件是否存在
        public static bool HttpFileExist(string fileUrl)
        {
            try
            {
                //创建根据网络地址的请求对象
                System.Net.HttpWebRequest httpWebRequest = (System.Net.HttpWebRequest)System.Net.WebRequest.CreateDefault(new Uri(fileUrl));
                httpWebRequest.Method = "HEAD";
                httpWebRequest.Timeout = 10000;
                //返回响应状态是否是成功比较的布尔值
                using (System.Net.HttpWebResponse response = (System.Net.HttpWebResponse)httpWebRequest.GetResponse())
                {
                    return response.StatusCode == System.Net.HttpStatusCode.OK;
                }
            }
            catch (Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, string.Format("HttpFileExist :Message:{0}",  ex.Message));
                return false;
            }
        }
        public static string GetUpdateVer(string serverUrl)
        {
            try
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Info, string.Format("GetUpdateVer serverUrl:{0}", serverUrl));
                string _Authorization = GetAuthorization();                
                if (String.IsNullOrEmpty(_Authorization)) return "";
                WebClient wc = new WebClient();
                wc.Encoding = Encoding.UTF8;
                wc.Headers.Add("Content-Type", "application/json");
                wc.Headers.Add("Authorization", _Authorization);
                string @string = wc.UploadString(serverUrl, "{}");

                //string sysuid = GetSysuid();
                ResponseArray result = JsonConvert.DeserializeObject<ResponseArray>(@string);
                if (result?.Code == 200 && result.data?.Count > 0)
                {
                    string version = "";
                    foreach (Dictionary<string, object> row in result.data)
                    {
                        version = row["osVersion"].ToString();
                        
                    }
                    return version;
                }
            }
            catch (Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, string.Format("GetUpdateVer:{0}-{1}", serverUrl, ex.Message));
            }
            return "";
        }

        private static string GetAuthorization()
        {
            string _token = Authorization;
            if (!String.IsNullOrEmpty(userinfo?.Accesstoken))
            {
                _token = userinfo.Accesstoken;
            }
            //NLogger.Instance.WriteLog(NLog.LogLevel.Info, string.Format("GetAuthorization:" + Authorization));
            return _token;
            //if (!string.IsNullOrEmpty(currentUserConfig.CurrentUser.Accesstoken)) CommonApp.Authorization = currentUserConfig.CurrentUser.Accesstoken;
        }
        private static void SetUserInfo()
        {
            try
            {
                var data = GetConfig("CurrentUserConfig", ProgramFilePath);
                userinfo = JsonConvert.DeserializeObject<UserInfo>(AESHelper.StringDecoding(data));
            }
            catch{
                
            }
        }
        /// <summary>
        /// 登录用户账号取得
        /// </summary>
        public static string GetSysUid()
        {
            string sysUid = "";
            try
            {
                if(userinfo == null)
                {
                    SetUserInfo();
                }       
                if(userinfo != null)
                {
                    sysUid = userinfo.sysUid;
                }
            }
            catch
            {

            }
            return sysUid;
        }

        public static void ActionLogPost()
        {
            if (!IndustryUpdated)
            {
                return;
            }
            try
            {
                string deviceId = GetDeviceId();
                ActionLogClass actionLogClass = new ActionLogClass();
                actionLogClass.page = "upgrade";
                actionLogClass.action = "upgrade";
                actionLogClass.description = IndustryUpdatedVerInfo;                
                string param = JsonConvert.SerializeObject(new List<ActionLogClass> { actionLogClass });
                var PostUrl = string.Format("{0}/zgzn-logs/logs/action?device={1}&ver={2}", ConfigController.DefaultConfigURL.Substring(0, ConfigController.DefaultConfigURL.LastIndexOf ("/")),deviceId, IndustryVer);
                string _Authorization = GetAuthorization();
                if (String.IsNullOrEmpty(_Authorization)) return ;
                WebClient wc = new WebClient();
                wc.Encoding = Encoding.UTF8;
                wc.Headers.Add("Content-Type", "application/json");
                wc.Headers.Add("Authorization", _Authorization);
                string @string = wc.UploadString(PostUrl, param);
                ResponseArray result = JsonConvert.DeserializeObject<ResponseArray>(@string);
                if (result?.Code == 200)
                {
                    NLogger.Instance.WriteLog(NLog.LogLevel.Info,string.Format("{0} response:", PostUrl, JsonConvert.SerializeObject(result)));
                    
                }
            }
            catch (Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error,ex.Message);
            }
            
        }

        private static string GetDeviceId()
        {
            string deviceCode = "1";
            string userdir = GetUserLogDir()?.Replace("\\","");
            string configFileUrl = Path.Combine(Environment.CurrentDirectory, "Programs", userdir, "config.ini");
            try
            {
                if (File.Exists(configFileUrl))
                {
                    string config = File.ReadAllText(configFileUrl);
                    if (!string.IsNullOrEmpty(config))
                    {
                        config = AESHelper.StringDecoding(config);
                        JToken token = JToken.FromObject(JsonConvert.DeserializeObject(config));
                        deviceCode = token["device"]["deviceCode"].ToString();
                    }

                }
            }
            catch { 
            }
            
            return deviceCode;
        }

        internal static string GetNonUpdateUser()
        {
            string sysUid = ConfigController.GetSysUid();
            try
            {
                string deviceId = GetDeviceId();
                var PostUrl = string.Format("{0}/user/getNonUpdateUser?device={1}&ver={2}", ConfigController.DefaultConfigURL, deviceId, IndustryVer);
                string _Authorization = GetAuthorization();
                if (String.IsNullOrEmpty(_Authorization)||string.IsNullOrEmpty(sysUid)) return "";
                WebClient wc = new WebClient();
                wc.Encoding = Encoding.UTF8;
                wc.Headers.Add("Content-Type", "application/json");
                wc.Headers.Add("Authorization", _Authorization);
                string @string = wc.UploadString(PostUrl,"");
                ResponseArray result = JsonConvert.DeserializeObject<ResponseArray>(@string);

                if (result?.Code == 200 && result.data?.Count > 0)
                {

                    NLogger.Instance.WriteLog(NLog.LogLevel.Info, string.Format("{0} response:{1}", PostUrl, JsonConvert.SerializeObject(result)));

                }
                else {
                    NLogger.Instance.WriteLog(NLog.LogLevel.Info, string.Format("{0} response:{1}", PostUrl, JsonConvert.SerializeObject(result)));
                    sysUid = "";
                }
            }
            catch (Exception ex)
            {
                sysUid = "";
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, ex.Message);
            }
            return sysUid;
        }
    }
    public class IniFile
    {
        string Path;
        string EXE = Assembly.GetExecutingAssembly().GetName().Name;

        [DllImport("kernel32", CharSet = CharSet.Unicode)]
        static extern long WritePrivateProfileString(string Section, string Key, string Value, string FilePath);

        [DllImport("kernel32", CharSet = CharSet.Unicode)]
        static extern int GetPrivateProfileString(string Section, string Key, string Default, StringBuilder RetVal, int Size, string FilePath);

        public IniFile(string IniPath = null)
        {
            Path = new FileInfo(IniPath ?? EXE + ".ini").FullName.ToString();
        }

        public string Read(string Key, string Section = null)
        {
            var RetVal = new StringBuilder(2048);
            GetPrivateProfileString(Section ?? EXE, Key, "", RetVal, 2048, Path);
            return RetVal.ToString();
        }

        public void Write(string Key, string Value, string Section = null)
        {
            WritePrivateProfileString(Section ?? EXE, Key, Value, Path);
        }

        public void DeleteKey(string Key, string Section = null)
        {
            Write(Key, null, Section ?? EXE);
        }

        public void DeleteSection(string Section = null)
        {
            Write(null, null, Section ?? EXE);
        }

        public bool KeyExists(string Key, string Section = null)
        {
            return Read(Key, Section).Length > 0;
        }
    }
    public class ActionLogClass
    {
        public long uid { get { return ConfigController.userinfo?.uid ?? 1; } }
        public string page { get; set; }
        public string action { get; set; }
        public string deviceType { get; set; } = "pc";
        public string description { get; set; }
        public string info { get; set; }
        public string token { get { return ConfigController.Authorization; } }
        public string uuid
        {
            get
            {
                return EncryptWithMD5(this.token); ;
            }
        }
        public string time { get; } = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss FFFF");
        public int maxUploadRows { get; set; } = 10;
        private string EncryptWithMD5(string source)
        {
            byte[] sor = Encoding.UTF8.GetBytes(source);
            MD5 md5 = MD5.Create();
            byte[] result = md5.ComputeHash(sor);
            StringBuilder strbul = new StringBuilder(40);
            for (int i = 0; i < result.Length; i++)
            {
                strbul.Append(result[i].ToString("x2"));//加密结果"x2"结果为32位,"x3"结果为48位,"x4"结果为64位

            }
            return strbul.ToString();
        }

    }
    public class ResponseBase
    {
        [JsonProperty("code")]
        public int Code { get; set; }

        [JsonProperty("msg")]
        public string Message { get; set; }
        public string Status { get; set; }
        public string ToJson(object targert)
        {
            return JsonConvert.SerializeObject(targert);
        }

        public string ToJson()
        {
            return JsonConvert.SerializeObject(this);
        }
    }
    public class ResponseBase<TReponse> : ResponseBase where TReponse : new()
    {
        [JsonProperty("data")]
        public TReponse data { get; set; }

        public ResponseBase()
        {
            this.data = Activator.CreateInstance<TReponse>();
        }
    }
    public class ResponseArray : ResponseBase<List<Dictionary<string, object>>> { }
    public class UserInfo
    {
        private string _accesstoken = "";
        public string sysUid { get; set; }
        public string sysSid { get; set; }
        [JsonProperty("token")]
        public string Accesstoken { get => _accesstoken?.Length > 10 ? (!string.IsNullOrEmpty(_accesstoken) && _accesstoken.StartsWith("Bearer ") ? _accesstoken : "Bearer " + _accesstoken) : ""; set => _accesstoken = string.IsNullOrEmpty(value) ? "" : value; }
        public string name { get; set; }
        //public string Pass { get; set; }
        public long uid { get; set; }
        public string employeenumber { get; set; }//          remark: '工号'
        public string privilege { get; set; }
        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }
    }
}
