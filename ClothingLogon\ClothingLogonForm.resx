﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAABVYAAAMACAYAAADPPjzCAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAADFaSURBVHhe7d3rUhtZ1q7Rvv+rdIcjiKbUnwDrLG21NlNkykmSAk2QlKcxIp4o/3CVbYy6rdeL
        lf86HA5LSZIk3b7df/4jqWc1vZalaP70cJj9999LSdJ4+9f/9vvX/08AAOCW4s9c+9XqsPvPfyT1pHjN
        er9Ek/1+d1jOJoc///23JGmkHYfV1/9PiL9tAwDgxo7j6p8/jQOOpG4Vr1WjKufE58ZqPn17cz359eHN
        tiRp+BlWAQDubL/bHP7f//1f45AjqRvFazReq3BODKub5fPbm2vDqiSNMsMqAEALjKtSdzOqcokYVrfr
        2WE2/W1YlaSRZlgFAGjJ8b7Vf/5pHHYktdTrazJem3AJw6okjTvDKgBAizzMSupWRlUyttu1YVWSRpxh
        FQCgRR5mJXUnD6siK66MMKxK0ngzrAIAtMy4KrWfUZXv2O93h/nTg2FVkkaaYRUAoAM8zEpqLw+r4rtO
        w2rDm21J0vAzrAIAdIRxVbp/RlV+Ik45L14eG99sS5KGn2EVAKBDjKvS/TKq8lMxrK7m07c32K4DkKTR
        ZVgFAOgY46p0+4yqXEMMq5vlswdYSdJIM6wCAHTQfrU67P75p3EQkvTDXl9b8RqDa9htFoZVSRpphlUA
        gI4yrko3yKjKlcXJZ8OqJI0zwyoAQIcZV6UrZlTlBvb73WH+9GBYlaQRZlgFAOg446p0hYyq3Ejcs7p4
        eWx8wy1JGnaGVQCAHjCuSj/IqMoNxbC6mk/f3mQ7tSpJo8qwCgDQEzEMxZPMG4cjSY0dn/5vVOWGYljd
        rmfuWZWkEWZYBQDokXhIinFVuqzjqPr6moFb227XhlVJGmGGVQCAnjGuSl9nVOWe4nPNA6wkaXwZVgEA
        eia+7NS4Kp3PqMq9eYCVJI0zwyoAQE8ZV6WPGVVpgwdYSdI4M6wCAPTY8fTqnz+NA5M0tuK1EK8JaMNu
        s3DPqiSNLMMqAEDPncbVf/5pHJukwff6uW9UpW1xUtqwKknjyrAKADAAx3F1tTKuanzFqPr6uW9UpW37
        /c49q5I0sgyrAAADEgOTe1c1lo73qb5+zkMXxLjvnlVJGleGVQCAgfFQK40hD6mii9yzKknjyrAKADBA
        7l3VYHOfKh0WY//86cGwKkkjybAKADBQ7l3V4HKfKh0Xn5vuWZWk8WRYBQAYOFcDaAj50n/6IIbVzfL5
        7Q23U6uSNPgMqwAAIxCDlKsB1MuKL/03qtIX2+3aPauSNJIMqwAAI1FeDeD0qvrS8ZSqL/2nZ/b7nXtW
        JWkkGVYBAEbmdHq1YciSupJTqvRV/EXAaj59e9NtXJWkQWdYBQAYoePpVVcDqIsVX/rvlCp9tl3PXAcg
        SSPIsAoAMFLHcdXpVXWo8pSqUZW+i89j1wFI0vAzrAIAjNxxYHX3qlrMXaoMTXwuL2eTxjfhkqThZFgF
        AODI6VW1UXlKFYbGdQCSNPwMqwAAnDi9qnvllCpD5zoASRp+hlUAAD44nV71cCtdu+LhVE6pMnSuA5Ck
        4WdYBQCg0fH0qoFV16oyqDqlyljsNgvXAUjSgDOsAgDwqXJgdT2Avtvxy/4NqozQfr9zHYAkDTjDKgAA
        FzkOrO5fVSL3qDJ28bm/mk/f3oAbVyVpcBlWAQBIMbDqqwyq8Nd2u3YdgCQNNMMqAADfYmBVPYMqfBSv
        h8XLY+MbcklSvzOsAgDwIwZWGVThvHhdbNczp1YlaYAZVgEAuIp3A+s//zQOcBpQr7/HBlW4TDy8zUOs
        JGl4GVYBALiqcmDd//ljYB1ir7+n8XtrUIXLxWvFQ6wkaXgZVgEAuInjwLrbHEc41wT0v+Pp1BhUX39P
        DaqQ5yFWkjS8DKsAANxUjHDHkdUp1v5VO51qUIXvi9ePh1hJ0rAyrAIAcDfHgdUp1s7ndCrcRjzE6vhm
        3KlVSRpEhlUAAFpxHFmLU6xG1vY7januToWb8RArSRpWhlUAAFoVI56RtZ3qY6pBFW4rXmOb5fPbG3Lj
        qiT1PsMqAACd0TiyupP1er1+LI2p0C6nViVpOBlWAQDopNPIGneyrlZG1u9WeQBVeWeqMRXaE6+/1Xz6
        9qbcuCpJvc6wCgBAL5yGVqdZP8+pVOi87XZ9mE1/G1YlqecZVgEA6KX60DrKsbU6ohpSoTfiNbqcTRrf
        pEuS+pNhFQCAQTgNrcXVAR/G1j4PrsXPvz6i+tJ+6K84tequVUnqd4ZVAAAGqxwdmwbXzo2uTeNpVBtQ
        I6D/4rV8umtVktTLDKsAAIxOdaSMTqNrWXXYfC3GznqnQbZW0/cte/ffrf54UW08jYBhc9eqJPU7wyoA
        ADSoj5zXDiD+t8Bdq5LU3wyrAAAA0BJ3rUpSfzOsAgAAQEvi1OrprlXjqiT1KsMqAAAAtMipVUnqZ4ZV
        AAAAaJFTq5LUzwyrAAAA0LL9buPUqiT1LMMqAAAAtCxOrW6Wz29v1o2rktSLDKsAAADQAfv97rB4eTSs
        SlJPMqwCAABAR+w2i8Ns+tu4Kkk9yLAKAAAAHRFXAixnk8Y38JKkbmVYBQAAgA7ZbtceZCVJPciwCgAA
        AB0Sp1ZX8+nbG3fjqiR1NsMqAAAAdMx+t3FqVZI6nmEVAAAAOiZOrW7XMw+ykqQOZ1gFAACADopxdfHy
        2PhmXpLUfoZVAAAA6CgPspKk7mZYBQAAgI7yICtJ6m6GVQAAAOiweJDV8UoAw6okdSrDKgAAAHTcbrPw
        ICtJ6liGVQAAAOg4VwJIUvcyrAIAAEAPuBJAkrqVYRUAAAB6IE6tbtczVwJIUkcyrAIAAEBPuBJAkrqT
        YRUAAAB65HQlQMObfEnS/TKsAgAAQM/sNovD/OnBqVVJajHDKgAAAPRMXAmwWT6/vbk3rkpSKxlWAQAA
        oIf2+93blQAxrBpXJenuGVYBAACgp7bbtSsBJKmlDKsAAADQU3ElwHY9O8ymv42rknTnDKsAAADQYzGu
        rubTtzf6xlVJuluGVQAAAOi5/W7jvlVJunOGVQAAABgA961K0n0zrAIAAMAAuG9Vku6bYRUAAAAGwn2r
        knS/DKsAAAAwIKf7VhtGAEnS9TKsAgAAwMDEuOq+VUm6bYZVAAAAGJi4EmC3WbhvVZJumGEVAAAABsjD
        rCTpthlWAQAAYKA8zEqSbpdhFQAAAAYs7ltdziaNo4Ak6fsZVgEAAGDgYlxdvDy+jQFOrkrSVTKsAgAA
        wAjEw6zmTw+GVUm6UoZVAAAAGIG4bzXGVQ+zkqTrZFgFAACAkYhxdbueGVcl6QoZVgEAAGBEYlxdzadv
        w4BxVZK+nWEVAAAARma/3x2Ws8nbOGBclaRvZVgFAACAEdrvNn/HVUlSOsMqAAAAjFSMq4uXx8bBQJL0
        eYZVAAAAGDHjqiR9L8MqAAAAjNx2uz7Mnx4ahwNJUnOGVQAAABi5/+33f8fVeJiVB1pJ0pcZVgEAAIDj
        uLrbLIyrknRhhlUAAADg6HhydT0zrkrSBRlWAQAAgBPjqiRdlmEVAAAAeKccV2fT38ZVSTqTYRUAAAD4
        4MO42jAqSNKYM6wCAAAAjZxclaTzGVYBAACAs4yrktScYRUAAAD4lHFVkj5mWAUAAAC+ZFyVpPcZVgEA
        AICLlOPq/OnBuCpp9BlWAQAAgIsZVyXpLcMqAAAAkBLj6m6z+DuuNgwOkjT0DKsAAABAmnFV0tgzrAIA
        AADfcrwWYLt+G1djaDCwShpRhlUAAADg22Jc3e82h8XL49vYYFyVNJIMqwAAAMCPxbi6nE0axwdJGmKG
        VQAAAOAq3o2rTq5KGniGVQAAAOBq9vvdYTWfHmbT32/jqoFV0kAzrAIAAABXFfeubpbPf8fVhkFCkvqe
        YRUAAAC4uhhXt+uZk6uSBpthFQAAALiJclydPz0YViUNLsMqAAAAcDMxru42i8Pi5fFtjDCwShpIhlUA
        AADgpmJc3e82f8dVSRpAhlUAAADgLmJcXc4mb6OEe1cl9TzDKgAAAHA3cXp1NZ96qJWk3mdYBQAAAO7q
        w0OtjKuSephhFQAAALg7D7WS1PcMqwAAAEArPjzUyulVST3KsAoAAAC0ar/fvb93tWHAkKSuZVgFAAAA
        Wvfu3tUYLQyskjqeYRUAAADohOO4ul27d1VSLzKsAgAAAJ0S964uZxNXA0jqdIZVAAAAoHPi9Opm+fx3
        XDWwSupYhlUAAACgk2Jc3W0W7l2V1MkMqwAAAEBnxbhaXg1wHDOMq5I6kmEVAAAA6Lz9fvf3aoAYNQys
        klrOsAoAAAD0Qnk1wOLl8W3YcPeqpBYzrAIAAAC9UV4NsJpPPdhKUqsZVgEAAIDeiYF1u555sJWk1jKs
        AgAAAL3kwVaS2sywCgAAAPSaB1tJaiPDKgAAANB7Hmwl6d4ZVgEAAIBB8GArSffMsAoAAAAMitOrku6R
        YRUAAAAYHKdXJd06wyoAAAAwWE6vSrpVhlUAAABg0JxelXSLDKsAAADAKDSeXq0NJZJ0aYZVAAAAYDSO
        p1f3u7+nV2MgMbBK+kaGVQAAAGB03L0q6acZVgEAAIBRKk+vbpbPh/nTw9tYYlyVdGGGVQAAAGDUPjzc
        KkYTA6ukLzKsAgAAALxyPYCkTIZVAAAAgEJ5etX1AJK+yrAKAAAAUBMD63a7Pixnk7frAZxelVTLsAoA
        AABwxnFgXc9cDyDpQ4ZVAAAAgE+cvR7AwCqNOsMqAAAAwAXKgXU1n75dDxDjinFVGm2GVQAAAICEGFh3
        m4X7V6WRZ1gFAAAASDqeXt3vjvevuh5AGmeGVQAAAIBvKgdW969K48uwCgAAAPBDZ+9fNbBKg82wCgAA
        AHAlMbBut+u/96/GAGNglQaZYRUAAADgyj484CqGGOOqNKgMqwAAAAA3YmCVhpthFQAAAOCGygdcxcC6
        eHl8G1hdDyD1PsMqAAAAwB2UA+t2PTsOrMdxxsAq9TbDKgAAAMAdVQfW+dPD20hjYJV6l2EVAAAAoAUG
        VqnfGVYBAAAAWmRglfqZYRUAAACgAxrvYI0MrFInM6wCAAAAdEh9YJ1Nf78NOU6xSp3KsAoAAADQQeXA
        utssDKxSBzOsAgAAAHRYdWBdziYGVqkjGVYBAAAAeiJG1u12fVjNpx50JbWcYRUAAACgZ46nWHebw2b5
        bGCVWsqwCgAAANBT5TUBMbC6h1W6b4ZVAAAAgJ7zoCvp/hlWAQAAAAaiHFjP3sNqZJWulmEVAAAAYICq
        97A6xSpdP8MqAAAAwIBVrwlwilW6XoZVAAAAgJGonmKNgdUpVun7GVYBAAAARqZ6inU5m3wcWI2s0pcZ
        VgEAAABGKgZWp1il72VYBQAAAOB0inW7nh1PsZ7uYo2MrNKHDKsAAAAAnNRPsS5eHl0VIDVkWAUAAACg
        UXmKNUbW1XzqqgCpkmEVAAAAgC+VI2v5wCtXBWjsGVYBAAAAuFj1qoC4j/XdVQGRkVUjybAKAAAAwLdU
        rwr4cB9rZGTVgDOsAgAAAPBjRlaNLcMqAAAAAFdlZNUYMqwCAAAAcDMXj6yGVvUswyoAAAAAd3FuZD0N
        rUZW9SjDKgAAAAB3dxpZX9ttFofVfHqYPz24MkC9ybAKAAAAQKtiZC2H1u12fRxZXRmgrmdYBQAAAKBT
        XBmgPmRYBQAAAKCzqiPrdj37/MoAQ6vumGEVAAAAgF6oXhlQnmZdziaGVrWSYRUAAACAXqoOrfEArMZr
        AyJDq26QYRUAAACAQaieZo2htfoQLEOrrp1hFQAAAIDBqV8bYGjVtTOsAgAAADB43x5aja06k2EVAAAA
        gNGpD63b7frDw7CMrfoswyoAAAAAvPowtq5np1OtxlbVM6wCAAAAQIN3Q+snY+u7wc3YOpoMqwAAAABw
        oa/G1vJU66cnWw2ug8iwCgAAAAA/8G5s3W1OY+und7ZGBtdeZ1gFAAAAgCsrx9bq4BoPyDp3b+uXJ1yj
        yqin9jOsAgAAAMCdvBtbGwbXOOF69mFZUdPgWlb9frp5hlUAAAAAaNmHE67l6FpcKXBudDW8tpdhFQAA
        AAA67Nzoutss3g2v5ehaH15T42tZ9fvrrdrHyLAKAAAAAD3VOLqWp12LKwYuOfXaOMCeGRQvrum/1WZN
        P8evavjvnD5ehlUAAAAAGK7q+PpugN1tzg6w9RH23BBbrWmE/LKmMTNb03/3gpp+DWXlrzl+/fFxiOJj
        Eh+fqPx4GVYBAAAAYOTqA+y7EbZpjK0MsvVRthxmq8VIWVYOl591buz8rOqPUf/xy59XfRyNjtcqvP56
        yl9f9dfc9HEpOLEKAAAAAHxP0/B4rupgeeuafvx6P2RYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAA
        AABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyr
        AAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAA
        kgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEA
        AAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZ
        VgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAA
        ACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwC
        AAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABI
        MqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAA
        AABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRY
        BQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAA
        kGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoA
        AAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJ
        sAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAA
        ACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEV
        AAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABA
        kmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAA
        AABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTD
        KgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAA
        gCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUA
        AAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJ
        hlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAA
        AABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyr
        AAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAA
        kgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEA
        AAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZ
        VgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAA
        ACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwC
        AAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABI
        MqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRYBQAA
        AABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAAkGRY
        BQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoAAAAA
        kGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJsAoA
        AAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAAACDJ
        sAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEVAAAA
        ACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABAkmEV
        AAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAAAABA
        kmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJhlUAAAAAgCTDKgAA
        AABAkmEVAAAAACDJsAoAAAAAkGRYBQAAAABIMqwCAAAAACQZVgEAAAAAkgyrAAAAAABJb8Pqfrc5nNrv
        Lu5/+/3ZAAAAAAAGavmv2X//vZxNfx8+a/708K7Fy+Ox5WxyajWfHtssnw/b9eyt7frDYGuEBQAAAAB6
        7m1Y/TP5dbi4//77yz4bZ8tBtjrCfja+AgAAAAB0TDGsNoyj6ZpG2KZq/159fI3h9d3J1/LUq8EVAAAA
        AOiGKw6r2ZpG16jyfeqja3nStX7K1dgKAAAAANxRi8PqZzUNrlHl+9RPuJ473QoAAAAAcGUdHVY/64ux
        NaqOrYZWAAAAAODKejisNnVmbK1eI2BoBQAAAACuZCDDalOfjK2GVgAAAADgBwY8rNb7ZGgtrw7YbRZG
        VgAAAADgKyMaVuudGVnjNOtyNvlwmhUAAAAAoDDiYbXemaE1TrNuls9GVgAAAACgZFg9m5EVAAAAAGhm
        WL2o2mlWIysAAAAAjJphNZ2RFQAAAADGzrD6oyoDa1SOrOWDrwysAAAAADBIhtWrVRtZ508Ph9V8etht
        Fk6xAgAAAMCwGFZvUmVkrV8VYGAFAAAAgN4zrN40p1gBAAAAYIgMq3er4RTr8S5WAysAAAAA9I1h9e41
        nGJ1TQAAAAAA9IphtdUqI2t5TcB2uzawAgAAAEC3GVY7UWVgjWsClrOJe1gBAAAAoLsMq52qNrC6hxUA
        AAAAOsmw2skMrAAAAADQZYbVzlcMrFHcw2pgBQAAAIDWGVZ7U+UUq4EVAAAAAFplWO1dlYHVFQEAAAAA
        0ArDam8zsAIAAABAWwyrva8YWMuHXO02C+MqAAAAANyWYXUwFadXY2BdziYGVgAAAAC4HcPq4KoMrKv5
        9LDfbQysAAAAAHBdhtVBVlwPEN+ePz0cNstnAysAAAAAXI9hddDVBlYPuAIAAACAqzCsjqJiYPWAKwAA
        AAC4CsPqqKoMrO5fBQAAAIBvM6yOrmJcjW+f7l91PQAAAAAAZBhWR1tlYHU9AAAAAACkGFZHXzGuuh4A
        AAAAAC5mWFVR5fTqdj0zrgIAAADAeYZVVSquB4jTq8vZxOlVAAAAAGhmWFVDxenV6sOtAAAAAIATw6rO
        VDu9ut2unV4FAAAAgDeGVX2R06sAAAAAUGdY1QUVp1fj2/Fwq91m4fQqAAAAAGNmWFUip1cBAAAAIBhW
        9Y2cXgUAAABg3Ayr+mZOrwIAAAAwXoZV/bBiYF3OJof9buP0KgAAAABjYFjVFaqcXt2uZ8ZVAAAAAIbO
        sKorFePqa7Pp78NqPnV6FQAAAIAhM6zqyhWnVz3YCgAAAIABM6zqBhXjapxe9WArAAAAAAbIsKobVgys
        HmwFAAAAwMAYVnXjinHVg60AAAAAGBDDqu5QjKuvuRoAAAAAgIEwrOpOFeNqfNvVAAAAAAD0nGFV7RRX
        A+w2C+MqAAAAAH1kWFUL1a4GMK4CAAAA0DOGVbVYMa6WVwMAAAAAQE8YVtVyxb2ri5fHw3a7dnoVAAAA
        gD4wrKoDFVcDxL2r2/XMuAoAAABA1xlW1ZGKcTWuBljNp4f9fld8jgIAAABA5xhW1aGKcTW+Xd676vQq
        AAAAAB1kWFV3i3tXd5uFcRUAAACArjGsqqMVp1fduwoAAABABxlW1eGKcTXuXd0sn42rAAAAAHSFYVUd
        rxhX49vlvasAAAAA0DLDqvpV3LvqoVYAAAAAtMywqh5VnFyNe1c91AoAAACAFhlW1cMmHmoFAAAAQKsM
        q+ppxUOtVvPpYb/fFZ/PAAAAAHAXhlX1NA+1AgAAAKA9hlUNIw+1AgAAAOCODKsaQMXJ1RhXt9u1cRUA
        AACAWzOsakAVD7XabRbGVQAAAABuybCqAVXcuxrj6nY9M64CAAAAcCuGVQ2wya/DbPr7sJpPjasAAAAA
        3IJhVQOsOLka345xNR5qBQAAAABXZFjV8FvOJsZVAAAAAK7JsKqBV5xcXbw8HsdVVwMAAAAAcAWGVY2g
        Ylw9PtRquzauAgAAAPBThlWNqMmv47i62yyMqwAAAAD8hGFVI6sYV7frmXEVAAAAgO8yrGpkxbUAr82m
        v42rAAAAAHyXYVUjrRhXN8tn4yoAAAAAWYZVjbTKydXVfHrY73fFawIAAAAAvmRY1ciLgfX1n8ZVAAAA
        ABIMq1LZcjY57Heb4rUBAAAAAGcZVqVjxcnVxcujcRUAAACArxhWpVPGVQAAAAAuY1iV3lUZV7fb9eF/
        +33xWgEAAACAE8OqdK7504NxFQAAAIAmhlXps2Jc3W0WxlUAAAAAqgyr0qdNfr2dXF3PjKsAAAAAlAyr
        0qfFnavGVQAAAADeM6xKX1aMq7Ppb+MqAAAAAMGwKl2UcRUAAACAvwyrUqpiXN0sn42rAAAAAONlWJVS
        VU6uGlcBAAAARsuwKn2rYlxdzafGVQAAAIDxMaxK364yru73u+I1BQAAAMAIGFalHxVXA7z+07gKAAAA
        MCqGVelaGVcBAAAARsOwKl2z5WxiXAUAAAAYPsOqdO2MqwAAAACDZ1iVbtFxXN1titcZAAAAAANjWJVu
        lXEVAAAAYLAMq9JNmvw6/nPx8mhcBQAAABgew6p0s4yrAAAAAENlWJVumnEVAAAAYIgMq9LNM64CAAAA
        DI1hVbpLxlUAAACAITGsSnfLuAoAAAAwFIZV6a4ZVwEAAACGwLAqtZVxFQAAAKC3DKtSm5Xj6v/2++I1
        CQAAAEAPGFaltotxdbtdG1cBAAAA+sOwKnWh+dODcRUAAACgPwyrUiea/DKuAgAAAPSHYVXqUsZVAAAA
        gF4wrEpdy7gKAAAA0HmGVamLxbi62yyMqwAAAADdZFiVuppxFQAAAKCzDKtSZyseaGVcBQAAAOgcw6rU
        2Sa/jKsAAAAA3WRYlTpdZVzdrmfGVQAAAIBuMKxKnc+4CgAAANA1hlWpFxlXAQAAALrEsCr1JuMqAAAA
        QFcYVqVeVYyrs+lv4yoAAABAewyrUi8zrgIAAAC0ybAq9TbjKgAAAEBbDKtSb6tcC7BZPhtXAQAAAO7H
        sCr1PuMqAAAAwL0ZVqVBZFwFAAAAuCfDqjSYinF1NZ8aVwEAAABuy7AqDSrjKgAAAMA9GFalwVUZV/f7
        XfFaBwAAAPgoDmbdq4ExrEpDzrgKAAAA49Y0cEaxF2y362OL5epDL39mH3p6fvlQ0/dr+u+VP1b8uE0/
        n6hnDKvS0FvOJsZVAAAAGLimobI+nJbDZ9NAeqvO/Xj1Ebb8uTb9OjrKsCqNoeO4utsUr3sAAACg76rD
        Y3n6tDqgNo2Zfak+uNZPuXaEYVUafJNfx38uXh6NqwAAANBT1WEx3t8PZUS9tOrYGr/+6sejJYZVaRQZ
        VwEAAKBXqsNhOaTGuDiWIfWryo9Fi0OrYVUaTbVx9Y7/QwMAAABcoBwGq1/ab0i9rHJkrV8dcEOGVWmM
        zZ8eThdCAwAAAO2pj6mG1OsUH8cbj6yGVWmUTX4dx9XdZmFcBQAAgDszpt63G42shlVptBXj6nY9M64C
        AADAHZSjXox88WXrTSOgblt5XcAVBlbDqjTa4s7V12bT38ZVAAAAuJFywIvTku5M7U7x+xC/Hz84xWpY
        lUZfMa5uls/GVQAAALiScqxzOrX7ffMUq2FVGn2Vk6ur+dS4CgAAAD8Q76vdndrP6nexfsGwKqmoMq7u
        d5vifyMAAACAS5SDqi/373/1awLOMKxK+thyNjGuAgAAwAWqg2rTSKd+98nAaliVVCuuBnj95+Ll8Tiu
        XnD0HQAAAEbHCdXxdOYEq2FVUkPFuDp/ejjsNgvjKgAAABQMquOtOrC+MqxK+qTJr+O4ul3PjKsAAACM
        mkFVZfH7HxlWJZ0vTq4WD7XaLJ+NqwAAAIxOvBeOPOVf7/vjxKqkCyrG1dV8Wh53BwAAgMEzqOp8hlVJ
        l1ScXI1vL2eT40OtAAAAYKiOX/b/+t43vuy/eVSTDKuSMhXj6uLl8fg3dq4GAAAAYEh82b8uz7AqKVtx
        etVDrQAAABgSg6pyGVYlfadiXPVQKwAAAPquPKXqaf/KZViV9JOKcdW9qwAAAPSRU6r6foZVST8tTq++
        /jPuXY1x1elVAAAA+sApVf0sw6qka1RcDeDeVQAAALou3rN64r9+nmFV0rUqxtW4GmA1nx72+13xf1kA
        AADQDb70X9fLsCrpmhXjany7vHfV6VUAAAC6oPzS/+aRTMpmWJV0w1wNAAAAQNvKL/13SlXXzbAq6VZV
        rgbYLJ9dDQAAAMDd+dJ/3S7DqqRbVrkaYPHyePw/M6dXAQAAuIfyS/+NqrpNhlVJ96gYV10NAAAAwD2U
        o2rzICZdI8OqpHtWXA3gwVYAAADcQrzPjKvonFLV7TOsSrp3Tq8CAABwI+5T1f0yrEpqo+LuVadXAQAA
        uIZ4T2lU1X0zrEpqs8rp1c3y+fjlGgZWAAAAMoyqaifDqqS2K06vxrcXL4+H3WZhXAUAAOAiRlW1l2FV
        UleqXA+wmk9dDwAAAMCXPPlf7WVYldSlKqdXXQ8AAADAOfE+0aiqdjOsSupitYF1u54ZWAEAADgyqqob
        GVYldbliYI3rAcr7Vw2sAAAA42VUVXcyrErqQ8Xp1RhYl7OJgRUAAGCEjKrqVoZVSX2qMrDGCVZXBAAA
        AIyDUVXdy7AqqY8ZWAEAAEYj3uttt+uGYUtqM8OqpD5XDKxRPORqs3w2sAIAAAxIOaq+/Jk1DFtSmxlW
        JQ2hGFiLkfU0sO42BlYAAIAeM6qq2xlWJQ2p2sC6mk896AoAAKCn4sCMUVXdzbAqaYhVBlb3sAIAAPRP
        vH8zqqrbGVYlDbnKwBq5JgAAAKD74v2aUVXdz7AqaSxVRlbXBAAAAHRTvD9bLFcNI5bUtQyrksZWZWAt
        rwlwihUAAKB9RlX1K8OqpLFWGVgjp1gBAADaE+/Bttu1KwDUowyrkvTlKVYjKwAAwG3F+y+jqvqVYVWS
        /lYOrJWRdTmbHLbrmZEVAADgRuJ9llFV/cuwKknNVQbWyFUBAAAA1xfvrdyrqn5mWJWkr6udYm0aWQ2t
        AAAAOfE+Ku5VbR6tpK5nWJWkyysHViMr3FT5OvppAAB0m3tV1e8Mq5L0vb4aWd3JyohVx81q8ZcPp15f
        I2VxSiFeN1HcaVwtHiRXLV5jTVW/T/XfP/13X3+M049Z+XnUf44AANxH/NnLqKp+Z1iVpJ93ZmStPviq
        HHCgz6oDZHQaKCsDaTloVkfQeC0sXh6PxWsjitfJJX14vV1Q03+nWvlziJ9P/Nyq4+zx51+OsA3jKwAA
        Pxd/rnKvqvqfYVWSrlttZI1iyIkB5zjaxGBTGWqgS8rPy6g+mpanScvBtBxLz42k714X1aqvkbZr+PlV
        fw318TV+/fFxaBpdAQC4XLwvclpV/c+wKkm3rTbglGNNOdKUA41xhlsrP8fKqsNpedL0ktH03ed3VB0q
        z1X/d7pY0887qnyf6sehHF3rr2WvZwCAz8Wfk4yqGkaGVUm6Xw1jTTnQGFq5hvLzJvpqOI3Pu/pw+u7z
        Nap+zlarf78x9MnHofz4VcfW6slWr2cAgDfxZyJXAGg4GVYlqZ0aBppynGk6BWeYIZSfB1F1OD2Opxec
        OP30c7Ba9fvp88587L56PQMAjFH8hX/zQCX1McOqJHWjhmEmimEmxrEYyk53tBpbB638fY2q42n9jtMv
        h9Oo+nlVrf79dN0aPtbl79G7E61exwDAiMSfeVwBoGFlWJWkblYdZj4ZZ2JoaxpbDTXdVf09isrhNLra
        qdPq91H7NfzexO9l+ZcmTrMCAEMXf8ZxBYCGl2FVkvpTwzgTlQNNObbGMOfJ5e0pP9Zl1eG0PHX64+E0
        qn4/9auG38f4vY/Ph3j9GlkBgKGJP984rarhZViVpP5WHWcqA01UjnRnB9fKcFPG56ofq7JyuI6P57ef
        rF//fSyrfh8Nu9rveXyOVEfW8vMNAKCP4s8xTqtqmBlWJWlYVYe52lgTlcNejH1RjH/l6Fq/VqA+vJYN
        SdOvL6oOplH9ftPqaFofTqsf72NNvydR/ftJUe3zo3ydHq8LKF6TAAB94oFVGm6GVUkaR9VBr1rl+1TH
        wXIwLE+7vhtfqwNsZYQtq4+U58po+ve/qv7zOv18K0NpdSz9ajCNqh+vY00f07L695WyVT6XytdlfJ4e
        X3/Faw0AoMvizyuuANBwM6xKkqpjYL3a960PjeXYUxajZIyTZeVgGcWA+W6cPVN19Pys8r9X/TGqP3b8
        XD4bScvqv8ZjTR+Lak3/jnSrap978Xkbn9vxOjCwAgBdFX9GcQWAhp1hVZL0VdVR57Oa/t2G6sPmd2v6
        bzfW9HNtqunflbpW7fM1/tIg/lIhTmIbWAGALom/AHZaVcPOsCpJumXV4fIeNf0cpKFW+byPv2yIk9rH
        B9Q5xQoAtMxpVY0jw6okSVK/qw2scU2AgRUAaJPTqhpHhlVJkqRh1DCwuocVALg3p1U1ngyrkiRJw6sY
        WCMDKwBwT3H3e/MIJQ0tw6okSdJwM7ACAHfktKrGlWFVkiRp+J25gxUA4JqcVtW4MqxKkiSNp8rAupxN
        Dtvt2ulVAOAqnFbV+DKsSpIkja/KwLqaT4+nSwysAMBPxFfDNI9P0lAzrEqSJI2zGFeLgXX+9HDYLJ/d
        vwoAfIvTqhpnhlVJkqRxVxtY4wFXxlUAICP+cvblz6xheJKGnGFVkiRJUeV6APevAgAZTqtqnBlWJUmS
        VM31AABAQvw5wWlVjTPDqiRJkupVrgdYvDwedpuFcRUA+CD+fBBf5dI8OklDz7AqSZKkcxUDa1wPsJpP
        D/vdxsAKAJzEnwtcA6DxZliVJEnSZ9VOr3q4FQBQir90bR6cpDFkWJUkSdIlOb0KAFQ4rSoZViVJknRp
        tdOr7l4FgPGKPwN4aJXGnWFVkiRJ2Ypx9XR6db8r3mIBAGPhoVWSYVWSJEnfrXJ6Nd5cOb0KAOPgGgAp
        MqxKkiTpJxXj6vzpwYOtAGAk4qtVmocmaUwZViVJknSNJh5sBQBjEP8f7xoAKTKsSpIk6Vp5sBUADJ5r
        AKQyw6okSZKuWeVqgM3y2bgKAAPjGgCpzLAqSZKkW1S7GgAA6D/XAEjVDKuSJEm6VZWrAeJNmNOrANBv
        rgGQqhlWJUmSdMtiXH0trgbYrmfGVQDoMdcASNUMq5IkSbp1xbgaVwO4dxUA+sk1AFI9w6okSZLuUWVc
        Xc4m7l0FgJ5xDYBUz7AqSZKkexYD6+s/497VGFedXgWAfoj/z375M2sYl6SxZliVJEnSvSvG1bh3dbdZ
        GFcBoAfiL0SbxyVprBlWJUmS1GIeagUA3ecaAKkpw6okSZLazEOtAKDzDKtSU4ZVSZIktVnloVar+dRD
        rQCgg2JYdb+qVM+wKkmSpC5U3Lu6nE2MqwDQMdvtumFUksaeYVWSJEldqRhXFy+Px3HV1QAA0A2uAZCa
        MqxKkiSpSxXj6vGhVtu1cRUAWuZ+VelchlVJkiR1scmv47i62yyMqwDQIverSucyrEqSJKmrFePqdj0z
        rgJAS+J6nuZRSRp7xbAqSZIkdbkYV18tJUnSfXu7BuDPUlK9P8v/D+/J8hDSsAOqAAAAAElFTkSuQmCC
</value>
  </data>
</root>