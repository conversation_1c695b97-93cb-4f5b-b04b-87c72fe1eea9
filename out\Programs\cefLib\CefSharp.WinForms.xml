<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CefSharp.WinForms</name>
    </assembly>
    <members>
        <member name="T:CefSharp.WinForms.ChromiumWebBrowser">
            <summary>
            ChromiumWebBrowser is the WinForms web browser control
            </summary>
            <seealso cref="T:System.Windows.Forms.Control" />
            <seealso cref="T:CefSharp.WinForms.IWinFormsWebBrowser" />
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.canExecuteJavascriptInMainFrameId">
            <summary>
            Used as workaround for issue https://github.com/cefsharp/CefSharp/issues/3021
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.browserInitialized">
            <summary>
            The browser initialized - boolean represented as 0 (false) and 1(true) as we use Interlocker to increment/reset
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.CanExecuteJavascriptInMainFrame">
            <summary>
            A flag that indicates if you can execute javascript in the main frame.
            Flag is set to true in IRenderProcessMessageHandler.OnContextCreated.
            and false in IRenderProcessMessageHandler.OnContextReleased
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.DialogHandler">
            <summary>
            Implement <see cref="T:CefSharp.IDialogHandler" /> and assign to handle dialog events.
            </summary>
            <value>The dialog handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.JsDialogHandler">
            <summary>
            Implement <see cref="T:CefSharp.IJsDialogHandler" /> and assign to handle events related to JavaScript Dialogs.
            </summary>
            <value>The js dialog handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.KeyboardHandler">
            <summary>
            Implement <see cref="T:CefSharp.IKeyboardHandler" /> and assign to handle events related to key press.
            </summary>
            <value>The keyboard handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.RequestHandler">
            <summary>
            Implement <see cref="T:CefSharp.IRequestHandler" /> and assign to handle events related to browser requests.
            </summary>
            <value>The request handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.DownloadHandler">
            <summary>
            Implement <see cref="T:CefSharp.IDownloadHandler" /> and assign to handle events related to downloading files.
            </summary>
            <value>The download handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.LoadHandler">
            <summary>
            Implement <see cref="T:CefSharp.ILoadHandler" /> and assign to handle events related to browser load status.
            </summary>
            <value>The load handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.LifeSpanHandler">
            <summary>
            Implement <see cref="T:CefSharp.ILifeSpanHandler" /> and assign to handle events related to popups.
            </summary>
            <value>The life span handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.DisplayHandler">
            <summary>
            Implement <see cref="T:CefSharp.IDisplayHandler" /> and assign to handle events related to browser display state.
            </summary>
            <value>The display handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.MenuHandler">
            <summary>
            Implement <see cref="T:CefSharp.IContextMenuHandler" /> and assign to handle events related to the browser context menu
            </summary>
            <value>The menu handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.RenderProcessMessageHandler">
            <summary>
            Implement <see cref="T:CefSharp.IRenderProcessMessageHandler" /> and assign to handle messages from the render process.
            </summary>
            <value>The render process message handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.FindHandler">
            <summary>
            Implement <see cref="T:CefSharp.IFindHandler" /> to handle events related to find results.
            </summary>
            <value>The find handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.AudioHandler">
            <summary>
            Implement <see cref="T:CefSharp.IAudioHandler" /> to handle audio events.
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.FocusHandler">
            <summary>
            The <see cref="T:CefSharp.IFocusHandler" /> for this ChromiumWebBrowser.
            </summary>
            <value>The focus handler.</value>
            <remarks>If you need customized focus handling behavior for WinForms, the suggested
            best practice would be to inherit from DefaultFocusHandler and try to avoid
            needing to override the logic in OnGotFocus. The implementation in
            DefaultFocusHandler relies on very detailed behavior of how WinForms and
            Windows interact during window activation.</remarks>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.DragHandler">
            <summary>
            Implement <see cref="T:CefSharp.IDragHandler" /> and assign to handle events related to dragging.
            </summary>
            <value>The drag handler.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.ResourceRequestHandlerFactory">
            <summary>
            Implement <see cref="T:CefSharp.IResourceRequestHandlerFactory" /> and control the loading of resources
            </summary>
            <value>The resource handler factory.</value>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.LoadError">
            <summary>
            Event handler that will get called when the resource load for a navigation fails or is canceled.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.FrameLoadStart">
            <summary>
            Event handler that will get called when the browser begins loading a frame. Multiple frames may be loading at the same
            time. Sub-frames may start or continue loading after the main frame load has ended. This method may not be called for a
            particular frame if the load request for that frame fails. For notification of overall browser load status use
            OnLoadingStateChange instead.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
            <remarks>Whilst this may seem like a logical place to execute js, it's called before the DOM has been loaded, implement
            <see cref="M:CefSharp.IRenderProcessMessageHandler.OnContextCreated(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.IFrame)" /> as it's called when the underlying V8Context is created
            </remarks>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.FrameLoadEnd">
            <summary>
            Event handler that will get called when the browser is done loading a frame. Multiple frames may be loading at the same
            time. Sub-frames may start or continue loading after the main frame load has ended. This method will always be called
            for all frames irrespective of whether the request completes successfully.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.LoadingStateChanged">
            <summary>
            Event handler that will get called when the Loading state has changed.
            This event will be fired twice. Once when loading is initiated either programmatically or
            by user action, and once when loading is terminated due to completion, cancellation of failure.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.ConsoleMessage">
            <summary>
            Event handler for receiving Javascript console messages being sent from web pages.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            (The exception to this is when you're running with settings.MultiThreadedMessageLoop = false, then they'll be the same thread).
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.StatusMessage">
            <summary>
            Event handler for changes to the status message.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang.
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            (The exception to this is when you're running with settings.MultiThreadedMessageLoop = false, then they'll be the same thread).
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.JavascriptMessageReceived">
            <summary>
            Event handler that will get called when the message that originates from CefSharp.PostMessage
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#IWebBrowser#IsBrowserInitialized">
            <summary>
            A flag that indicates whether the WebBrowser is initialized (true) or not (false).
            </summary>
            <value><c>true</c> if this instance is browser initialized; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#OnFrameLoadStart(CefSharp.FrameLoadStartEventArgs)">
            <summary>
            Handles the <see cref="E:FrameLoadStart" /> event.
            </summary>
            <param name="args">The <see cref="T:CefSharp.FrameLoadStartEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#OnFrameLoadEnd(CefSharp.FrameLoadEndEventArgs)">
            <summary>
            Handles the <see cref="E:FrameLoadEnd" /> event.
            </summary>
            <param name="args">The <see cref="T:CefSharp.FrameLoadEndEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#OnConsoleMessage(CefSharp.ConsoleMessageEventArgs)">
            <summary>
            Handles the <see cref="E:ConsoleMessage" /> event.
            </summary>
            <param name="args">The <see cref="T:CefSharp.ConsoleMessageEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#OnStatusMessage(CefSharp.StatusMessageEventArgs)">
            <summary>
            Handles the <see cref="E:StatusMessage" /> event.
            </summary>
            <param name="args">The <see cref="T:CefSharp.StatusMessageEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#OnLoadError(CefSharp.LoadErrorEventArgs)">
            <summary>
            Handles the <see cref="E:LoadError" /> event.
            </summary>
            <param name="args">The <see cref="T:CefSharp.LoadErrorEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#HasParent">
            <summary>
            Gets or sets a value indicating whether this instance has parent.
            </summary>
            <value><c>true</c> if this instance has parent; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#BrowserAdapter">
            <summary>
            Gets the browser adapter.
            </summary>
            <value>The browser adapter.</value>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.InternalIsBrowserInitialized">
            <summary>
            Check is browser is initialized
            </summary>
            <returns>true if browser is initialized</returns>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.ThrowExceptionIfBrowserNotInitialized">
            <summary>
            Throw exception if browser not initialized.
            </summary>
            <exception cref="T:System.Exception">Thrown when an exception error condition occurs.</exception>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.ThrowExceptionIfDisposed">
            <summary>
            Throw exception if disposed.
            </summary>
            <exception cref="T:System.ObjectDisposedException">Thrown when a supplied object has been disposed.</exception>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.managedCefBrowserAdapter">
            <summary>
            The managed cef browser adapter
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.parentFormMessageInterceptor">
            <summary>
            The parent form message interceptor
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.browser">
            <summary>
            The browser
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.designMode">
            <summary>
            A flag that indicates whether or not the designer is active
            NOTE: DesignMode becomes false by the time we get to the destructor/dispose so it gets stored here
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.initialized">
            <summary>
            A flag that indicates whether or not <see cref="M:CefSharp.WinForms.ChromiumWebBrowser.InitializeFieldsAndCefIfRequired"/> has been called.
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.browserCreated">
            <summary>
            Has the underlying Cef Browser been created (slightly different to initialized in that
            the browser is initialized in an async fashion)
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.initialAddressLoaded">
            <summary>
            A flag indicating if the <see cref="P:CefSharp.WinForms.ChromiumWebBrowser.Address"/> was used when calling CreateBrowser
            If false and <see cref="P:CefSharp.WinForms.ChromiumWebBrowser.Address"/> contains a non empty string Load will be called
            on the main frame
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.removeExNoActivateStyle">
            <summary>
            If true the the WS_EX_NOACTIVATE style will be removed so that future mouse clicks
            inside the browser correctly activate and focus the window.
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.browserSettings">
            <summary>
            Browser initialization settings
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.requestContext">
            <summary>
            The request context (we deliberately use a private variable so we can throw an exception if
            user attempts to set after browser created)
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.disposeSignaled">
            <summary>
            The value for disposal, if it's 1 (one) then this instance is either disposed
            or in the process of getting disposed
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.ChromiumWebBrowser.parkingControl">
            <summary>
            Parking control used to temporarily host the CefBrowser instance
            when <see cref="!:RecreatingHandle"/> is <c>true</c>.
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.IsDisposed">
            <summary>
            Gets a value indicating whether this instance is disposed.
            </summary>
            <value><see langword="true" /> if this instance is disposed; otherwise, <see langword="false" />.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.IsActivating">
            <summary>
            Set to true while handing an activating WM_ACTIVATE message.
            MUST ONLY be cleared by DefaultFocusHandler.
            </summary>
            <value><c>true</c> if this instance is activating; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.BrowserSettings">
            <summary>
            Gets or sets the browser settings.
            </summary>
            <value>The browser settings.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.ActivateBrowserOnCreation">
            <summary>
            Activates browser upon creation, the default value is false. Prior to version 73
            the default behaviour was to activate browser on creation (Equivalent of setting this property to true).
            To restore this behaviour set this value to true immediately after you create the <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> instance.
            https://bitbucket.org/chromiumembedded/cef/issues/1856/branch-2526-cef-activates-browser-window
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.RequestContext">
            <summary>
            Gets or sets the request context.
            </summary>
            <value>The request context.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.IsLoading">
            <summary>
            A flag that indicates whether the control is currently loading one or more web pages (true) or not (false).
            </summary>
            <value><c>true</c> if this instance is loading; otherwise, <c>false</c>.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.TooltipText">
            <summary>
            The text that will be displayed as a ToolTip
            </summary>
            <value>The tooltip text.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.Address">
            <summary>
            The address (URL) which the browser control is currently displaying.
            Will automatically be updated as the user navigates to another page (e.g. by clicking on a link).
            </summary>
            <value>The address.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.AddressChanged">
            <summary>
            Occurs when the browser address changed.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.TitleChanged">
            <summary>
            Occurs when the browser title changed.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.ChromiumWebBrowser.IsBrowserInitializedChanged">
            <summary>
            Event called after the underlying CEF browser instance has been created. 
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread.
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.CanGoForward">
            <summary>
            A flag that indicates whether the state of the control currently supports the GoForward action (true) or not (false).
            </summary>
            <value><c>true</c> if this instance can go forward; otherwise, <c>false</c>.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.CanGoBack">
            <summary>
            A flag that indicates whether the state of the control current supports the GoBack action (true) or not (false).
            </summary>
            <value><c>true</c> if this instance can go back; otherwise, <c>false</c>.</value>
            <remarks>In the WPF control, this property is implemented as a Dependency Property and fully supports data
            binding.</remarks>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.IsBrowserInitialized">
            <summary>
            A flag that indicates whether the WebBrowser is initialized (true) or not (false).
            </summary>
            <value><c>true</c> if this instance is browser initialized; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.UseParentFormMessageInterceptor">
            <summary>
            ParentFormMessageInterceptor hooks the Form handle and forwards
            the move/active messages to the browser, the default is true
            and should only be required when using <see cref="P:CefSharp.CefSettingsBase.MultiThreadedMessageLoop"/>
            set to true.
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.ParkControlOnHandleDestroyed">
            <summary>
            By default when <see cref="M:System.Windows.Forms.Control.OnHandleDestroyed(System.EventArgs)"/> is called
            the underlying Browser Hwnd is only parked (moved to a temp parent) 
            when <see cref="P:System.Windows.Forms.Control.RecreatingHandle"/> is <c>true</c>, there are a few other
            cases where parking of the control is desired, you can force parking by setting
            this property to <c>true</c>.
            </summary>
            <remarks>
            You may wish to set this property to <c>true</c> when using the browser in conjunction
            with https://github.com/dockpanelsuite/dockpanelsuite
            </remarks>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.#cctor">
            <summary>
            Initializes static members of the <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> class.
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.OnApplicationExit(System.Object,System.EventArgs)">
            <summary>
            Handles the <see cref="E:ApplicationExit" /> event.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.#ctor">
            <summary>
            This constructor exists as the WinForms designer requires a parameterless constructor, if you are instantiating
            an instance of this class in code then use the <see cref="M:CefSharp.WinForms.ChromiumWebBrowser.#ctor(System.String,CefSharp.IRequestContext)"/>
            constructor overload instead. Using this constructor in code is unsupported and you may experience <see cref="T:System.NullReferenceException"/>'s
            when attempting to access some of the properties immediately after instantiation. 
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.#ctor(CefSharp.Web.HtmlString,CefSharp.IRequestContext)">
            <summary>
            Initializes a new instance of the <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> class.
            **Important** - When using this constructor the <see cref="P:System.Windows.Forms.Control.Dock"/> property
            will default to <see cref="F:System.Windows.Forms.DockStyle.Fill"/>.
            </summary>
            <param name="html">html string to be initially loaded in the browser.</param>
            <param name="requestContext">(Optional) Request context that will be used for this browser instance, if null the Global
            Request Context will be used.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.#ctor(System.String,CefSharp.IRequestContext)">
            <summary>
            Initializes a new instance of the <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/> class.
            **Important** - When using this constructor the <see cref="P:System.Windows.Forms.Control.Dock"/> property
            will default to <see cref="F:System.Windows.Forms.DockStyle.Fill"/>.
            </summary>
            <param name="address">The address.</param>
            <param name="requestContext">(Optional) Request context that will be used for this browser instance, if null the Global
            Request Context will be used.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.InitializeFieldsAndCefIfRequired">
            <summary>
            Required for designer support - this method cannot be inlined as the designer
            will attempt to load libcef.dll and will subsequently throw an exception.
            TODO: Still not happy with this method name, need something better
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.Dispose(System.Boolean)">
            <summary>
            If not in design mode; Releases unmanaged and - optionally - managed resources for the <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/>
            </summary>
            <param name="disposing"><see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.InternalDispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources for the <see cref="T:CefSharp.WinForms.ChromiumWebBrowser"/>
            </summary>
            <param name="disposing"><see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
            <remarks>
            This method cannot be inlined as the designer will attempt to load libcef.dll and will subsequently throw an exception.
            </remarks>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.Load(System.String)">
            <summary>
            Loads the specified URL.
            </summary>
            <param name="url">The URL to be loaded.</param>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.JavascriptObjectRepository">
            <summary>
            The javascript object repository, one repository per ChromiumWebBrowser instance.
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.OnHandleCreated(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.HandleCreated" /> event.
            </summary>
            <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CreateBrowserWindowInfo(System.IntPtr)">
             <summary>
             Override this method to handle creation of WindowInfo. This method can be used to customise aspects of
             browser creation including configuration of settings such as <see cref="P:CefSharp.IWindowInfo.ExStyle"/>.
             Window Activation is disabled by default, you can re-enable it by overriding and removing the
             WS_EX_NOACTIVATE style from <see cref="P:CefSharp.IWindowInfo.ExStyle"/>.
             </summary>
             <param name="handle">Window handle for the Control</param>
             <returns>Window Info</returns>
             <example>
             To re-enable Window Activation then remove WS_EX_NOACTIVATE from ExStyle
             <code>
             const uint WS_EX_NOACTIVATE = 0x08000000;
             windowInfo.ExStyle &amp;= ~WS_EX_NOACTIVATE;
            </code>
             </example>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#OnAfterBrowserCreated(CefSharp.IBrowser)">
            <summary>
            Called after browser created.
            </summary>
            <param name="browser">The browser.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#SetAddress(CefSharp.AddressChangedEventArgs)">
            <summary>
            Sets the address.
            </summary>
            <param name="args">The <see cref="T:CefSharp.AddressChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#SetLoadingStateChange(CefSharp.LoadingStateChangedEventArgs)">
            <summary>
            Sets the loading state change.
            </summary>
            <param name="args">The <see cref="T:CefSharp.LoadingStateChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#SetTitle(CefSharp.TitleChangedEventArgs)">
            <summary>
            Sets the title.
            </summary>
            <param name="args">The <see cref="T:CefSharp.TitleChangedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.CefSharp#Internals#IWebBrowserInternal#SetTooltipText(System.String)">
            <summary>
            Sets the tooltip text.
            </summary>
            <param name="tooltipText">The tooltip text.</param>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.Focused">
            <summary>
            Manually implement Focused because cef does not implement it.
            </summary>
            <value><c>true</c> if focused; otherwise, <c>false</c>.</value>
            <remarks>This is also how the Microsoft's WebBrowserControl implements the Focused property.</remarks>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.OnSizeChanged(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.SizeChanged" /> event.
            </summary>
            <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.ResizeBrowser(System.Int32,System.Int32)">
            <summary>
            Resizes the browser.
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.HideInternal">
            <summary>
            When minimized set the browser window size to 0x0 to reduce resource usage.
            https://github.com/chromiumembedded/cef/blob/c7701b8a6168f105f2c2d6b239ce3958da3e3f13/tests/cefclient/browser/browser_window_std_win.cc#L87
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.ShowInternal">
            <summary>
            Show the browser (called after previous minimised)
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.OnGotFocus(System.EventArgs)">
            <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.GotFocus" /> event.
            </summary>
            <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.GetBrowser">
            <summary>
            Returns the current IBrowser Instance
            </summary>
            <returns>browser instance or null</returns>
        </member>
        <member name="P:CefSharp.WinForms.ChromiumWebBrowser.DefaultSize">
            <summary>
            Gets the default size of the control.
            </summary>
            <value>
            The default <see cref="T:System.Drawing.Size" /> of the control.
            </value>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowser.IsInputKey(System.Windows.Forms.Keys)">
            <summary>
            Makes certain keys as Input keys when CefSettings.MultiThreadedMessageLoop = false
            </summary>
            <param name="keyData">key data</param>
            <returns>true for a select list of keys otherwise defers to base.IsInputKey</returns>
        </member>
        <member name="T:CefSharp.WinForms.CefSettings">
            <summary>
            Initialization settings. Many of these and other settings can also configured
            using command-line switches.
            </summary>
        </member>
        <member name="T:CefSharp.WinForms.ChromiumWebBrowserDesigner">
            <summary>
            ChromiumWebBrowser Control Designer
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowserDesigner.OnPaintAdornments(System.Windows.Forms.PaintEventArgs)">
            <summary>
            Receives a call when the control that the designer is managing has painted its surface so the designer can paint any additional adornments on top of the control.
            </summary>
            <param name="pe">args</param>
        </member>
        <member name="M:CefSharp.WinForms.ChromiumWebBrowserDesigner.PreFilterProperties(System.Collections.IDictionary)">
            <summary>
            Adjusts the set of properties the component exposes through a TypeDescriptor.
            </summary>
            <param name="properties">properties</param>
        </member>
        <member name="T:CefSharp.WinForms.Internals.DefaultFocusHandler">
            <summary>
            Default implementation of <see cref="T:CefSharp.IFocusHandler" />
            for the WinForms implementation
            </summary>
            <seealso cref="T:CefSharp.IFocusHandler" />
        </member>
        <member name="M:CefSharp.WinForms.Internals.DefaultFocusHandler.OnGotFocus(CefSharp.IWebBrowser,CefSharp.IBrowser)">
            <summary>
            Called when the browser component has received focus.
            </summary>
            <param name="chromiumWebBrowser">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <remarks>Try to avoid needing to override this logic in a subclass. The implementation in
            DefaultFocusHandler relies on very detailed behavior of how WinForms and
            Windows interact during window activation.</remarks>
        </member>
        <member name="M:CefSharp.WinForms.Internals.DefaultFocusHandler.OnSetFocus(CefSharp.IWebBrowser,CefSharp.IBrowser,CefSharp.CefFocusSource)">
            <summary>
            Called when the browser component is requesting focus.
            </summary>
            <param name="chromiumWebBrowser">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="source">Indicates where the focus request is originating from.</param>
            <returns>Return false to allow the focus to be set or true to cancel setting the focus.</returns>
        </member>
        <member name="M:CefSharp.WinForms.Internals.DefaultFocusHandler.OnTakeFocus(CefSharp.IWebBrowser,CefSharp.IBrowser,System.Boolean)">
            <summary>
            Called when the browser component is about to lose focus.
            For instance, if focus was on the last HTML element and the user pressed the TAB key.
            </summary>
            <param name="chromiumWebBrowser">the ChromiumWebBrowser control</param>
            <param name="browser">the browser object</param>
            <param name="next">Will be true if the browser is giving focus to the next component
            and false if the browser is giving focus to the previous component.</param>
        </member>
        <member name="T:CefSharp.WinForms.Internals.ControlExtensions">
            <summary>
            ControlExtensions.
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ControlExtensions.InvokeOnUiThreadIfRequired(System.Windows.Forms.Control,System.Action)">
            <summary>
            Executes the Action asynchronously on the UI thread, does not block execution on the calling thread.
            No action will be performed if the control doesn't have a valid handle or the control is Disposed/Disposing.
            </summary>
            <param name="control">the control for which the update is required</param>
            <param name="action">action to be performed on the control</param>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ControlExtensions.Activate(System.Windows.Forms.Control)">
            <summary>
            Activates the specified control.
            </summary>
            <param name="control">The control.</param>
            <returns><c>true</c> if XXXX, <c>false</c> otherwise.</returns>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ControlExtensions.IsActiveControl(System.Windows.Forms.Control)">
            <summary>
            Returns whether the supplied control is the currently
            active control.
            </summary>
            <param name="control">the control to check</param>
            <returns>true if the control is the currently active control</returns>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ControlExtensions.SelectNextControl(System.Windows.Forms.Control,System.Boolean)">
            <summary>
            Selects the next control.
            </summary>
            <param name="control">The control.</param>
            <param name="next">if set to <c>true</c> [next].</param>
        </member>
        <member name="T:CefSharp.WinForms.Internals.ParentFormMessageInterceptor">
            <summary>
            ParentFormMessageInterceptor - hooks into the parent forms
            message loop to incercept messages like WM_MOVE
            </summary>
            <seealso cref="T:System.Windows.Forms.NativeWindow" />
            <seealso cref="T:System.IDisposable" />
        </member>
        <member name="F:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.isMoving">
            <summary>
            Keep track of whether a move is in progress.
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.movingRectangle">
            <summary>
            Used to determine the coordinates involved in the move
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.previousWindowState">
            <summary>
            Store the previous window state, used to determine if the
            Windows was previously <see cref="F:System.Windows.Forms.FormWindowState.Minimized"/>
            and resume rendering
            </summary>
        </member>
        <member name="P:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.Browser">
            <summary>
            Gets or sets the browser.
            </summary>
            <value>The browser.</value>
        </member>
        <member name="P:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.ParentForm">
            <summary>
            Gets or sets the parent form.
            </summary>
            <value>The parent form.</value>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.#ctor(CefSharp.WinForms.ChromiumWebBrowser)">
            <summary>
            Initializes a new instance of the <see cref="T:CefSharp.WinForms.Internals.ParentFormMessageInterceptor"/> class.
            </summary>
            <param name="browser">The browser.</param>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.RefindParentForm">
            <summary>
            Call to force refinding of the parent Form.
            (i.e. top level window that owns the ChromiumWebBrowserControl)
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.ParentParentChanged(System.Object,System.EventArgs)">
            <summary>
            Adjust the form to listen to if the ChromiumWebBrowserControl's parent changes.
            </summary>
            <param name="sender">The ChromiumWebBrowser whose parent has changed.</param>
            <param name="e">The <see cref="T:System.EventArgs" /> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.OnHandleCreated(System.Object,System.EventArgs)">
            <summary>
            Handles the <see cref="E:HandleCreated" /> event.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.OnHandleDestroyed(System.Object,System.EventArgs)">
            <summary>
            Handles the <see cref="E:HandleDestroyed" /> event.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.WndProc(System.Windows.Forms.Message@)">
            <summary>
            Invokes the default window procedure associated with this window.
            </summary>
            <param name="m">A <see cref="T:System.Windows.Forms.Message" /> that is associated with the current Windows message.</param>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.OnMoving">
            <summary>
            Called when [moving].
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.Dispose">
            <summary>
            Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.Dispose(System.Boolean)">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:CefSharp.WinForms.Internals.ParentFormMessageInterceptor.OnThreadException(System.Exception)">
            <summary>
            When overridden in a derived class, manages an unhandled thread exception.
            </summary>
            <param name="e">An <see cref="T:System.Exception" /> that specifies the unhandled thread exception.</param>
        </member>
        <member name="T:CefSharp.WinForms.Internals.NativeMethods">
            <summary>
            Class NativeMethods.
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.Internals.NativeMethods.WM_MOVE">
            <summary>
            The w m_ move
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.Internals.NativeMethods.WM_MOVING">
            <summary>
            The w m_ moving
            </summary>
        </member>
        <member name="F:CefSharp.WinForms.Internals.NativeMethods.WM_ACTIVATE">
            <summary>
            The w m_ activate
            </summary>
        </member>
        <member name="T:CefSharp.WinForms.IWinFormsWebBrowser">
            <summary>
            WinForms specific implementation, has events the
            <see cref="T:CefSharp.WinForms.ChromiumWebBrowser" /> implementation exposes.
            </summary>
            <seealso cref="T:CefSharp.IWebBrowser" />
        </member>
        <member name="E:CefSharp.WinForms.IWinFormsWebBrowser.TitleChanged">
            <summary>
            Occurs when the browser title changed.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread. 
            </summary>
        </member>
        <member name="E:CefSharp.WinForms.IWinFormsWebBrowser.AddressChanged">
            <summary>
            Occurs when the browser address changed.
            It's important to note this event is fired on a CEF UI thread, which by default is not the same as your application UI
            thread. It is unwise to block on this thread for any length of time as your browser will become unresponsive and/or hang..
            To access UI elements you'll need to Invoke/Dispatch onto the UI Thread. 
            </summary>
        </member>
        <member name="T:CefSharp.WinForms.WebBrowserExtensions">
            <summary>
            Helper extensions for performing common CefSharp related WinForms tasks
            </summary>
        </member>
        <member name="M:CefSharp.WinForms.WebBrowserExtensions.DestroyWindow(CefSharp.IWebBrowser)">
            <summary>
            Manually call https://docs.microsoft.com/en-us/windows/win32/api/winuser/nf-winuser-destroywindow
            passing in the handle returned from <see cref="M:CefSharp.IBrowserHost.GetWindowHandle"/>.
            This method can be used to manually close the underlying CefBrowser instance.
            This will avoid the WM_Close message that CEF sends by default to the top level window.
            (Which closes your application). This method should generally only be used in the WinForms version.
            </summary>
            <param name="chromiumWebBrowser">the ChromiumWebBrowser instance</param>
            <returns>If the function succeeds, the return value is true.</returns>
            <example>
            <code>
            //Invoke on the CEF UI Thread
            Cef.UIThreadTaskFactory.StartNew(() =>
            {
              var closed = chromiumWebBrowser.DestroyWindow();
            });
            </code>
            </example>
        </member>
    </members>
</doc>
