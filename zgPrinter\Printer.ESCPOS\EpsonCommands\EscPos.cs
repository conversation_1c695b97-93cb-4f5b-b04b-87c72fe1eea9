﻿using ESC_POS_USB_NET.Interfaces.Command;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using zgPrinter.Model;
using zgPrinter.Printer.ESCPOS.Interface;

namespace zgPrinter.Printer.ESCPOS.EpsonCommands
{
    internal class EscPos : IPrintCommand
    {
        #region command property
        public IFontMode FontModeCmd { get; set; }
        public IFontWidth FontWidthCmd { get; set; }
        public IAlignment AlignmentCmd { get; set; }
        public IPaperCut PaperCutCmd { get; set; }
        public IDrawer DrawerCmd { get; set; }
        public IQrCode QrCodeCmd { get; set; }
        public IBarCode BarCodeCmd { get; set; }
        public IInitializePrint InitializePrintCmd { get; set; }
        public IImage ImageCmd { get; set; }
        public ILineHeight LineHeightCmd { get; set; }
        public int ColsNomal => 40;
        public int ColsCondensed => 64;
        public int ColsExpanded => 24;

        #endregion

        private byte[] _buffer;

        string _codePage;

        public byte[] PrintContent { get => _buffer; }

        public EscPos(string codePage)
        {
            FontModeCmd = new FontMode();
            FontWidthCmd = new FontWidth();
            AlignmentCmd = new Alignment();
            PaperCutCmd = new PaperCut();
            DrawerCmd = new Drawer();
            QrCodeCmd = new QrCode();
            BarCodeCmd = new BarCode();
            ImageCmd = new Image();
            LineHeightCmd = new LineHeight();
            InitializePrintCmd = new InitializePrint();
            this._codePage = codePage;
        }



        public byte[] Separator(char speratorChar= '-')
        {
            return FontModeCmd.Condensed(EnumPrinterModeState.On)
                .AddBytes(new string(speratorChar, ColsNomal))
                .AddBytes(FontModeCmd.Condensed(EnumPrinterModeState.Off))
                .AddCrLF();
        }

        public byte[] Separator(int count ,char speratorChar = '-')
        {
            return FontModeCmd.Condensed(EnumPrinterModeState.On)
                .AddBytes(new string(speratorChar, count))
                .AddBytes(FontModeCmd.Condensed(EnumPrinterModeState.Off))
                .AddCrLF();
        }

        public byte[] AutoTest()
        {
            return new byte[] { 29, 40, 65, 2, 0, 0, 2 };
        }

        public void Append(string value)
        {
            AppendString(value, true);
        }

        public void Append(byte[] value)
        {
            if (value == null)
                return;
            var list = new List<byte>();
            if (_buffer != null)
                list.AddRange(_buffer);
            list.AddRange(value);
            _buffer = list.ToArray();
        }

        public void AppendWithoutLf(string value)
        {
            AppendString(value, false);
        }

        private void AppendString(string value, bool useLf)
        {
            if (string.IsNullOrEmpty(value))
                return;
            if (useLf)
                value += "\n";
            var list = new List<byte>();
            if (_buffer != null)
                list.AddRange(_buffer);
            var bytes = Encoding.GetEncoding(_codePage).GetBytes(value);
            list.AddRange(bytes);
            _buffer = list.ToArray();
        }

        /// <summary>
        /// 换行
        /// </summary>
        public void NewLine()
        {
            Append("\r");
        }

        /// <summary>
        /// 换N行
        /// </summary>
        /// <param name="lines"></param>
        public void NewLines(int lines)
        {
            for (int i = 1, loopTo = lines - 1; i <= loopTo; i++)
                NewLine();
        }

        /// <summary>
        /// 清空指令内容
        /// </summary>
        public void Clear()
        {
            _buffer = null;
        }

        public void TestPrinter()
        {
            //Image(new Bitmap(@"D:\logo.png"));
            //AlignCenter();
            //Ean13("1234567890123",Positions.BelowBarcode);
            ////QrCode("12345678");
            //Append("NORMAL - 48 COLUMNS");
            //Append("1...5...10...15...20...25...30...35...40...45.48");
            //Separator();
            //Append("Text Normal");
            //BoldMode("Bold Text");
            //UnderlineMode("Underlined text");
            //Separator();
            //ExpandedMode(PrinterModeState.On);
            //Append("Expanded - 23 COLUMNS");
            //Append("1...5...10...15...20..23");
            //ExpandedMode(PrinterModeState.Off);
            //Separator();
            //CondensedMode(PrinterModeState.On);
            //Append("Condensed - 64 COLUMNS");
            //Append("1...5...10...15...20...25...30...35...40...45...50...55...60..64");
            //CondensedMode(PrinterModeState.Off);
            //Separator();
            //DoubleWidth2();
            //Append("Font Width 2");
            //DoubleWidth3();
            //Append("Font Width 3");
            //NormalWidth();
            //Append("Normal width");
            //Separator();
            //AlignRight();
            //Append("Right aligned text");
            //AlignCenter();
            //Append("Center-aligned text");
            //AlignLeft();
            //Append("Left aligned text");
            //Separator();
            //Font("Font A 珺", Fonts.FontA);
            //Font("Font B 珺", Fonts.FontB);
            //Font("Font C 珺", Fonts.FontC);
            //Font("Font D 珺", Fonts.FontD);
            //Font("Font E 珺", Fonts.FontE);
            //Font("Font Special A", Fonts.SpecialFontA);
            //Font("Font Special B", Fonts.SpecialFontB);
            //Separator();
            //InitializePrint();
            ////new EscPos().table();
            //Append("商品\t单价\t数量\t总价");
            //Append("111111\t123.00\t15.8\t15800.00\n");

            ////SetLineHeight(24);
            ////Append("This is first line with line height of 30 dots");
            ////SetLineHeight(80);
            ////Append("This is second line with line height of 24 dots");
            ////Append("This is third line with line height of 40 dots");
            ////NewLines(3);
            ////Append("End of Test :)");
            //Separator();
        }

        /// <summary>
        /// 加粗模式打印文字
        /// </summary>
        /// <param name="value"></param>
        public void BoldMode(string value)
        {
            Append(FontModeCmd.Bold(value));
        }

        /// <summary>
        /// 加粗模式开关
        /// </summary>
        /// <param name="state"></param>
        public void BoldMode(EnumPrinterModeState state)
        {
            Append(FontModeCmd.Bold(state));
        }

        /// <summary>
        /// 设置字体打印
        /// </summary>
        /// <param name="value"></param>
        /// <param name="state"></param>
        public void Font(string value, EnumFonts state)
        {
            Append(FontModeCmd.Font(value, state));
        }

        /// <summary>
        /// 下划线文本
        /// </summary>
        /// <param name="value"></param>
        public void UnderlineMode(string value)
        {
            Append(FontModeCmd.Underline(value));
        }

        /// <summary>
        /// 下划线模式开关
        /// </summary>
        /// <param name="state"></param>
        public void UnderlineMode(EnumPrinterModeState state)
        {
            Append(FontModeCmd.Underline(state));
        }

        /// <summary>
        /// 扩大模式打印文本
        /// </summary>
        /// <param name="value"></param>
        public void ExpandedMode(string value)
        {
            Append(FontModeCmd.Expanded(value));
        }

        /// <summary>
        /// 扩大模式开关
        /// </summary>
        /// <param name="state"></param>
        public void ExpandedMode(EnumPrinterModeState state)
        {
            Append(FontModeCmd.Expanded(state));
        }

        /// <summary>
        /// 浓缩模式打印文本
        /// </summary>
        /// <param name="value"></param>
        public void CondensedMode(string value)
        {
            Append(FontModeCmd.Condensed(value));
        }

        /// <summary>
        /// 设置浓缩模式开关
        /// </summary>
        /// <param name="state"></param>
        public void CondensedMode(EnumPrinterModeState state)
        {
            Append(FontModeCmd.Condensed(state));
        }

        public void NormalWidth()
        {
            Append(FontWidthCmd.Normal());
        }

        public void DoubleWidth2()
        {
            Append(FontWidthCmd.DoubleWidth2());
        }

        public void DoubleWidth3()
        {
            Append(FontWidthCmd.DoubleWidth3());
        }

        /// <summary>
        /// 设置向左浮动
        /// </summary>
        public void AlignLeft()
        {
            Append(AlignmentCmd.Left());
        }

        /// <summary>
        /// 设置向右浮动
        /// </summary>
        public void AlignRight()
        {
            Append(AlignmentCmd.Right());
        }

        /// <summary>
        /// 设置居中
        /// </summary>
        public void AlignCenter()
        {
            Append(AlignmentCmd.Center());
        }

        public void FullPaperCut()
        {
            Append(PaperCutCmd.Full());
        }

        public void PartialPaperCut()
        {
            Append(PaperCutCmd.Partial());
        }

        public void OpenDrawer()
        {
            Append(DrawerCmd.Open());
        }

        /// <summary>
        /// 打印二维码
        /// </summary>
        /// <param name="qrData"></param>
        public void QrCode(string qrData)
        {
            Append(QrCodeCmd.Print(qrData));
        }

        /// <summary>
        /// 打印二维码
        /// </summary>
        /// <param name="qrData"></param>
        /// <param name="qrCodeSize"></param>
        public void QrCode(string qrData, EnumQrCodeSize qrCodeSize)
        {
            Append(QrCodeCmd.Print(qrData, qrCodeSize));
        }

        /// <summary>
        /// 打印CODE128格式的条码
        /// </summary>
        /// <param name="code"></param>
        /// <param name="printString"></param>
        public void Code128(string code, EnumPositions printString = EnumPositions.NotPrint)
        {
            Append(BarCodeCmd.Code128(code, printString));
        }

        /// <summary>
        /// 打印Code39格式的条码
        /// </summary>
        /// <param name="code"></param>
        /// <param name="printString"></param>
        public void Code39(string code, EnumPositions printString = EnumPositions.NotPrint)
        {
            Append(BarCodeCmd.Code39(code, printString));
        }

        /// <summary>
        /// 打印Ean13格式的条码
        /// </summary>
        /// <param name="code"></param>
        /// <param name="printString"></param>
        public void Ean13(string code, EnumPositions printString = EnumPositions.NotPrint)
        {
            Append(BarCodeCmd.Ean13(code, printString));
        }

        /// <summary>
        /// 打印图片
        /// </summary>
        /// <param name="image"></param>
        public void Image(Bitmap image)
        {
            Append(ImageCmd.Print(image));
        }
        
        public void NormalLineHeight()
        {
            Append(LineHeightCmd.Normal());
        }

        public void SetLineHeight(byte height)
        {
            Append(LineHeightCmd.SetLineHeight(height));
        }

    }
}

