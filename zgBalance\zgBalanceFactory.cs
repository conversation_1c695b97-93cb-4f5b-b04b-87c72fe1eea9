﻿using CefSharp;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using zgBalanceCommon.Balance;

namespace zgBalance
{
    public class zgBalanceFactory
    {
        public void CreateBalanceInstance(string scaleBrandCode, string scaleTypeCode, string ip,int port, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null)
        {
            var key = scaleBrandCode.ToLower() + "@" + scaleTypeCode.ToLower();
            if ("dahua@tm15".Equals(key))
            {
                var result = new zgBalance.DH.TM.BalanceDHTMF(ip, Convert.ToInt32(port));
                onsuccess.ExecuteAsync(result);
            }
            else {
                onfail.ExecuteAsync("not exists Balance");
            }
        }
    }
}
