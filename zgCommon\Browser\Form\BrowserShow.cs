﻿using CefSharp;
using Newtonsoft.Json;
using System;
using System.Windows.Forms;
using zgBalance.DH.TM;
using zgCommon.Browser;
using zgLogging;
using zgpos.Browser;
using zgpos.EventHandlers;
using zgUtils;
using zgUtils.Model;

namespace zgpos
{
    public class BrowserShow : BrowserBase
    {
        static string address = (string.IsNullOrEmpty(App.MainPageUrl)?CommonApp.ConfigBase.MainPageUrl:App.MainPageUrl) + @"/index.html?" + Guid.NewGuid().ToString("N");
        public BrowserShow(Form form):base(form, address)
        {
            Log.Info("BrowserShow(Form form):base(form, address)：" + address);
            try
            {
                browser.ConsoleMessage += ChromiumWebBrowserMessageHandler.DoConsoleMessage;
                browser.JavascriptMessageReceived += ChromiumWebBrowserMessageHandler.DoJavascriptMessage;
                browser.LoadingStateChanged += ChromiumWebBrowserLoadHandler.DoLoadingStateChanged;
                browser.LoadError += ChromiumWebBrowserLoadHandler.DoLoadError;
                browser.FrameLoadStart += ChromiumWebBrowserLoadHandler.DoFrameLoadStart;
                browser.FrameLoadEnd += ChromiumWebBrowserLoadHandler.DoFrameLoadEnd;
                // 向前端暴露 C# 函数
                Log.Info("Cefsharp  new Notify() start");
                Notify notify = new Notify();
                Log.Info("Cefsharp  new Notify() ");
                browser.JavascriptObjectRepository.Register("external", notify, isAsync: false, options: BindingOptions.DefaultBinder);
                Log.Info("Cefsharp Register new Notify() end");
                browser.JavascriptObjectRepository.Register("sqlitePlugin", new SQLitePlugin(), isAsync: true, options: BindingOptions.DefaultBinder);
                browser.JavascriptObjectRepository.Register("zgPrinter", new ZgPrinterBrowserFun(), isAsync: true, options: BindingOptions.DefaultBinder);
                browser.JavascriptObjectRepository.Register("zgDHTMFBalance", new BalanceDHTMF(), isAsync: true, options: BindingOptions.DefaultBinder);
                browser.JavascriptObjectRepository.Register("actionLog", new ActionLog(), isAsync: true, options: BindingOptions.DefaultBinder);
                browser.JavascriptObjectRepository.Register("csharpHttps", new CsharpHttps(), isAsync: true, options: BindingOptions.DefaultBinder);
                browser.DownloadHandler = new DownloadHandler();// new MyDownLoadFile();   //添加下载文件支持

            }
            catch (Exception ex) {
                Log.Error("BrowserShow(Form form):base(form, address)：" + ex.Message);
            }
            
        }
        internal void ReloadBrowser()
        {
            browser?.Reload();
        }
    }
    public class BrowserMainShow : BrowserBase
    {
        static string address = (string.IsNullOrEmpty(App.MainPageUrl) ? CommonApp.ConfigBase.MainPageUrl : App.MainPageUrl) + @"/index.html?" + Guid.NewGuid().ToString("N");
        public BrowserMainShow(Form form) : base(form, address)
        {
            browser.ConsoleMessage += ChromiumWebBrowserMessageHandler.DoConsoleMessage;
            browser.JavascriptMessageReceived += ChromiumWebBrowserMessageHandler.DoJavascriptMessage;
            browser.LoadingStateChanged += ChromiumWebBrowserLoadHandler.DoLoadingStateChanged;
            browser.LoadError += ChromiumWebBrowserLoadHandler.DoLoadError;
            browser.FrameLoadStart += ChromiumWebBrowserLoadHandler.DoFrameLoadStart;
            browser.FrameLoadEnd += Browser_FrameLoadEnd;
            // 向前端暴露 C# 函数
            browser.JavascriptObjectRepository.Register("external", new Industry(), isAsync: false, options: BindingOptions.DefaultBinder);
            

        }

        private void Browser_FrameLoadEnd(object sender, FrameLoadEndEventArgs e)
        {
            //browser.ShowDevTools();
        }

        internal void ReloadBrowser()
        {
            browser?.Reload();
        }
    }
    public class Industry {
        /// <summary>
        /// 切换业态
        /// </summary>
        /// <param name="className">xxxx.dll</param>
        /// <param name="classUrl">https://xxxx/xxxx.dll</param>
        /// <param name="onsuccess"></param>
        public void switchIndustry(IndustryClass industry, IJavascriptCallback onsuccess = null)
        {
            App.SwitchIndustry(industry, onsuccess);
        }
        public void switchIndustry(string className, string classUrl, IJavascriptCallback onsuccess = null)
        {
            
            switchIndustry(className, classUrl, onsuccess);
        }
        public string getVersion()
        {
            string version = CommonApp.Version + (CommonApp.ISPRODCT ? "" : "(体验版)");

            return version;
        }
        public string getApiUrl(string type)
        {
            return zgUtils.Security.AESHelper.StringDecoding(zgzn.ConfigController.DefaultConfigURL);
        }
        public string getIndustries() {
            return JsonConvert.SerializeObject(CommonApp.Industries);
        }
    }
}
