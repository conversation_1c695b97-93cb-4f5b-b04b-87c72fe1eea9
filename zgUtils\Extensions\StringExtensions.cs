﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace zgUtils.Extensions
{
    public static class StringExtensions
    {
        public static string GetMd5Hash(this string tagert)
        {
            string result;
            using (MD5 md = MD5.Create())
            {
                byte[] array = md.ComputeHash(Encoding.UTF8.GetBytes(tagert));
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < array.Length; i++)
                {
                    stringBuilder.Append(array[i].ToString("x2"));
                }
                result = stringBuilder.ToString();
            }
            return result;
        }

        public static string UrlEncode(this string tagert)
        {
            StringBuilder stringBuilder = new StringBuilder();
            byte[] bytes = Encoding.UTF8.GetBytes(tagert);
            for (int i = 0; i < bytes.Length; i++)
            {
                stringBuilder.Append("%" + Convert.ToString(bytes[i], 16));
            }
            return stringBuilder.ToString();
        }
    }
}
