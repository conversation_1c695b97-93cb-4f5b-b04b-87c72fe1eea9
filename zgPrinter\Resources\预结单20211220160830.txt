[{
	"index": 9,
	"contenttype": 1,
	"textalign": 32,
	"contentfont": "\"宋体, 12pt, style=Bold\"",
	"content": "预结单",
	"datamember": "name",
	"format": "{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
},{
	"index": 10,
	"contenttype": 1,
	"textalign": 32,
	"contentfont": "\"宋体, 12pt\"",
	"content": "我的店铺",
	"datamember": "storename",
	"format": "{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 20,
	"contenttype": 5,
	"textalign": 0,
	"content": 1
}, {
	"index": 30,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt\"",
	"content": "桌号",
	"datamember": "桌号",
	"format": "桌号：{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 31,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt\"",
	"content": "流水号",
	"datamember": "流水号",
	"format": "流水号：{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 40,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt\"",
	"content": "人数",
	"datamember": "人数",
	"format": "人数：{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 41,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt\"",
	"content": "会员",
	"datamember": "会员",
	"format": "会员：{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 50,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt\"",
	"content": "订单号",
	"datamember": "订单号",
	"format": "订单号：{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 70,
	"contenttype": 5,
	"textalign": 0,
	"content": 0
}, {
	"index": 80,
	"contenttype": 3,
	"headtextalign": 0,
	"bodytextalign": 0,
	"datamember": "goods",
	"printhead": true,
	"printseparatorline": true,
	"headfont": "\"宋体, 9pt\"",
	"bodyfont": "\"宋体, 9pt\"",
	"config": "[{\"col_head\":\"菜品/数量\",\"col_dataMember\":\"name\",\"col_singleLine\":\"true\",\"col_isPrint\":\"true\",\"col_align\":null,\"col_width\":\"40\"},{\"col_head\":\"菜品/数量\",\"col_dataMember\":\"num\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\",\"col_align\":null,\"col_width\":\"45\"},{\"col_head\":\"金额\",\"col_dataMember\":\"price\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\",\"col_align\":null,\"col_width\":\"25\"},{\"col_head\":\"优惠价\",\"col_dataMember\":\"total\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\",\"col_align\":null,\"col_width\":\"25\"}]",
	"displayname": "商品信息"
}, {
	"index": 90,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt, style=Bold\"",
	"content": "加菜标题",
	"datamember": "加菜标题",
	"format": "{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 100,
	"contenttype": 3,
	"headtextalign": 0,
	"bodytextalign": 0,
	"datamember": "addgoods",
	"printhead": false,
	"printseparatorline": false,
	"headfont": "\"宋体, 9pt\"",
	"bodyfont": "\"宋体, 9pt\"",
	"config": "[{\"col_head\":\"销售单号\",\"col_dataMember\":\"saleno\",\"col_singleLine\":\"true\",\"col_isPrint\":\"true\",\"col_align\":null,\"col_width\":\"50\"},{\"col_head\":\"菜品/数量\",\"col_dataMember\":\"name\",\"col_singleLine\":\"true\",\"col_isPrint\":\"true\",\"col_align\":null,\"col_width\":\"50\"},{\"col_head\":\"菜品/数量\",\"col_dataMember\":\"num\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\",\"col_align\":null,\"col_width\":\"40\"},{\"col_head\":\"金额\",\"col_dataMember\":\"price\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\",\"col_align\":null,\"col_width\":\"25\"},{\"col_head\":\"优惠价\",\"col_dataMember\":\"total\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\",\"col_align\":null,\"col_width\":\"25\"}]",
	"displayname": "商品信息"
}, {
	"index": 110,
	"contenttype": 5,
	"textalign": 0,
	"content": 0
}, {
	"index": 111,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt\"",
	"content": "退菜",
	"datamember": "退菜",
	"format": "已退菜品：{0}",
	"defaultcontent": "Default",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 112,
	"contenttype": 3,
	"headtextalign": 0,
	"bodytextalign": 0,
	"datamember": "retreatfood",
	"printhead": false,
	"printseparatorline": false,
	"headfont": "\"宋体, 9pt\"",
	"bodyfont": "\"宋体, 9pt\"",
	"config": "[{\"col_head\":\"菜品\",\"col_dataMember\":\"name\",\"col_singleLine\":\"true\",\"col_isPrint\":\"true\",\"col_align\":null,\"col_width\":\"40\"},{\"col_head\":\"菜品/数量\",\"col_dataMember\":\"num\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\",\"col_align\":null,\"col_width\":\"35\"},{\"col_head\":\"金额\",\"col_dataMember\":\"price\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\",\"col_align\":null,\"col_width\":\"30\"},{\"col_head\":\"优惠价\",\"col_dataMember\":\"total\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\",\"col_align\":null,\"col_width\":\"30\"}]",
	"displayname": "商品信息"
}, {
	"index": 113,
	"contenttype": 5,
	"textalign": 0,
	"content": 0,
	"displaygroup": "retreatfood",
}, {
	"index": 114,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt\"",
	"content": "整单备注",
	"datamember": "整单备注",
	"format": "整单备注：{0}",
	"defaultcontent": "Default",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 115,
	"contenttype": 1,
	"textalign": 64,
	"contentfont": "\"宋体, 9pt\"",
	"content": "数量合计",
	"datamember": "数量合计",
	"format": "数量合计：{0}",
	"defaultcontent": "Default",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 120,
	"contenttype": 1,
	"textalign": 64,
	"contentfont": "\"宋体, 9pt\"",
	"content": "金额合计",
	"datamember": "金额合计",
	"format": "金额合计：{0}",
	"defaultcontent": "Default",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 130,
	"contenttype": 5,
	"textalign": 0,
	"content": 0
}, {
	"index": 140,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt\"",
	"content": "整单折扣",
	"datamember": "整单折扣",
	"format": "整单折扣：{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 140,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt\"",
	"content": "菜品优惠",
	"datamember": "菜品优惠",
	"format": "菜品优惠：{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 135,
	"contenttype": 5,
	"textalign": 0,
	"content": 1
},{
	"index": 141,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt\"",
	"content": "会员名",
	"datamember": "会员名",
	"format": "会员名：{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 142,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt\"",
	"content": "会员手机号",
	"datamember": "会员手机号",
	"format": "会员手机号：{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 143,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt\"",
	"content": "余额",
	"datamember": "余额",
	"format": "余额：{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 144,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt\"",
	"content": "积分",
	"datamember": "积分",
	"format": "积分：{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
},  {
	"index": 160,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt\"",
	"content": "抹零",
	"datamember": "抹零",
	"format": "抹零：{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 170,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt\"",
	"content": "减免",
	"datamember": "减免",
	"format": "减免：{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 180,
	"contenttype": 1,
	"textalign": 64,
	"contentfont": "\"宋体, 12pt, style=Bold\"",
	"content": "应付",
	"datamember": "应付",
	"format": "应付：{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 190,
	"contenttype": 5,
	"textalign": 0,
	"content": 0
}, {
	"index": 200,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"宋体, 9pt\"",
	"content": "打印时间",
	"datamember": "打印时间",
	"format": "打印时间：{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}]