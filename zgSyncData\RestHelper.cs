﻿using RestSharp;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using zgSyncData.Model;

namespace zgSyncData
{
    public static class RestHelper
    {
        public static RestRequest SetContentTypeJson(this RestRequest request)
        {
            request.AddHeader("Content-Type", "application/json");
            return request;
        }

        public static RestRequest SetAuthorization(this RestRequest request,string authorization)
        {
            request.AddHeader("Authorization", authorization);
            return request;
        }

        public static RestRequest SetHeaderValue(this RestRequest request, string name, string value)
        {
            request.AddHeader(name, value);
            return request;
        }

        public static void SetHeaderValue(this WebHeaderCollection header, string name, string value)
        {
            var property = typeof(WebHeaderCollection).GetProperty("InnerCollection",
                System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
            if (property != null)
            {
                var collection = property.GetValue(header, null) as NameValueCollection;
                collection[name] = value;
            }
        }

        public static IRestResponse<T> RequestPost<T>(this RestClient client,string url, string authorization, object parm) where T : ResponseData
        {
            var request = new RestRequest(url, Method.POST);
            request.SetAuthorization(authorization).SetContentTypeJson();
            request.AddJsonBody(parm);
            var result = client.Execute<T>(request);
            return result;
        }

        public async static Task<IRestResponse<T>> RequestAsyncPost<T>(this RestClient client, string url, string authorization, object parm) where T : ResponseData
        {
            var request = new RestRequest(url, Method.POST);
            request.SetAuthorization(authorization).SetContentTypeJson();
            request.AddJsonBody(parm);
            var result = await client.ExecuteAsync<T>(request);
            return result;
        }
    }
}
