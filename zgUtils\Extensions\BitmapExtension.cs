﻿using System;
using System.Drawing;
using System.IO;

namespace zgUtils.Extensions
{
    public static class BitmapExtension
    {
        public static string ToBase64(this Bitmap bitmap)
        {
            using (var stream = new MemoryStream())
            {
                bitmap.Save(stream, bitmap.RawFormat);
                byte[] bytes = new byte[stream.Length];
                stream.Position = 0;
                stream.Read(bytes, 0, (int)stream.Length);
                stream.Close();
                return Convert.ToBase64String(bytes);
            }
        }
        /// <summary>
        /// 文件转base64
        /// </summary>
        /// <returns>base64字符串</returns>
        public static string FileToBase64String(string filepath)
        {
            FileStream fsForRead = new FileStream(filepath, FileMode.Open);//文件路径
            string base64Str = "";
            try
            {
                //读写指针移到距开头10个字节处
                fsForRead.Seek(0, SeekOrigin.Begin);
                byte[] bs = new byte[fsForRead.Length];
                int log = Convert.ToInt32(fsForRead.Length);
                //从文件中读取10个字节放到数组bs中
                fsForRead.Read(bs, 0, log);
                base64Str = Convert.ToBase64String(bs);
                return "data:image / png; base64,"+base64Str;
            }
            catch (Exception ex)
            {
                Console.Write(ex.Message);
                Console.ReadLine();
                return base64Str;
            }
            finally
            {
                fsForRead.Close();
            }
        }
        public static Bitmap Base64StringToImage(string strbase64)
        {
            try
            {

                byte[] arr = Convert.FromBase64String(strbase64);
                MemoryStream ms = new MemoryStream(arr);
                Bitmap bmp = new Bitmap(ms);
                ms.Close();
                ms.Dispose();
                return bmp;
            }
            catch
            {
                return null;
            }
        }
    }
}
