﻿using CefSharp;
using CefSharp.DevTools.IndexedDB;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PrintCore;
using System;
using System.Collections.Generic;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using zgLogging;
using zgPrinter;
using zgPrinter.Model;
using zgPrinter.Model.ZgLabel;
using zgPrinter.Printer.DrawImg;
using zgPrinter.Printer.ESCPOS;
using zgPrinter.Printer.TSPL;
using zgPrinter.Printer.Usb;
using zgUtils;

namespace zgpos.Browser
{
    public class ZgPrinterBrowserFun
    {
        private readonly UsbPrinter _usbPrinter = new UsbPrinter();

        private static ZGPrintTool _printerTool = new ZGPrintTool();

        private static string logoImgPath { get => Path.Combine(CommonApp.Directory, CommonApp.Config.Base.LogoDir); }

        public static ZGPrintTool printerTool { get => _printerTool; }


        public void Print(object jsonText, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null)
        {
            ZGPrintTool tool = new ZGPrintTool();
            try
            {
                Log.WriterNormalLog($"Print enter：data:{JsonConvert.SerializeObject(jsonText)}");
                var zgData = JObject.FromObject(jsonText);
                if (zgData["logo"] != null && !string.IsNullOrEmpty(zgData["logo"].ToString()))
                {
                    var logoPath = zgData["logo"].ToString();
                    var fileFullName = logoPath.Substring(logoPath.LastIndexOf('/') + 1);
                    zgData["logo"] = logoImgPath + "\\" + fileFullName;
                }
                if (zgData["qrcode"] != null && !string.IsNullOrEmpty(zgData["qrcode"].ToString()))
                {
                    var qrcodePath = zgData["qrcode"].ToString();
                    var fileFullName = qrcodePath.Substring(qrcodePath.LastIndexOf('/') + 1);
                    zgData["qrcode"] = logoImgPath + "\\" + fileFullName;
                }
                var printNum = zgData.Value<int>("printNum");
                var printMode = zgData.Value<int>("printMode");
                if (printMode < 1000)
                {
                    tool.PrintReceipt(zgData, printNum);
                }
                else
                {
                    var templateName = zgData.Value<string>("template");
                    EnumReceiptType enumReceiptType = (EnumReceiptType)printMode;
                    var printerName = zgData.Value<string>("printername");
                    if (enumReceiptType == EnumReceiptType.CommonGraphics)
                    {
                        tool.PrintCommonReceiptGraphics(zgData, templateName, printerName, printNum);
                    }
                    if (enumReceiptType == EnumReceiptType.CommonESCPOS)
                    {
                        tool.PrintCommonReceiptESCPOS(zgData, templateName, printerName, printNum);
                    }
                }

                if (onsuccess != null)
                {
                    onsuccess.ExecuteAsync("PrintPOS success");
                }
            }
            catch (Exception e)
            {
                Log.WriterExceptionLog($"Print error：{e.Message},data:{JsonConvert.SerializeObject(jsonText)}");
                if (onfail != null)
                {
                    onfail.ExecuteAsync(e.Message);
                }
            }
        }

        /// <summary>
        /// 获取USB打印机清单
        /// </summary>
        /// <returns></returns>
        public List<KeyValueItem> GetUsbPrinterList()
        {
            var usbprinterList = _usbPrinter.GetUsbPrintList();
            return usbprinterList;
        }

        public void PrintBatch(object configText, object jsonText, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null)
        {
            var propertyLogo = "logo";
            var propertyQrCode = "qrcode";
            try
            {
                Log.WriterNormalLog($"PrintBatch enter：config: {JsonConvert.SerializeObject(configText)},data:{JsonConvert.SerializeObject(jsonText)}");
                var printConfig = JObject.FromObject(configText).ToObject<PrintConfig>();
                var printDataArray = JArray.FromObject(jsonText);
                foreach (var item in printDataArray)
                {
                    var printData = JObject.FromObject(item);
                    var logo = printData.Value<string>(propertyLogo);
                    var qrcode = printData.Value<string>(propertyQrCode);
                    if (!string.IsNullOrEmpty(logo))
                    {
                        var fileFullName = logo.Substring(logo.LastIndexOf('/') + 1);
                        item[propertyLogo] = Path.Combine(logoImgPath, fileFullName);
                    }
                    if (!string.IsNullOrEmpty(qrcode))
                    {
                        var fileFullName = qrcode.Substring(qrcode.LastIndexOf('/') + 1);
                        item[propertyQrCode] = Path.Combine(logoImgPath, fileFullName);
                    }
                }
                printerTool.PrintCommonReceiptESCPOS(printConfig, printDataArray);
                onsuccess?.ExecuteAsync("PrintPOS success");
            }
            catch (Exception e)
            {
                Log.WriterExceptionLog($"PrintBatch error：{e.Message},data:{JsonConvert.SerializeObject(jsonText)}");
               onfail?.ExecuteAsync(e.Message);
            }
        }

        public void PrintLabel40x30(string printerName, string jsonText, int printCount = 1, int startX = 40, IJavascriptCallback onSuccessCallback = null, IJavascriptCallback onFailedCallback = null)
        {
            try
            {
                var tspl = new TsplPrinter();
                var homeBytes = tspl.Home().Finish();
                _printerTool.PrintLabel(homeBytes, printerName);
                tspl.Clear();

                var dataItems = JsonConvert.DeserializeObject<List<PrintLabelItem>>(jsonText.ToString());
                foreach (var item in dataItems)
                {
                    tspl = tspl.SetSize("40mm", "30mm").SetGap().SetDirection(1).SetReference(0, 0).CLS();
                    if (!string.IsNullOrEmpty(item.name))
                    {
                        tspl.PrintText(startX, 20, item.name);
                    }
                    if (!string.IsNullOrEmpty(item.sale_price))
                    {
                        tspl.PrintText(startX, 70, $"商品售价:￥{item.sale_price}");
                    }
                    if (!string.IsNullOrEmpty(item.code))
                    {
                        tspl.PrintBarCode(startX, 100, 100, "128", item.code);
                    }
                    var bytes = tspl.Finish(printCount);
                    _printerTool.PrintLabel(bytes, printerName);
                    tspl.Clear();
                }

                onSuccessCallback?.ExecuteAsync("success");
            }
            catch (Exception ex)
            {
                onFailedCallback?.ExecuteAsync(ex.Message);
                throw;
            }
        }

        public void PrintLabelSeq(object jsonText, IJavascriptCallback onSuccessCallback = null, IJavascriptCallback onFailedCallback = null) {
            try
            {
                var jsonStr = JsonConvert.SerializeObject(jsonText);
#if DEBUG
                //var xmlDoc = XDocument.Load(@"C:\WorkSpace\新建文本文档.txt");
                //_printerTool.Convert2ZgLabel(xmlDoc);
                //return;

                ZgLabel l = new ZgLabel();
                l.Width = 40;
                l.Height = 30;
                l.Rotation = 0;
                l.Items = new List<ZgLabelItem>() {
                    //new ZgLabelItem(){ Text="မင်္ဂလာပါ" }
                    new ZgLabelItem(){ Text="商品名称：123456789" },
                    new ZgLabelItem(){ Text="商品条码：123456789" },
                    new ZgLabelItem(){ Text="123456789",Type = "barcode" },
                };
                l.PrinterName = "Xprinter XP-235B (来自 EYTP-10204054)";
                //jsonStr = "{\"Rotation\":0,\"Width\":40,\"Height\":30,\"PrintCount\":1,\"PrinterName\":\"Xprinter XP-235B (来自 EYTP-10204054)\",\"Items\":[{\"Text\":\"商品名称：123456789\"},{\"Text\":\"商品条码：123456789\"},{\"Text\":\"123456789\",\"Type\":\"barcode\"}]}";
#endif
                Log.WriterNormalLog(jsonStr);
                var printdata = JsonConvert.DeserializeObject<ZgLabel>(jsonStr);
                if (printdata.Rotation == 90) {
                    _printerTool.PrintLabelsSequentiallyVertical(printdata);
                }
                if (printdata.Rotation == 0)
                {
                    _printerTool.PrintLabelsSequentially(printdata);
                }

                onSuccessCallback?.ExecuteAsync("success");
            }
            catch (Exception ex)
            {
                onFailedCallback?.ExecuteAsync(ex.Message);
                throw;
            }
           
        }

    }
}
