﻿using CefSharp;
using System;
using System.Windows.Forms;
using zgLogging;
using zgpos.Browser;
using zgUtils;
using zgUtils.Controls;
namespace zgpos
{

    public partial class FormShow : Form
    {
        protected CommonApp crBrowser;
        // public FormBack formBack = null;
        public Form parentform = null;

        public FormShow(Form parentform)
        {
            CheckForIllegalCrossThreadCalls = false;
            this.parentform = parentform;
            this.InitializeComponent();
            this.Icon = zgCommon.Properties.Resources.Icon;
            this.Dock = DockStyle.Fill;
            App.assembly = System.Reflection.Assembly.GetExecutingAssembly();
        }
       
        private void ZgPosForm_Shown(object sender, System.EventArgs e)
        {
            
            this.parentform.Hide();

        }
       
        /*
         * 加载
         * **/
        private void FormShow_Load(object sender, EventArgs e)
        {
            this.InvokeOnUiThreadIfRequired(() => this.Controls.Add(App.initMainBrowser(this)));           
        }

        
        private void FormShow_FormClosing(object sender, FormClosingEventArgs e)
        {
            Log.WriterNormalLog("FormShow_FormClosing.................................."+ ControlBox.ToString());
            try
            {
                parentform.Visible = true;
                //Configs.AnimateWindow(Handle, 1000, Configs.AW_SLIDE | Configs.AW_HIDE | Configs.AW_BLEND);
            }
            catch (Exception ex) {
                Log.WriterNormalLog("FormShow_FormClosing:"+ex.Message);
            }
        }

        private void FormShow_FormClosed(object sender, FormClosedEventArgs e)
        {
            Log.WriterNormalLog("FormShow_FormClosed..................................");
        }

        private void FormShow_LocationChanged(object sender, EventArgs e)
        {
            Log.WriterNormalLog("FormShow_LocationChanged");
        }

    }
}
