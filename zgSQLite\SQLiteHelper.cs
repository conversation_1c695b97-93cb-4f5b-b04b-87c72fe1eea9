﻿using System;
using System.Collections.Generic;
using System.Data;
using Newtonsoft.Json.Linq;
using System.IO;
using System.Data.SQLite;
using zgLogging;

namespace zgUtils.Controls
{
    public class SQLiteHelper
    {
        private static string databaseName { get; set; }

        public static string directory { get; set; }
        public List<string> ParameterList { get; set; }
        public static string FullPath { get { return directory + databaseName + ".db"; } }
        public static string SqlitePath { get { return directory + databaseName + ".sqlite"; } }
        public static string ConnectionString { get { return "Data source=" + FullPath + ";Version=3;"; } }
        public static string BaseConnectionString { get { return "Data source=" + SqlitePath + ";Version=3;"; } }
        public static string sql_setting = @"select key,value,remark from settings";
        public static List<string> sqllist = new List<string>() {
            sql_setting,
            @"select * from clerks",
            @"select * from storeinfo;"
        };

        public static SQLiteConnection readConnection = null;
        public static SQLiteConnection syncReadConnection = null;
        public static SQLiteConnection writeConnection = null;

        public static void InitReadConnection() 
        {
            if (readConnection == null)
            {
                readConnection = new SQLiteConnection(ConnectionString);
            }
            if (readConnection.State == ConnectionState.Closed)
            {
                readConnection.Open();
            }
        }
        public static void InitSyncReadConnection()
        {
            if (syncReadConnection == null)
            {
                syncReadConnection = new SQLiteConnection(ConnectionString);
            }
            if (syncReadConnection.State == ConnectionState.Closed)
            {
                syncReadConnection.Open();
            }
        }
        public static void InitWriteConnection()
        {
            if (writeConnection == null)
            {
                writeConnection = new SQLiteConnection(ConnectionString);
            }
            if (writeConnection.State == ConnectionState.Closed)
            {
                writeConnection.Open();
            }
        }

        public static void Exit()
        {
            ConnectionClose(readConnection);
            ConnectionClose(syncReadConnection);
            ConnectionClose(writeConnection);
            readConnection = null;
            syncReadConnection = null;
            writeConnection = null;
        }

        static void ConnectionClose(SQLiteConnection connection)
        {
            if (connection != null)
            {
                try
                {
                    connection.Close();
                }
                catch (SQLiteException ex)
                {
                    Log.WriterExceptionLog("SQLiteConnection Close failed: " + ex.ToString());
                }
                finally
                {
                    try
                    {
                        connection.Dispose();
                    }
                    catch (SQLiteException ex)
                    {
                        Log.WriterExceptionLog("SQLiteConnection Dispose failed: " + ex.ToString());
                    }
                }
            }
        }

        ///// <summary>
        ///// 创建一个数据库文件。如果存在同名数据库文件，则会覆盖。
        ///// </summary>
        ///// <param name="dbName">数据库文件名。为null或空串时不创建。</param>
        ///// <param name="password">（可选）数据库密码，默认为空。</param>
        ///// <exception cref="Exception"></exception>
        //public static void CreateDB(string dbName)
        //{
        //    if (!string.IsNullOrEmpty(dbName))
        //    {
        //        SQLiteConnection.CreateFile(dbName);
        //    }
        //}
        public static long FileSize()
        {
            return FileSize(FullPath);
        }
        //所给路径中所对应的文件大小
        public static long FileSize(string filePath)
        {
            try
            {
                if (!File.Exists(filePath)) return 0;
                FileInfo fileInfo = new FileInfo(filePath);
                return fileInfo.Length;
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
                return 0;
            }

        }

        /// <summary>
        /// 创建数据库
        /// 数据库不存在，新建数据库
        /// </summary>
        /// <param name="dbName"></param>
        /// <param name="password"></param>
        /// <param name="version"></param>
        public static void OpenDatabase(string Version,string dbFilePathName)
        {
            Log.WriterNormalLog("数据初始化......");
            directory = dbFilePathName.Substring(0, dbFilePathName.LastIndexOf("\\") + 1);
            databaseName = dbFilePathName.Substring(dbFilePathName.LastIndexOf("\\") + 1);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }           
        }
        //public static void OpenDatabase(string dbName, string password = "", int dbVersion = 3)
        //{
        //    if (FileSize(FullPath) == 0)
        //    {
        //        OpenDatabase();
        //    }
        //}
        public static DataSet GetInitData()
        {
            DataSet ds;
            try
            {
                Log.WriterNormalLog("初期数据取得......");
                ds = ExecuteDataSet(string.Join(";", sqllist.ToArray()));
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
                throw ex;
            }
            return ds;
        }
        /// <summary>
        /// 数据库升级
        /// </summary>
        /// <returns></returns>
        public static void UpdateData(string Version)
        {
            

        }
        /// <summary>
        /// 
        /// </summary>
        internal static void MoveUserDb(string sysuid)
        {
            if (FileSize(FullPath) > 0)
            {
                var sysUid = sysuid ?? "";
                string sourceFileName = FullPath;
                string destFileName = FullPath.Replace(".db", DateTime.Now.ToString("yyyyMMddHHmmssffff") + sysUid + ".bk");
                Exit();
                File.Move(sourceFileName, destFileName);
            }
        }


        /*
* 版本号对比
* true：this_version>pre_version
* false：this_version<=pre_version
* **/
        private static bool VerCompare(string this_version, string pre_version)
        {
            return new Version(this_version) > new Version(pre_version);
        }


        /// <summary>
        /// 初始化数据库
        /// </summary>
        /// <returns></returns>
        public static void InitData(string path, byte[] ResourcesData)
        {
            Log.WriterNormalLog("DB创建开始......" + path);
            Exit();
            FileStream fs = new FileStream(path, FileMode.OpenOrCreate);
            BinaryWriter binWriter = new BinaryWriter(fs);
            binWriter.Write(ResourcesData, 0, ResourcesData.Length);
            binWriter.Close();
            fs.Close();
        }
        public static void UpdateDataBase()
        {
            try
            {
                string preversion = "0";
                DataTable tb = ExecuteDataTable("select max(id) as id from main.upgradelist");
                if (tb != null && tb.Rows.Count > 0 && !string.IsNullOrEmpty(tb.Rows[0]["id"].ToString()))
                {
                    preversion = tb.Rows[0]["id"].ToString();
                }
                List<string> sqlList = new List<string>();
                using (SQLiteConnection baseConnection = new SQLiteConnection(BaseConnectionString)) 
                { 
                    baseConnection.Open();
                    DataTable dt = ExecuteDataTable("select * from main.upgradelist where id>'" + preversion + "'", baseConnection);
                    foreach (DataRow row in dt.Rows)
                    {
                        string sql = row["sql"].ToString();

                        try
                        {
                            ExecuteNonQuery(string.Format("insert into main.upgradelist(version,sql,sortno) values('{0}', '{1}', '{2}');", row["version"].ToString(), sql.Replace("'", "''"), row["sortno"].ToString()));
                            if (!string.IsNullOrEmpty(sql)) ExecuteNonQuery(sql);
                        }
                        catch (Exception ex) {

                            Log.WriterExceptionLog(ex.Message);
                        }
                        
                    }
                }
            }
            catch (Exception ex) {
                Log.WriterExceptionLog(ex.Message);
            }
            
        }

        public static int SetSettingByKey(string key, string value, string remark = "")
        {
            return ExecuteNonQuery(String.Format("replace into settings (key, value, remark) values ('{0}', '{1}', '{2}');", key, value, remark));
        }
        public static string GetSettingByKey(string key)
        {
            string result = "";
            if (FileSize() > 0)
            {
                DataTable dt = ExecuteDataTable(string.Format("select key,value,remark from settings where key='{0}';", key));
                if (dt != null && dt.Rows.Count > 0)
                {
                    result = dt.Rows[0]["value"].ToString();
                }
            }
            return result;
        }

        /// <summary> 
        /// 对SQLite数据库执行增删改操作，返回受影响的行数。 
        /// </summary> 
        /// <param name="sql">要执行的增删改的SQL语句。</param> 
        /// <param name="parameters">执行增删改语句所需要的参数，参数必须以它们在SQL语句中的顺序为准。</param> 
        /// <returns></returns> 
        /// <exception cref="Exception"></exception>
        public static int ExecuteNonQuery(string sql, SQLiteConnection connection = null)
        {
            if (connection == null)
            {
                InitWriteConnection();
                connection = writeConnection;
            }
            using (SQLiteCommand cmd = new SQLiteCommand(connection))
            {
                cmd.CommandText = sql;
                return cmd.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// 批量处理数据操作语句。
        /// </summary>
        /// <param name="list">SQL语句集合。</param>
        /// <exception cref="Exception"></exception>
        public static void ExecuteNonQueryBatch(List<string> sqlList, SQLiteConnection connection = null)
        {
            if (connection == null)
            {
                InitWriteConnection();
                connection = writeConnection;
            }
            using (SQLiteTransaction tran = connection.BeginTransaction())
            {
                using (SQLiteCommand cmd = new SQLiteCommand(connection))
                {
                    try
                    {
                        foreach (string sql in sqlList)
                        {
                            cmd.CommandText = sql;
                            cmd.ExecuteNonQuery();
                        }
                        tran.Commit();
                    }
                    catch (Exception ex)
                    {
                        Log.WriterExceptionLog(ex.Message+"::::"+ cmd.CommandText);
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// 批量处理数据操作语句。
        /// </summary>
        /// <param name="list">SQL语句集合。</param>
        /// <exception cref="Exception"></exception>
        public static void ExecuteNonQueryBatchPrepare(List<string> sqlList)
        {
            InitWriteConnection();
            using (SQLiteTransaction tran = writeConnection.BeginTransaction())
            {
                using (SQLiteCommand cmd = new SQLiteCommand(writeConnection))
                {
                    try
                    {
                        foreach (string sql in sqlList)
                        {
                            cmd.CommandText = sql;
                            cmd.Prepare();
                            cmd.ExecuteNonQuery();
                        }
                        tran.Commit();
                    }
                    catch (Exception ex)
                    {
                        Log.WriterExceptionLog(ex.Message);
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }


        /// <summary>
        /// 执行查询语句，并返回第一个结果。
        /// </summary>
        /// <param name="sql">查询语句。</param>
        /// <returns>查询结果。</returns>
        /// <exception cref="Exception"></exception>
        public static object ExecuteScalar(string sql, params SQLiteParameter[] parameters)
        {
            InitReadConnection();
            using (SQLiteCommand cmd = new SQLiteCommand(readConnection))
            {
                cmd.CommandText = sql;
                if (parameters.Length != 0)
                {
                    cmd.Parameters.AddRange(parameters);
                }
                return cmd.ExecuteScalar();
            }
        }

        /// <summary> 
        /// 执行一个查询语句，返回一个关联的SQLiteDataReader实例。 
        /// </summary> 
        /// <param name="sql">要执行的查询语句。</param> 
        /// <param name="parameters">执行SQL查询语句所需要的参数，参数必须以它们在SQL语句中的顺序为准。</param> 
        /// <returns></returns> 
        /// <exception cref="Exception"></exception>
        public static string ExecuteReader(string sql)
        {
            InitReadConnection();
            using (SQLiteCommand command = new SQLiteCommand(sql, readConnection))
            {
                using (SQLiteDataReader reader = command.ExecuteReader())
                {
                    JArray arrays = new JArray();
                    while (reader.Read())
                    {
                        int len = reader.FieldCount;
                        JObject row = new JObject();
                        for (int i = 0; i < len; i++)
                        {
                            if (!reader.IsDBNull(i))
                            {
                                row.Add(reader.GetName(i), reader.GetValue(i).ToString());
                            }
                            else
                            {
                                row.Add(reader.GetName(i), "");
                            }
                        }
                        arrays.Add(row);
                    }
                    return arrays.ToString().Replace("\r\n", "");
                }
            }
        }
        /// <summary> 
        /// 查询数据库中的所有数据类型信息。
        /// </summary> 
        /// <returns></returns> 
        /// <exception cref="Exception"></exception>
        public static DataTable GetSchema()
        {
            InitReadConnection();
            return readConnection.GetSchema("TABLES");
        }

        public static void ClearData(string directory)
        {
            Exit();
            string sourceDirectory = directory.Trim(new char[] {'\\','\\' });
            string destDirectory = string.Format(@"{0}" + DateTime.Now.ToString("yyyyMMddHHmmssffff") , sourceDirectory);
            Directory.Move(sourceDirectory, destDirectory);
        }

        /// <summary> 
        /// 执行一个查询语句，返回一个包含查询结果的DataTable 
        /// </summary> 
        /// <param name="sql">要执行的查询语句</param> 
        /// <param name="parameters">执行查询语句的参数</param> 
        /// <returns></returns> 
        public static DataTable ExecuteDataTable(string sql, SQLiteConnection connection = null)
        {
            if (connection == null)
            {
                InitReadConnection();
                connection = readConnection;
            }
            DataTable dataTable = new DataTable();
            using (SQLiteDataAdapter adapter = new SQLiteDataAdapter(sql, connection))
            {
                adapter.Fill(dataTable);
            }
            return dataTable;
        }

        /// <summary> 
        /// 执行一个查询语句，返回一个包含查询结果的DataSet。 
        /// </summary> 
        /// <param name="sql">要执行的查询语句。</param> 
        /// <param name="parameters">执行SQL查询语句所需要的参数，参数必须以它们在SQL语句中的顺序为准。</param> 
        /// <returns></returns> 
        /// <exception cref="Exception"></exception>
        public static DataSet ExecuteDataSet(string sql)
        {
            DataSet dataSet = new DataSet();
            InitReadConnection();
            using (SQLiteCommand command = new SQLiteCommand(sql, readConnection))
            {
                using (SQLiteDataAdapter adapter = new SQLiteDataAdapter(command))
                { 
                    adapter.Fill(dataSet);
                }
            }
            return dataSet;
        }
    }
}