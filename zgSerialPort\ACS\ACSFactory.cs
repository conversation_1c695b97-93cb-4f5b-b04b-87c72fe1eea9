﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace zgSerialPort.ACS
{
    public static class ACSFactory
    {
        /// <summary>
        /// 获取电子秤处理类
        /// </summary>
        /// <param name="acsName">类名</param>
        /// <returns></returns>
        public static BaseACS GetACS(string acsName) {
            Type type = Type.GetType($"zgSerialPort.ACS.{acsName}");
            var obj = System.Activator.CreateInstance(type);
            return obj as BaseACS;
        }
    }
}
