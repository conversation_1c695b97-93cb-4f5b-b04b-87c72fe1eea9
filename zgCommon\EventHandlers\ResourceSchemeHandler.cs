﻿using CefSharp;
using System;
using System.Drawing;
using System.IO;
using System.Net;
using System.Reflection;
using System.Threading.Tasks;
using zgLogging;
using zgUtils;
using zgUtils.Controls;
using zgUtils.Files;

namespace zgpos.EventHandlers
{
    public class ResourceSchemeHandler : ResourceHandler
    {
        public CefReturnValue ProcessRequestAsync(Uri url, ICallback callback) {

            string filepath = CommonApp.Directory + url.LocalPath.Replace("/","\\");
            try
            {
                if (!Directory.Exists(System.IO.Path.GetDirectoryName(filepath)))
                {
                    Directory.CreateDirectory(System.IO.Path.GetDirectoryName(filepath));
                }
                if (url.LocalPath.Contains("logo.png") && !File.Exists(filepath))
                {
                    zgCommon.Properties.Resources.logo.Save(filepath);
                }
                if (url.LocalPath.Contains("qrcode.png") && !File.Exists(filepath))
                {
                    zgCommon.Properties.Resources.qrcode.Save(filepath);
                }
                ProcessRequest(callback, filepath);
            }
            catch (Exception ex) {
                Log.WriterExceptionLog(ex.Message);
            }
           
            return CefReturnValue.Continue;
        }
        public CefReturnValue ProcessRequestAsync_product(Uri url, ICallback callback)
        {

            string filepath = CommonApp.Directory + url.LocalPath.TrimStart('/').Replace("/", @"\");
            try
            {
                if (!Directory.Exists(System.IO.Path.GetDirectoryName(filepath)))
                {
                    Directory.CreateDirectory(System.IO.Path.GetDirectoryName(filepath));
                }
                if (!File.Exists(filepath))
                {
                    string durl = url.Query.Replace("?url=", "");
                    FileDownLoader.Instance.SimpleDownLoadFile(durl, filepath);
                }

                ProcessRequest(callback, filepath);

            }
            catch (Exception ex) {
                Log.WriterExceptionLog(ex.Message);
            }
            return CefReturnValue.Continue;
        }
        public Stream FileToStream(string fileName)

        {

            // 打开文件

            FileStream fileStream = new FileStream(fileName, FileMode.Open, FileAccess.Read, FileShare.Read);

            // 读取文件的 byte[]

            byte[] bytes = new byte[fileStream.Length];

            fileStream.Read(bytes, 0, bytes.Length);

            fileStream.Close();

            // 把 byte[] 转换成 Stream

            Stream stream = new MemoryStream(bytes);

            return stream;

        }
        public override CefReturnValue ProcessRequestAsync(IRequest request, ICallback callback)
        {
            Uri u = new Uri(request.Url);
            if ("switchIndustry/".Equals(u.Segments[1])) {
                return ProcessRequestAsync_switchIndustry(u,callback);
            }
            if ("local/".Equals(u.Segments[1]))
            {
                return ProcessRequestAsync(u, callback);
            }
            if ("advert/".Equals(u.Segments[1]))
            {
                return ProcessRequestAsync_advert(u, callback);
            }
            if ("productImg/".Equals(u.Segments[1]))
            {
                return ProcessRequestAsync_product(u, callback);
            }
            //var names = this.GetType().Assembly.GetManifestResourceNames();

            //Log.WriterNormalLog(string.Join(",",names));

            
            String file = u.Authority + u.AbsolutePath;

            Assembly ass = App.assembly;
            String resourcePath = "Industries.Resources." + file.Replace("/", ".");
            String resourcePath1 = ass.GetName().Name + ".Resources." + file.Replace("/", ".");
            Task.Run(() =>
            {
                using (callback)
                {
                    if (ass.GetManifestResourceInfo(resourcePath) != null)
                    {
                        SetFileStream(file, ass.GetManifestResourceStream(resourcePath));
                        callback.Continue();
                    }else if (ass.GetManifestResourceInfo(resourcePath1) != null)
                    {
                        SetFileStream(file, ass.GetManifestResourceStream(resourcePath1));
                        callback.Continue();
                    }
                    else
                    {
                        callback.Cancel();
                    }
                }
            }).Wait();

            return CefReturnValue.Continue;
        }

        private CefReturnValue ProcessRequestAsync_switchIndustry(Uri u, ICallback callback)
        {
            try
            {
                String file = u.Authority + u.AbsolutePath;
                Bitmap bit = (Bitmap)zgzn.ConfigController.dics[file.Substring(0, file.LastIndexOf('.')).Substring(file.LastIndexOf('/') + 1)];
                SetFileStream(file, new MemoryStream(BitmapToBytes(bit)));
            }
            catch { }
            
            return CefReturnValue.Continue;
        }
        public static byte[] BitmapToBytes(Bitmap Bitmap)
        {
            MemoryStream ms = null;
            try
            {
                ms = new MemoryStream();
                Bitmap.Save(ms, Bitmap.RawFormat);
                byte[] byteImage = new Byte[ms.Length];
                byteImage = ms.ToArray();
                return byteImage;
            }
            catch (ArgumentNullException ex)
            {
                throw ex;
            }
            finally
            {
                ms.Close();
            }
        }
        private void SetFileStream(string file, Stream stream)
        {
            string mimeType = "application/octet-stream";
            switch (Path.GetExtension(file))
            {
                case ".html":
                    mimeType = "text/html";
                    break;
                case ".js":
                    mimeType = "text/javascript";
                    break;
                case ".css":
                    mimeType = "text/css";
                    break;
                //case ".map":
                //    string subType = Path.GetExtension(Path.GetFileNameWithoutExtension(file));
                //    if(subType.Equals("css")) mimeType = "text/css";
                //    if (subType.Equals("js")) mimeType = "text/javascript";
                //    break;
                case ".png":
                    mimeType = "image/png";
                    break;
                case ".xls":
                case ".xlsx":
                    mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    break;
                case ".svg":
                    mimeType = "image/svg+xml";
                    break;
                case ".appcache":
                    break;
                case ".manifest":
                    mimeType = "text/cache-manifest";
                    break;
            }

            // Reset the stream position to 0 so the stream can be copied into the underlying unmanaged buffer
            stream.Position = 0;
            // Populate the response values - No longer need to implement GetResponseHeaders (unless you need to perform a redirect)
            ResponseLength = stream.Length;
            MimeType = mimeType;
            StatusCode = (int)HttpStatusCode.OK;
            Stream = stream;
        }

        private CefReturnValue ProcessRequestAsync_advert(Uri url, ICallback callback)
        {
            string filepath = CommonApp.Directory + url.LocalPath.Replace("/", "\\");
            ProcessRequest(callback, filepath);

            return CefReturnValue.Continue;
        }

        private void ProcessRequest(ICallback callback, string filepath)
        {
            Task.Run(() =>
            {
                using (callback)
                {
                    if (File.Exists(filepath))
                    {
                        Stream stream = FileToStream(filepath);
                        SetFileStream(filepath, stream);

                        callback.Continue();
                    }
                    else
                    {
                        callback.Cancel();
                    }
                }
            }).Wait();
            
        }
    }
}