﻿using zgSerialPort.Common;
using System.IO.Ports;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Text;
using System.Linq;

namespace zgSerialPort.ACS
{
    /// <summary>
    /// 大华电子秤
    /// </summary>
    [ACSAttribute(ModelName = "大华电子秤", IsRelease = true, Index = 1)]
    public class DaHuaACS : BaseACS
    {
        Regex reg = new Regex(@"(-?\d+(\.\d+)?)");
        StringBuilder value = new StringBuilder();
        public override async void CommDataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            var serialPort = (System.IO.Ports.SerialPort)sender;
            if (serialPort.IsOpen)
            {
                try
                {
                    SerialPort port = (SerialPort)sender;
                    byte[] buffer = new byte[port.BytesToRead];
                    await port.BaseStream.ReadAsync(buffer, 0, buffer.Length); // 异步读取数据到字节数组
                    string data = Encoding.ASCII.GetString(buffer);
                    value.Append(data);
                    if (buffer.Any(b => b == '\r')) {
                        if (value.Length > 0) {
                            var msg = reg.Match(value.ToString()).ToString();
                            CallbackAction(msg);
                            value.Clear();
                        }
                    }
                }
                catch { }

            }
        }
    }
}
