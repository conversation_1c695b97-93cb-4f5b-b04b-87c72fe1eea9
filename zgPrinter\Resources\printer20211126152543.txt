[{"index": 1, "contenttype": 4, "textalign": 0, "datamember": "logo", "displayname": "", "imgpath": ""}, {"index": 2, "contenttype": 1, "textalign": 32, "contentfont": "\"仿宋, 12pt, style=Bold\"", "content": "澎湖山庄家家悦", "datamember": "storename", "format": "{0}", "defaultcontent": "", "displayname": "文本", "wordwarp": false}, {"index": 2, "contenttype": 5, "textalign": 0, "content": 1}, {"index": 3, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "流水号：11111", "datamember": "流水号", "format": "流水号：{0}", "defaultcontent": "", "displayname": "流水号", "wordwarp": false}, {"index": 3, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "类型：堂食", "datamember": "类型", "format": "类型：{0}", "defaultcontent": "", "displayname": "类型", "wordwarp": false}, {"index": 3, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "销售单号：11111", "datamember": "销售单号", "format": "销售单号：{0}", "defaultcontent": "", "displayname": "订单号", "wordwarp": false}, {"index": 4, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "", "datamember": "收银员", "format": "收银员：{0}", "defaultcontent": "", "displayname": "收银员", "wordwarp": false}, {"index": 5, "contenttype": 5, "textalign": 0, "content": 0}, {"index": 6, "contenttype": 3, "headtextalign": 32, "bodytextalign": 16, "datamember": "groups", "printhead": true, "printseparatorline": false, "headfont": "\"仿宋, 7.5pt\"", "bodyfont": "\"仿宋, 7.5pt\"", "config": "[{\"col_width\":\"90\",\"col_align\":\"Left\",\"col_head\":\"商品名称\",\"col_dataMember\":\"商品名称\",\"col_singleLine\":\"true\",\"col_isPrint\":\"true\"},{\"col_width\":\"35\",\"col_align\":\"Center\",\"col_head\":\"数量\",\"col_dataMember\":\"数量\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\"},{\"col_width\":\"30\",\"col_align\":\"Right\",\"col_head\":\"金额\",\"col_dataMember\":\"金额\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\"},{\"col_width\":\"30\",\"col_align\":\"Right\",\"col_head\":\"优惠价\",\"col_dataMember\":\"优惠价\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\"}]", "displayname": "商品信息"}, {"index": 7, "contenttype": 5, "textalign": 0, "content": 0}, {"index": 8, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "", "datamember": "合计", "format": "金额合计：{0}", "defaultcontent": " ", "displayname": "合计", "wordwarp": false}, {"index": 9, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "", "datamember": "其他优惠", "format": "优惠：{0}", "defaultcontent": "", "displayname": "其他优惠", "wordwarp": false}, {"index": 10, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "", "datamember": "应付", "format": "应付：{0}", "defaultcontent": "", "displayname": "应付", "wordwarp": false}, {"index": 11, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "", "datamember": "会员支付", "format": "会员支付：{0}", "defaultcontent": "", "displayname": "会员支付", "wordwarp": false}, {"index": 12, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "", "datamember": "找零", "format": "找零：{0}", "defaultcontent": "", "displayname": "找零", "wordwarp": false}, {"index": 14, "contenttype": 5, "textalign": 0, "content": 1}, {"index": 15, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "", "datamember": "会员名", "format": "会员名：{0}", "defaultcontent": "", "displayname": "会员名", "wordwarp": false}, {"index": 16, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "", "datamember": "会员手机号", "format": "会员手机号：{0}", "defaultcontent": "", "displayname": "会员手机号", "wordwarp": false}, {"index": 17, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "", "datamember": "余额", "format": "余额：{0}", "defaultcontent": "", "displayname": "余额", "wordwarp": false}, {"index": 18, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9pt\"", "content": "", "datamember": "积分", "format": "积分：{0}", "defaultcontent": "", "displayname": "积分", "wordwarp": false}, {"index": 21, "contenttype": 5, "textalign": 0, "content": 0}, {"index": 22, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 9.75pt, style=Bold\"", "content": "", "datamember": "备注内容", "format": "{0}", "defaultcontent": "", "displayname": "结尾", "wordwarp": false}, {"index": 23, "contenttype": 1, "textalign": 16, "contentfont": "\"仿宋, 7pt\"", "content": "", "datamember": "打印时间", "format": "打印时间：{0}", "defaultcontent": "", "displayname": "打印时间", "wordwarp": false}, {"index": 24, "contenttype": 4, "textalign": 0, "datamember": "qrcode", "displayname": "", "imgpath": ""}]