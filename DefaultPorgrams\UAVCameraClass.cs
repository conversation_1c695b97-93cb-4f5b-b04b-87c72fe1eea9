﻿using System.Windows.Forms;

using zgpos.ProgramTemplate;

namespace DefaultPorgrams
{
    public class UAVCameraClass : ProgramTemplateClass
    {
        public UAVCameraClass()
        {
            this.Name = "商超版";
            this.Description = "商超版 [via leon]";
            this.Icon = DefaultProgramResource.UAVCameraIcon;
        }

        public override string FileName => System.Reflection.Assembly.GetExecutingAssembly().ManifestModule.ScopeName;
        protected override Form CreateProgramForm()
        {
            return new DefaultProgramForm(
                this.Name,
                this.Icon,
                DefaultProgramResource.UAVCamera
                );
        }
    }
}
