﻿using System;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;
using zgLogging;
using zgpos;
using zgpos.ProgramTemplate;
using zgUtils;
using zgUtils.Model;

namespace zgzn
{
    public partial class DesktopForm : Form
    {
        //string ver ;
        //string oldver = null;
        public DesktopForm()
        {
            CommonApp.Authorization = zgzn.ConfigController.Authorization;

            CommonApp.DefaultConfigURL = zgUtils.Security.AESHelper.StringDecoding(zgzn.ConfigController.DefaultConfigURL);
            CefHelper.InitCef();
            this.InitializeComponent();            
        }
        public DesktopForm(bool isproduct, Assembly assembly) :this()
        {
            CommonApp.ISPRODCT = isproduct;
            CommonApp.Assembly = assembly;
        }
        public void ShowProgram(int errCnt=0,ProgramTemplateClass CurrentProgram = null) {

            CommonApp.Industries = IndustryClass.GetIndustries();
            App.isReload = false;
            if (CurrentProgram == null || CurrentProgram?.ProgramForm == null)
            {
                string fileName = ConfigController.GetIndustryName();
                    
                try
                {
                    CurrentProgram = InitializeProgram(fileName);
                    
                }
                catch (Exception ex) {
                    zgLogging.Log.Error("{0}加载程序插件遇到异常：{1}-{2}", fileName,CommonApp.Industries?.Count, ex.Message);

                    //try
                    //{
                    //    zgLogging.Log.Error("加载程序插件遇到异常：{0}-{1}", CommonApp.Industries?.Count, ex.Message);
                    //    IndustryClass industry = null;
                    //    if (!string.IsNullOrEmpty(fileName))
                    //    {
                    //        industry = IndustryClass.GetIndustry(fileName);
                    //    }
                    //    else if (CommonApp.Industries?.Count==1)
                    //    {
                    //        industry = CommonApp.Industries[0];
                    //    }
                    //    if (industry != null && !string.IsNullOrEmpty(industry.name))
                    //    {
                    //        zgLogging.Log.Info("远程下载程序插件开始：");
                    //        App.SwitchIndustry(industry, null);
                    //        UnityModule.Invoke("SwitchIndustry");
                    //        ShowProgram();
                    //        //this.Close();

                    //        return;
                    //    }

                    //}
                    //catch (Exception ex1)
                    //{
                    //    zgLogging.Log.Error("再次加载程序插件遇到异常：{0}", ex1.Message);
                    //}
                }
            }
            var fm = new Form();

            if (CurrentProgram == null || CurrentProgram?.ProgramForm == null)
            {
                fm = new FormShow(this);// this.LoadProgram();
            }
            else
            {
                fm = CurrentProgram.ProgramForm;
            }
            //ver = zgUtils.CommonApp.Version;
            //if (oldver != null && !ver.Equals(oldver))
            //{
            //    zgUtils.CommonApp.IndustryUpdated = true;
            //    oldver = null;
            //}
            fm.ShowDialog(this);
            fm.Dispose();
            this.DialogResult = DialogResult.OK;
            Log.WriterNormalLog(String.Format("画面关闭处理：AllowToQuit:{0} isReload:{1}", App.AllowToQuit, App.isReload));
            if (App.isReload) {

                //oldver = ver;
                //ConfigController.dics.Add("reload",true);
                UnityModule.Invoke("Reload");

                this.Close();                
            }else if (App.isSwitchIndustry) {
                UnityModule.Invoke("SwitchIndustry");
                this.Close();
            }
            else if (App.AllowToQuit) this.Close();
           
            zgLogging.Log.Info(CurrentProgram?.Name + " Closed");
        }

        private void DesktopForm_Shown(object sender, EventArgs e)
        {
            
            Application.DoEvents();
            
        }

        

        private static ProgramTemplateClass InitializeProgram(string fileName)
        {
            CommonApp.SetupPackge = ConfigController.GetConfig("SetupPackge");
            
            string ProgramPath = FileController.PathCombine(UnityModule.ProgramDirectory, fileName);            
            ProgramTemplateClass ProgramInstance = ProgramController.GetProgramPlugin(ProgramPath, "MainClass");

            //无法创建指定DLL内指定CLASS的Program对象时，尝试扫描整个目录
            if (ProgramInstance == null && string.IsNullOrEmpty(fileName))
            {
                zgLogging.Log.Error("无法创建指定的 Program，尝试扫描 Program 目录 ...");
                ProgramInstance = ProgramController.ScanProgramPlugins(UnityModule.ProgramDirectory).FirstOrDefault();
            }

            if (ProgramInstance == null)
                throw new Exception("无法创建 Program 对象");

            if (ProgramInstance.ProgramForm == null)
                throw new Exception("无法创建 Program.ProgramForm 对象");

            ProgramInstance.ProgramFinished += new EventHandler<EventArgs>((s, e) => { SwitchToProgram(s, e); });
        
            return ProgramInstance;
        }
        private static void SwitchToProgram(object s, EventArgs e)
        {
            zgLogging.Log.Info("主画面启动完成！");

        }

        private void DesktopForm_Load(object sender, EventArgs e)
        {
            
            try
            {
                ShowProgram();
            }
            catch (Exception ex) {
                zgLogging.Log.Error("无法创建指定的 Program，尝试扫描 Program 目录 ...",  ex.Message);
                
            }
            
        }
        
    }
}
