﻿using CefSharp;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using zgLogging;
using zgUtils;

namespace zgCommon.Browser
{
    internal class CsharpHttps
    {
		/// <summary>
		/// 下载文件
		/// </summary>
		public async Task<int> DownloadFile(string url, string token, string directoryName, string fileName,
			IJavascriptCallback successBack = null, IJavascriptCallback errorBack = null)
		{
			HttpWebRequest httpWebRequest = null;

			try
			{
				string fileDirectory = string.Format(@"{0}Resources\sync-csv\{1}", CommonApp.BaseDirectory, directoryName);
				string filePath = string.Format(@"{0}\{1}", fileDirectory, fileName);

				httpWebRequest = (HttpWebRequest) WebRequest.Create(url);
				httpWebRequest.Method = "GET";
				httpWebRequest.ReadWriteTimeout = 300000;
				httpWebRequest.Timeout = 300000;
				httpWebRequest.Headers.Add("Authorization", token);

				int res = 0;
				using (WebResponse response = httpWebRequest.GetResponse())
				{
					string contentType = response.ContentType;

                    if (contentType != null && contentType.StartsWith("application/octet-stream", StringComparison.CurrentCultureIgnoreCase))
					{
                        if (!Directory.Exists(fileDirectory))
                        {
                            Directory.CreateDirectory(fileDirectory);
                        }
                        if (File.Exists(filePath))
                        {
                            File.Delete(filePath);
                        }
                        using (Stream responseStream = response.GetResponseStream())
                        {
                            using (FileStream fileStream = new FileStream(filePath, FileMode.Create))
                            {
                                responseStream.CopyTo(fileStream);
                                res = 1;
                            }
                        }
                    }
				}

				if (successBack != null)
				{
					using (successBack)
					{
						await successBack.ExecuteAsync(res);
					}
				}
				return res;
			}
			catch (Exception ex)
			{
				Log.WriterExceptionLog(string.Format("DownLoadFile失败：{0}\n{1}", url, ex.Message));
				if (errorBack != null)
				{
					using (errorBack)
					{
						string results = "{\"code\":\"" + ex.HResult + "\",\"message\":\"" + ex.Message + "\"}";
						results = results.Replace("\r\n", "");
						await errorBack.ExecuteAsync(results);
					}
				}

				throw ex;
			}
			finally
			{
                httpWebRequest?.Abort();
			}
		}

		/// <summary>
		/// 云同步上传文件
		/// </summary>
		public async Task<int> UploadSyncFile(string url, string token, string[] tableNames, IJavascriptCallback successBack = null, IJavascriptCallback errorBack = null)
		{
			HttpWebRequest httpWebRequest = null;

			try
			{
                string boundary = DateTime.Now.Ticks.ToString("x");

                httpWebRequest = (HttpWebRequest)WebRequest.Create(url);
                httpWebRequest.ContentType = "multipart/form-data; boundary=" + boundary;
                httpWebRequest.Method = "POST";
                httpWebRequest.KeepAlive = true;
                httpWebRequest.Credentials = CredentialCache.DefaultCredentials;
                httpWebRequest.ReadWriteTimeout = 300000;
                httpWebRequest.Timeout = 300000;
                httpWebRequest.Headers.Add("Authorization", token);

                string fileDirectory = string.Format(@"{0}Resources\sync-csv\", CommonApp.BaseDirectory);
                int res = 0;

                List<KeyValue> keyValues = new List<KeyValue>();
                foreach (string tableName in tableNames) 
				{
                    string filePath = string.Format("{0}{1}.csv", fileDirectory, tableName);
                    FileInfo file = new FileInfo(filePath);

                    if (file.Exists && file.Length > 0)
                    {
                        keyValues.Add(new KeyValue(tableName, file.Name, filePath, "text/csv"));
                    }
                }

				if (keyValues.Count > 0) 
				{
					ExecuteMultipartRequest(httpWebRequest, boundary, keyValues);
					using (HttpWebResponse response = (HttpWebResponse)httpWebRequest.GetResponse())
					{
						if (response.StatusCode == HttpStatusCode.OK)
						{
							res = 1;
						}
					}
				}

                if (successBack != null)
				{
					using (successBack)
					{
						await successBack.ExecuteAsync(res);
					}
				}
				return res;
			}
			catch (Exception ex)
			{
				Log.WriterExceptionLog(string.Format("UploadSyncFile失败：{0}\n{1}", url, ex.Message));
				if (errorBack != null)
				{
					using (errorBack)
					{
						string results = "{\"code\":\"" + ex.HResult + "\",\"message\":\"" + ex.Message + "\"}";
						results = results.Replace("\r\n", "");
						await errorBack.ExecuteAsync(results);
					}
				}

				throw ex;
			}
			finally
			{
                httpWebRequest?.Abort();
			}
		}

        public static void ExecuteMultipartRequest(HttpWebRequest request, string boundary, List<KeyValue> keyValues)
        {
            byte[] boundarybytes = Encoding.ASCII.GetBytes("\r\n--" + boundary + "\r\n");
            using (Stream stream = request.GetRequestStream())
            {
                string paramTemplate = "Content-Disposition: form-data; name=\"{0}\"\r\n\r\n{1}";
                string fileTemplate = "Content-Disposition: form-data; name=\"{0}\";filename=\"{1}\"\r\nContent-Type:{2}\r\n\r\n";
                foreach (KeyValue keyValue in keyValues)
                {
                    if (keyValue.FilePath == null)
                    {
                        stream.Write(boundarybytes, 0, boundarybytes.Length);
                        string formitem = string.Format(paramTemplate, keyValue.Key, keyValue.Value);
                        byte[] formitembytes = Encoding.UTF8.GetBytes(formitem);
                        stream.Write(formitembytes, 0, formitembytes.Length);
                    }
                    else
                    {
                        stream.Write(boundarybytes, 0, boundarybytes.Length);
                        string header = string.Format(fileTemplate, keyValue.Key, keyValue.FilePath, keyValue.ContentType);
                        byte[] headerbytes = Encoding.UTF8.GetBytes(header);
                        stream.Write(headerbytes, 0, headerbytes.Length);

						byte[] fileBytes = File.ReadAllBytes(keyValue.FilePath);
						stream.Write(fileBytes, 0, fileBytes.Length);
					}
                }

                byte[] trailer = Encoding.ASCII.GetBytes("\r\n--" + boundary + "--\r\n");
                stream.Write(trailer, 0, trailer.Length);
				stream.Close();
            }
        }
    }
}
