﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using zgLogging;
using zgUtils.Controls;
using zgUtils.Model;
using zgUtils.Security;

namespace zgUtils
{
    public class CommonApp
    {
        public static SettingInfo settings { get; set; } = new SettingInfo();
        public static List<StoreInfo> storeinfo { get; set; } = new List<StoreInfo>();
        public static bool IndustryUpdated { get; set; } = false;
        public static UserInfo userinfo { get; set; } = new UserInfo();
        //public static Advert advert { get; set; } = new Advert();
        public static List<Clerk> clerks { get; set; } = new List<Clerk>();

        public static string GetInitDataJsonStr()
        {
            InitData();
            return "window.$clerks=" + JsonConvert.SerializeObject(clerks)
            + ";window.$storeinfo=" + JsonConvert.SerializeObject(storeinfo)
            + ";window.$setting=" + JsonConvert.SerializeObject(settings.setting)
            + ";window.$userinfo=" + JsonConvert.SerializeObject(userinfo)
            + ";window.$config=" + JsonConvert.SerializeObject(CommonApp.Config);
        }

        public static object Mqtt { get; set; }

               
        public static void InitData()
        {
            DataSet ds = SQLiteHelper.GetInitData();
            if (ds == null) return;
            settings = new SettingInfo(ds.Tables[0]);
            userinfo = settings.getUserinfo();
            clerks = Utils.DataTableToList<Clerk>(ds.Tables[1]);
            storeinfo = Utils.DataTableToList<StoreInfo>(ds.Tables[2]);
            userinfo.privilege = clerks.Where((clerk) => clerk.uid == userinfo.uid).FirstOrDefault()?.privilege;

        }

        public static string SendToWebBrowser()
        {
            try
            {
                return string.Format("{{\"$clerks\":{0},\"$storeinfo\":{1},\"$setting\":{2},\"$userinfo\":{3},\"$config\":{4}}}",
                               //JsonConvert.SerializeObject(advert),
                               JsonConvert.SerializeObject(clerks),
                               JsonConvert.SerializeObject(storeinfo),
                               JsonConvert.SerializeObject(settings.setting),
                               JsonConvert.SerializeObject(userinfo),
                               JsonConvert.SerializeObject(CommonApp.Config)
                           );
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
                return "{{}}";
            }


        }
        public static string UpdateSetinng()
        {
            DataTable dt = SQLiteHelper.ExecuteDataTable(SQLiteHelper.sql_setting);
            settings = new SettingInfo(dt);
            return string.Format("{{\"$setting\":{0}}}", JsonConvert.SerializeObject(settings.setting));
        }
        public static void CreateStore()
        {
            Log.CreateLogListener(SubDirectory + "-" + sysUid + "-" + sysSid);
            SQLiteHelper.OpenDatabase(Version, Directory + "data\\" + SubDirectory);
            if (SQLiteHelper.FileSize(SQLiteHelper.FullPath) == 0 )
            {
                SQLiteHelper.InitData(SQLiteHelper.FullPath, NetworkCenter.Instance.GetLocalDb(Version));
            }
        }
        public static void UpdateDataBase(string installedVersion)
        {
            Log.WriterNormalLog(string.Format("版本是否升级：{0}-{1} {2}", sysUid, sysSid, IndustryUpdated));
            if(IndustryUpdated) CommonApp.Config.IndustryUpdated = true;
            if (string.IsNullOrEmpty(sysUid) || !IndustryUpdated) return;
            IndustryUpdated = false;            
            byte[] resourcesData = NetworkCenter.Instance.GetLocalDb(Version);
            if (resourcesData == null) return;
            SQLiteHelper.InitData(SQLiteHelper.SqlitePath, resourcesData);
            SQLiteHelper.UpdateDataBase();
        }
        public static ResponseLogin Logon(LoginRequest loginParam)
        {
            step = Setp.Step_1;
            ResponseLogin result;
            Log.Debug(string.Format("登录参数：{0}", JsonConvert.SerializeObject(loginParam)));
            if (loginParam.data == null)
            {
                RequestBase<LoginRequest> request = new RequestBase<LoginRequest>(CommonApp.Config.ServerUrl.LogonUrl, "", loginParam);
                //Log.WriterNormalLog(string.Format("请求远程接口登录：{0}", request.ToJson()));
                result = NetworkCenter.Instance.SendRequest<RequestBase<LoginRequest>, ResponseLogin>(request);
                Log.Debug(string.Format("请求远程接口登录：{0}", result.ToJson()));
            }
            else {
                result = loginParam.data;
                //Log.WriterNormalLog(string.Format("群客多扫码登录：{0}", result.ToJson()));
            }
            
            if (result.Code != 200)
            {
                return result;
            }
            UserInfo userInfo = JsonConvert.DeserializeObject<UserInfo>(JsonConvert.SerializeObject(result.data));
            CurrentUserConfig currentUserConfig = new CurrentUserConfig(userInfo);
            FileManager.SetConfig("CurrentUserConfig", currentUserConfig.data,CommonApp.ProgramFile);
            ResetIndustryUpdated(loginParam.version_db);
            ConfigManager.LoadConfig();
            //NetworkCenter.Instance.AddLoginActionLog();
            Log.WriterNormalLog("本地数据更新处理开始");
            // settings表内是否有devicecode // 将sys_uid、sys_sid、token、手机号、密码、是否记住密码状态、登陆时间 插入到settings表
            string sql = "replace into settings (key, value, remark) values ('{0}', '{1}', '{2}')";
            //DataTable dt = CommonApp.settings.localData.Clone();

            List<string> sqllist = new List<string>();
            sqllist.Add(string.Format(sql, "userremember", loginParam?.userremember.ToString(), ""));
            sqllist.Add(string.Format(sql, "userlastime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), ""));
            sqllist.Add(string.Format(sql, "ultimate", CommonApp.Config?.activationCode?.ultimate?.ToString().ToLower(), ""));
            sqllist.Add(string.Format(sql, "useruid", result.data?.sysUid.ToString(), ""));
            sqllist.Add(string.Format(sql, "usersid", result.data?.sysSid.ToString(), ""));
            sqllist.Add(string.Format(sql, "usertoken", result.data?.token.ToString(), ""));
            sqllist.Add(string.Format(sql, "username", result.data?.name?.ToString(), ""));
            sqllist.Add(string.Format(sql, "uid", result.data?.uid?.ToString(), ""));
            if(CommonApp.Config?.deviceCode>0) sqllist.Add(string.Format(sql, "devicecode", CommonApp.Config?.deviceCode, ""));
            sqllist.Add(string.Format(sql, "period", CommonApp.Config?.activationCode?.period.ToString(), ""));
            Log.WriterNormalLog("本地数据更新处理开始step1");
            if (loginParam.type != 2)
            {
                sqllist.Add(string.Format(sql, "userpwd", loginParam.password, ""));
            }
            else
            {
                sqllist.Add(string.Format(sql, "employeeremember", loginParam.employeeremember.ToString(), ""));
                sqllist.Add(string.Format(sql, "employeenumber", result.data.employeeNumber, ""));
                List<string> employeenumberList = settings.setting.employeenumber_list?.Split(',')?.ToList() ?? new List<string>();
                if (loginParam.employeeremember)
                {
                    if (!employeenumberList.Contains(result.data.employeeNumber)) employeenumberList.Add(result.data.employeeNumber);
                    sqllist.Add(string.Format(sql, "employeenumber_list", string.Join(",", employeenumberList), ""));
                }
            }
            Log.WriterNormalLog("本地数据更新处理开始step2");
            // storeinfo 更新 {guid}, {name}, {addr}, {industry}, {contact}, {tel}, {qq},{createAt}, {syncAt}, 0, 1,{discountSettings}, {settings}
            //var ins_storeinfo = @"replace into storeinfo(id,guid, name, addr, industry, contacter, tel, qq, create_at, revise_at,
            // is_deleted, is_synced, discount_settings, settings) values(1,'{0}', '{1}', '{2}', '{3}', '{4}', '{5}', '{6}','{7}', '{8}', 0, 1,'{9}', '{10}' );";
            //var store = result.data?.shopList?[0];
            //sqllist.Add(string.Format(ins_storeinfo, store.guid, store.name, store.addr, store.industry, store.contacter, store.tel, store.qq, store.create_at, store.syncTime, store.discount_settings, store.settings));


            // clerk 表更新{uid}, '{name}', {role}, {status}, '{privilege}', '{phone}', '{email}', '{gender}', '{avatar}''{employeeNumber}', '{password}'
            var clerk = @"replace into clerks(uid,name,role,status,privilege,phone,email,gender,avatar,employee_number,password)
                select {0}, '{1}', {2}, {3}, '{4}', '{5}', '{6}', '{7}', '{8}','{9}', '{10}'";
            sqllist.Add(string.Format(clerk, result.data.uid, result.data.name, 1, 0, result.data.privilege, result.data.phone, "", "", "", null, null));
            List<LoginAuthList> clerkData = result.data?.shopList?[0].loginAuthList;
            if (clerkData?.Count > 0)
            {
                foreach (var item in clerkData)
                {
                    sqllist.Add(string.Format(clerk, item.uid, item.name, item.role, item.status, item.privilege, item.phone, "", "", "", item.employeeNumber, item.password));
                }
            }
            Log.WriterNormalLog("本地数据更新处理开始step3");
            // 交接班数据插入{uid},case when '{employee_number}'='null' then null else '{employee_number}' end,'{name}',strftime('%Y-%m-%d %H:%M:%S','now','localtime'),'{create_by}','{fingerprint}'
            var shiftData = @"insert into shifthistories(uid, employee_number, name, begin_date, create_by, fingerprint)
                VALUES ({0},'{1}','{2}',strftime('%Y-%m-%d %H:%M:%S','now','localtime'),'{3}','{4}'
                  );";
            sqllist.Add(string.Format(shiftData, result.data.uid, result.data.employeeNumber, result.data.name, result.data.uid, result.data.fingerprint));
            SQLiteHelper.ExecuteNonQueryBatch(sqllist);
            Log.WriterNormalLog("本地数据更新处理完成");
            return result;
        }

        public static void getCommonProduct(string code)
        {
            Product Param = new Product(code);
            var url = "http://pos.zhangguizhinang.com/POSAPI/products/getCommonProducts";
            step = Setp.Step_1;
            Log.Info(string.Format("登录参数：{0}", JsonConvert.SerializeObject(Param)));
            RequestBase<Product> request = new RequestBase<Product>(url, "", Param);
            //Log.WriterNormalLog(string.Format("请求远程接口登录：{0}", request.ToJson()));
            //result = NetworkCenter.Instance.SendRequest<RequestBase<Product>, ResponseJsonStr>(request);
            //Log.Info(string.Format("请求远程接口登录：{0}", result.ToJson()));
            Req<string> req = new Req<string>(request.CreateURL(), request.PostContent());
            var result = ApiHelper.PostAsJsonAsync<string, ResponseJsonStr>(req).Result.RespData;


            if (result.Code != 200)
            {
                Log.Info(string.Format("请求远程接口登录：{0}", result.ToJson()));

            }

        }
        private static void ResetIndustryUpdated(string version_db)
        {
            try
            {
                if (!string.IsNullOrEmpty(sysUid) && !IndustryUpdated && !string.IsNullOrEmpty(version_db))
                {
                    string sql = string.Format("select 1 from upgradelist where version='{0}'", version_db);
                    Log.WriterNormalLog("ResetIndustryUpdated:" + sql);
                    DataTable tb = SQLiteHelper.ExecuteDataTable(sql);
                    if (tb != null && tb.Rows.Count > 0)
                    {
                        IndustryUpdated = false;
                    }
                    else {
                        IndustryUpdated = true;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error("ResetIndustryUpdated:" + ex.Message);
            }
        }
        public static void initialize()
        {
            sysUid = null;
            sysSid = null;
            settings = new SettingInfo();
            storeinfo = new List<StoreInfo>();
            userinfo = new UserInfo();
            clerks = new List<Clerk>();
            isClose = false;
            frmBack = null;
            useCardReader = 0;
            secondaryScreen = null;
            hasCustomerDisplay = true;
            _version = null;
        }

        public static void ClearUser(string sysUid = null, int? sysSid = null)
        {
            CurrentUserConfig currentUserConfig = null;
            ConfigManager.Clear();
            
            settings = new SettingInfo();
            storeinfo = new List<StoreInfo>();
            userinfo = new UserInfo();
            clerks = new List<Clerk>();
            if (!string.IsNullOrEmpty(sysUid))
            {
                //CommonApp.Config = ConfigManager.Instance;
                userinfo.sysUid = sysUid;
                userinfo.sysSid = sysSid?.ToString();
                userinfo.Accesstoken = CommonApp.Authorization;
                currentUserConfig = new CurrentUserConfig(userinfo);
                settings.setting.sysUid = sysUid;
                settings.setting.sysSid = sysUid;
            }
            FileManager.SetConfig("CurrentUserConfig", currentUserConfig?.data, CommonApp.ProgramFile);
            
           
        }

        public static string GetUpdateInfo()
        {
            try
            {
                //string sysuid = GetSysuid();
                string param = "{sys_uid:\"" + CommonApp.sysUid + "\"}";
                RequestJsonStr request = new RequestJsonStr(CommonApp.Config.ServerUrl.AUTOUPDATEURL, "", param);
                ResponseArray result = NetworkCenter.Instance.SendRequest<RequestJsonStr, ResponseArray>(request);
                if (result?.Code == 200 && result.data?.Count > 0)
                {
                    string url = "", url_0 = "", url_1 = "";
                    foreach (Dictionary<string, object> row in result.data)
                    {
                        string version = row["osVersion"].ToString();
                        if (row["sysUid"].Equals("0"))
                        {
                            url_0 = version;
                        }
                        else
                        {
                            url_1 = version;
                            isGrayUser = true;
                        }
                    }
                    url = string.IsNullOrEmpty(url_1) ? url_0 : url_1;
                    return url;
                }
            }
            catch (Exception ex)
            {
                Log.WriterNormalLog(ex.Message);
            }
            return "";
        }

        //public static string GetUpdateUrl()
        //{
        //    string ver = GetUpdateInfo();
        //    string url = "";

        //    if (!string.IsNullOrEmpty(ver))
        //    {
        //        url = string.Format(CommonApp.Config.ServerUrl.UPDATEURL, ver);
        //        if (!Utils.HttpFileExist(url))
        //        {
        //            url = "";
        //        }
        //    }

        //    return url;
        //}

        public static bool isClose = false;
        public static Form mainform;
        public static Form frmOnLine = null;
        public static Form frmBack = null;
        //public static Form frmShow = null;
        public static int useCardReader = 0;
        public static Screen secondaryScreen = null;
        public static bool hasCustomerDisplay = true;
        public static string Printers { get { return Utils.getPrinters(); } }
        private static string _token;
        public static string Authorization { get => _token.StartsWith("Bearer ") ? _token : "Bearer " + _token; set => _token = value; }
        public static string DefaultConfigURL { get; set; }
        public static string DeviceId { get { return FingerPrint.Value(); } }
        private static string _programFile;
        public static string ProgramFile
        {
            get { return _programFile; }
            set
            {
                _programFile = value;
                _baseDirectory = _programFile.Substring(0, _programFile.LastIndexOf("\\") + 1);
                _subDirectory = _programFile.Substring(_programFile.LastIndexOf("\\") + 1, _programFile.LastIndexOf(".") - _programFile.LastIndexOf("\\") - 1).ToLower();
                if (string.IsNullOrEmpty(_version))
                {
                    _version = FileVersionInfo.GetVersionInfo(_programFile).FileVersion;

                }
            }
        }
        public static string Directory { get { return BaseDirectory + SubDirectory + "-" + sysUid + "-" + sysSid + "\\"; } }
        private static string _baseDirectory;
        public static string BaseDirectory { get { return _baseDirectory; } }
        private static string _subDirectory;
        public static string SubDirectory { get { return _subDirectory; } }

        private static string _version;
        public static string Version
        {
            get
            {
                return _version;

            }
        }// // 当前版本号
        public static ConfigManager Config
        {
            get;
            set;
        }
        public static string systemTitle;
        public static bool ISPRODCT;
        
        public static string sysUid { get; set; }
        public static string sysSid { get; set; }
        public static ConfigBase ConfigBase { get { return new ConfigBase(); } }

        public static Setp step { get; set; }
        public static string SetupPackge { get; set; }
        public static bool FirstLogin { get; set; }
        public static Object MainClass { get; set; }
        public static bool isGrayUser { get; set; } = false;
        public static List<IndustryClass> Industries { get; set; }
        public static Assembly Assembly { get; set; }

        public enum Setp
        {
            [Description("登录前")]
            Step_0 = 0,
            [Description("登录后")]
            Step_1 = 1,
            [Description("初次同步后")]
            Step_2 = 2
        }
        #region 内存回收  
        [DllImport("kernel32.dll", EntryPoint = "SetProcessWorkingSetSize")]
        public static extern int SetProcessWorkingSetSize(IntPtr process, int minSize, int maxSize);
        /// <summary>  
        /// 释放内存  
        /// </summary>  
        public static void ClearMemory(string msg = "")
        {
            if (msg.Length > 5000) msg = msg.Substring(0, 5000);
            StackTrace trace = new StackTrace();
            StackFrame frame = trace.GetFrame(1);
            MethodBase method = frame.GetMethod();
            string className = method.ReflectedType.Name;
            Log.WriterNormalLog("ClassName:" + className + " MethodName:" + method.Name + "......SQL:" + msg);

            Process proc = Process.GetCurrentProcess();
            long usedMemory = proc.PrivateMemorySize64;
            if (usedMemory > 1024 * 1024 * 500)
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
                if (Environment.OSVersion.Platform == PlatformID.Win32NT)
                {
                    SetProcessWorkingSetSize(Process.GetCurrentProcess().Handle, -1, -1);
                }
            }
        }
        #endregion



    }

    public class Product
    {
        public Product()
        {
            
        }
        public Product(string code)
        {
            this.code = code;
        }

        public string code { get; set; }
        public string sysUid { get; set; } = "17705351914";
        public int sysSid { get; set; } = 1;
        public bool barcode { get; set; } = true;
        
    }
}
