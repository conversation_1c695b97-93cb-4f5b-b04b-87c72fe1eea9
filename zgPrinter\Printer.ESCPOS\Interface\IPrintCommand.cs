﻿using ESC_POS_USB_NET.Interfaces.Command;
using zgPrinter.Printer.ESCPOS.Interface;

namespace zgPrinter.Printer.ESCPOS.Interface
{
    internal interface IPrintCommand
    {
        int ColsNomal { get; }
        int ColsCondensed { get; }
        int ColsExpanded { get; }
        IFontMode FontModeCmd { get; set; }
        IFontWidth FontWidthCmd { get; set; }
        IAlignment AlignmentCmd { get; set; }
        IPaperCut PaperCutCmd { get; set; }
        IDrawer DrawerCmd { get; set; }
        IQrCode QrCodeCmd { get; set; }
        IBarCode BarCodeCmd { get; set; }
        IImage ImageCmd { get; set; }
        ILineHeight LineHeightCmd { get; set; }
        IInitializePrint InitializePrintCmd { get; set; }
        byte[] Separator(char speratorChar = '-');
        byte[] AutoTest();
    }
}

