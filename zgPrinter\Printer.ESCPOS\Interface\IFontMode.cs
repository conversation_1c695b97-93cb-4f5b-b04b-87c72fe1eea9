﻿using zgPrinter.Model;

namespace zgPrinter.Printer.ESCPOS.Interface
{

    internal interface IFontMode
    {
        byte[] Bold(string value);
        byte[] Bold(EnumPrinterModeState state);
        byte[] Underline(string value);
        byte[] Underline(EnumPrinterModeState state);
        byte[] Expanded(string value);
        byte[] Expanded(EnumPrinterModeState state);
        byte[] Condensed(string value);
        byte[] Condensed(EnumPrinterModeState state);
        byte[] Font(string value, EnumFonts state);
        byte[] Font(EnumFonts state);
    }
}

