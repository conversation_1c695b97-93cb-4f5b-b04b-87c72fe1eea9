﻿using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using zgSerialPort.Common;

namespace zgSerialPort.ACS
{
    /// <summary>
    /// 顶尖OS2电子秤
    /// </summary>
    [ACSAttribute(ModelName = "顶尖OS2", IsRelease = true, Index = 3)]
    internal class AclasOS2ACS : BaseACS
    {
        /// <summary>
        /// 协议头
        /// </summary>
        byte[] protocolHeader = new byte[] { 0x01, 0x02 };

        /// <summary>
        /// 协议尾
        /// </summary>
        byte[] protocolEnd = new byte[] { 0x03, 0x04 };
        /*
         byte [] bytes = new byte[]{0x01,0x02,0x53,0x20,0x20,0x30,0x2C,0x35,0x37,0x30,0x4B,0x47,0x71,0x03,0x04,0x08};
         */

        /// <summary>
        /// 默认编码
        /// </summary>
        private Encoding encoding = System.Text.Encoding.UTF8;
        public AclasOS2ACS()
        {
        }

        public override void CommDataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            var serialPort = (System.IO.Ports.SerialPort)sender;
            if (serialPort.IsOpen)
            {
                try
                {
                    Thread.Sleep(50);

                    byte[] buffer = new byte[200];
                    serialPort.Read(buffer, 0, buffer.Length);
                    int startIndex = IndexOf(buffer, protocolHeader);
                    int endIndex = LastIndexOf(buffer, protocolEnd);
                    var result = buffer.Skip(startIndex + 2).Take(endIndex - startIndex + 1);

                    if (result.Count() > 0 && startIndex > -1 && endIndex > startIndex + 1)
                    {
                        decimal ratio = 0X20 != result.ElementAt(1) ? -1 : 1;
                        //S:稳定
                        if (0x53 == result.ElementAt(0))
                        {
                            string str = encoding.GetString(result.Skip(2).Take(6).ToArray()).TrimStart();
                            var weight = Convert.ToDecimal(str.Replace(",", ".")) * 1000 * ratio;
                            var sta2 = result.Last();
                            var bit6 = sta2.GetBit(0x40);//重量溢出
                            var bit5 = sta2.GetBit(0x20);//当前在去皮模式
                            var bit4 = sta2.GetBit(0x10);//当前重量为0

                            CallbackAction(weight.ToString());
                        }
                    }


                }
                catch
                {

                }
            }
        }
    }
}
