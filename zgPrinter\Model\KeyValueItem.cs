﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgPrinter.Model
{
    /// <summary>
    /// 键值存储，用于返回前台时生成的数据
    /// </summary>
    public class KeyValueItem
    {
        public KeyValueItem() { }

        public KeyValueItem(string key, string value) {
            this.Key = key;
            this.Value = value;
        }

        public string Key { get; set; }

        public string Value { get; set; }
    }
}
