﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PrintCore
{
    public class PrintLabelModel
    {
        public string printname { get; set; }
        public double Width { get; set; } = 60d;
        public double Height { get; set; } = 60d;
        public bool Landscape { get; set; } = true;
        public float Offset { get; set; } = 0.09f;
        public int Image_Width { get; set; } = 170;
        public int Image_Height { get; set; } = 60;
        public List<PrintLabelItem> items { get; set; } = new List<PrintLabelItem>();
        public FontSize DefaultSize { get; set; } = FontSize.Normal;
        public List<LabelItem> others { get; set; } = new List<LabelItem>();
        
        public void setModel_40()
        {
            Landscape = false;
            printname = printname;
            Width = 40d;
            Height = 30d;
            Image_Width = 170;
            Image_Height = 55;
            DefaultSize = FontSize.Small;
            Offset = 0f;
        }
    }
    public class PrintLabelItem
    {
        public string name { get; set; }
        public string code { get; set; }
        public string sale_price { get; set; }
        public string commodityDate { get; set; }//生产日期
        public string commodityEndDate { get; set; }//保质期  


    }
    public class LabelItem
    {
        public string barcode { get; set; }
        public int barcode_Width { get; set; } = 170;
        public int barcode_Height { get; set; } = 60;


        public string Title { get; set; }
        public string Text { get; set; }
        public Nullable<FontSize> Size { get; set; }
        public string qrcode { get; set; }
        public double qrcode_Width { get; set; }
        public double qrcode_Height { get; set; }
        public string qrcode_logo { get; set; }
    }
}
