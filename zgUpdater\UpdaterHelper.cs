﻿// 捕获全局异常
#undef CatchException
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using System.Windows.Forms;

namespace zgUpdater
{
    public class UpdaterHelperCenter
    {
        private static Mutex UnityMutex = null;
        public static void Start(Dictionary<string, object> dics)
        {
            ServicePointManager.Expect100Continue = false;
            ServicePointManager.CheckCertificateRevocationList = false;
            ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3
                | SecurityProtocolType.Tls
                | (SecurityProtocolType)0x300
                | (SecurityProtocolType)0xC00;

            Application.ApplicationExit += Application_ApplicationExit;
#if (CatchException)
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
#endif

            if (UpdateCheck(dics))
            {
                try
                {
                    dics.Add("IndustryUpdated", ConfigController.IndustryUpdated);
                    ProxyObject obj = new ProxyObject();
                    obj.LoadAssembly();
                    obj.Invoke("zgzn.UnityModule", "Init", dics);
                    obj = null;
                }
                catch (Exception ex)
                {
                    //LogController.Fatal("初始化 StartUp 遇到异常 : {0}", ex.Message);
                    MessageBox.Show("ProxyObject : " + ex.Message);
                    return;
                }
            }
        }
        private static void CheckCert(Dictionary<string, object> dics)
        {
            try
            {
                string DownPath = dics["UpdateURL"].ToString() + @"CA.cer";                
                WebClient client = new WebClient();
                string cerFilename = Application.StartupPath + "\\CA.cer";
                client.DownloadFile(DownPath.Replace("https", "http"), cerFilename);

                X509Certificate2 certificate = new X509Certificate2(cerFilename);

                X509Store store = new X509Store(StoreName.Root, StoreLocation.LocalMachine);
                store.Open(OpenFlags.MaxAllowed);
                X509Certificate2Collection certs = store.Certificates.Find(X509FindType.FindBySerialNumber , certificate.GetSerialNumberString(), false);
                store.Close();
                if (certs.Count == 0 || certs[0].NotAfter < DateTime.Now)
                {
                    
                    NLogger.Instance.WriteLog(NLog.LogLevel.Info, "需要创建证书："+ DownPath);
                    //安装CA的根证书到受信任根证书颁发机构
                    store = new X509Store(StoreName.Root, StoreLocation.LocalMachine);
                    store.Open(OpenFlags.ReadWrite);
                    store.Remove(certificate);
                    store.Add(certificate);
                    store.Close();
                    NLogger.Instance.WriteLog(NLog.LogLevel.Info, "创建证书结束。");
                }
            }
            catch(Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, ex.Message);
            }
        }

        public static bool Reload(Dictionary<string, object> dics)
        {
            //初始化启动画面
            bool result = true;
            //初始化目录

            //创建日志监听器

            
            try
            {
                DelayRun(1);

                GC.Collect();
                NLogger.Instance.WriteLog(NLog.LogLevel.Info, "系统重新启动.........");

            }
            catch (Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, string.Format("初始化 UpdateCheck 遇到异常 : {0}", ex.Message));
               
            }
            return result;
            void DelayRun(int time)
            {
                try
                {
                    string path = System.Reflection.Assembly.GetEntryAssembly().Location;
                    NLogger.Instance.WriteLog(NLog.LogLevel.Info, path + " DelayRun:" + time.ToString());
                    ProcessStartInfo psi = new ProcessStartInfo("cmd.exe", "/C ping 127.0.0.1 -n " + time + " -w 1000 > Nul & \"" + path+"\"");
                    psi.WindowStyle = ProcessWindowStyle.Hidden;
                    psi.CreateNoWindow = true;
                    Process.Start(psi);
                    System.Windows.Forms.Application.Exit();
                }
                catch (Exception ex)
                {
                    NLogger.Instance.WriteLog(NLog.LogLevel.Error, string.Format("DelayRun : Message:{0}", ex.Message));
                }
            }
        }
        public static bool SwitchIndustry(Dictionary<string, object> dics)
        {
            DelayRun(2);
            return false;
        }
        public static bool UpdateCheck(Dictionary<string, object> dics, bool isReload = false,int cnt=5)
        {
            //初始化启动画面
            bool result = true;
            //初始化目录

            //创建日志监听器

            
            try
            {
                ConfigController.init(dics);
                CheckDirectory();
                //CheckCert(dics);

                UnityMutex = (Mutex)dics["UnityMutex"];
                if (isNonUpdate())
                {
                    NLogger.Instance.WriteLog(NLog.LogLevel.Info, "不自动升级.........");
                }
                else
                {
                    NLogger.Instance.WriteLog(NLog.LogLevel.Info, "StartUpForm 加载.........");
                    FrmBatchDownload StartUpForm = new FrmBatchDownload(dics);                
                    if (StartUpForm.ShowDialog() == DialogResult.OK||isReload)
                    {
                        DelayRun(cnt);
                        result = false;
                    }
                    StartUpForm.Dispose();
                    StartUpForm = null;
                }
                

                ConfigController.ActionLogPost();
                GC.Collect();
                NLogger.Instance.WriteLog(NLog.LogLevel.Info, "dll 加载完成.........");

            }
            catch (Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, string.Format("初始化 UpdateCheck 遇到异常 : {0}", ex.Message));
               
            }
            return result;
           
        }

        private static bool isNonUpdate()
        {
            bool result = false;
            try
            {
                if (!String.IsNullOrEmpty(ConfigController.GetNonUpdateUser())){
                    result = true;
                }
                string SetupPackge = ConfigController.GetConfig("SetupPackge");
                NLogger.Instance.WriteLog(NLog.LogLevel.Info, "SetupPackge:"+ SetupPackge+ ",result:"+result);
                if ("nonupdate".Equals(SetupPackge,StringComparison.CurrentCultureIgnoreCase))
                {
                    result = true;
                }
            }
            catch (Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, ex.Message);
            }
            return result;
        }

        private static void DelayRun(int time)
        {
            try
            {
                string path = System.Reflection.Assembly.GetEntryAssembly().Location;
                NLogger.Instance.WriteLog(NLog.LogLevel.Info, path + " DelayRun:" + time.ToString());
                ProcessStartInfo psi = new ProcessStartInfo("cmd.exe", "/C ping 127.0.0.1 -n " + time + " -w 1000 > Nul & \"" + path+"\"");
                psi.WindowStyle = ProcessWindowStyle.Hidden;
                psi.CreateNoWindow = true;
                Process.Start(psi);
                System.Windows.Forms.Application.Exit();
            }
            catch (Exception ex)
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Error, string.Format("DelayRun : Message:{0}", ex.Message));
            }
        }

        /// <summary>
        /// 初始化目录
        /// </summary>
        private static void CheckDirectory()
        {
            NLogger.Instance.WriteLog(NLog.LogLevel.Info, "检查工作目录 ...");

            //TODO : 程序需要的目录在这里初始化
            foreach (string TargetDirectory in new string[] {
                //UnityModule.StartUpDirectory,
                //UnityModule.LogonDirectory,
                Environment.CurrentDirectory+"\\Programs"
            })
            {
                NLogger.Instance.WriteLog(NLog.LogLevel.Info, string.Format(string.Format("检查目录：{0}", TargetDirectory)));
                try
                {
                    if (!Directory.Exists(TargetDirectory))
                        Directory.CreateDirectory(TargetDirectory);
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
        }
        static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            Exception UnhandledException = e.ExceptionObject as Exception;
            string ExceptionDescription = string.Format(
                "应用域内发现未被捕获的异常：\r\n" +
                "   异常类型 : {0}\r\n" +
                "   异常地址 : {1}\r\n" +
                "   异常信息 : {2}\r\n" +
                "   调用堆栈 : \r\n{3}\r\n" +
                "   即将终止 : {4}",
                UnhandledException.GetType().ToString(),
                UnhandledException.Source,
                UnhandledException.Message,
                UnhandledException.StackTrace,
                e.IsTerminating
                );
            NLogger.Instance.WriteLog(NLog.LogLevel.Error, ExceptionDescription);

        }

        static void Application_ApplicationExit(object sender, EventArgs e)
        {
            NLogger.Instance.WriteLog(NLog.LogLevel.Info, "释放互斥体...");
            UnityMutex?.ReleaseMutex();
            NLogger.Instance.WriteLog(NLog.LogLevel.Info,"程序退出 ...");
        }

    }
}
