﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgCommon.Properties
{
    public class Resources
    {
        public static Bitmap logo_title;
        public static Bitmap pc_cloud_loading;

        public static Bitmap logo { get; set; }
        public static Bitmap qrcode { get; set; }
        public static Bitmap LogoImage { get; set; }
        public static string ErrorPage { get;  set; }
        public static Bitmap top { get;  set; }
        public static Icon Icon { get; set; }
        public static Image DefaultProgramIcon { get; set; }
        public static Bitmap back_bg { get; set; }

        public static string Advert { get; set; }

        //public static Func<string, Stream> ResourceStream;
    }
}
