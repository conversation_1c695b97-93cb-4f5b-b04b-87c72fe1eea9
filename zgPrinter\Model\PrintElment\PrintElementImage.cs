﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgPrinter.Model
{
    /// <summary>
    /// 打印元素-图片，独占一行
    /// </summary>
    public class PrintElementImage : PrintElement
    {
        string _imgPath;

        public PrintElementImage() { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="index">序号</param>
        /// <param name="imgPath">图片路径</param>
        public PrintElementImage(int index, string imgPath) : this(index, new Bitmap(imgPath))
        {
            _imgPath = imgPath;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="index">序号</param>
        /// <param name="image">图片</param>
        public PrintElementImage(int index, Image image)
        {
            this.Index = index;
            this.Content = image;
            this.ContentType = EnumContentType.Image;
        }

        public new Image Content { get; set; }

        public override JObject ToJObject()
        {
            var jsonObject = new JObject();
            jsonObject.Add("index", this.Index);
            jsonObject.Add("contenttype", (int)this.ContentType);
            jsonObject.Add("textalign", (int)this.TextAlign);
            jsonObject.Add("datamember", this.DataMember);
            return jsonObject;
        }
    }
}
