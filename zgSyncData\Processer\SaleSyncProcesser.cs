﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using zgSyncData.Model;
using zgUtils.Controls;

namespace zgSyncData.Processer
{
    public class SaleSyncProcesser : BaseSyncProcesser
    {
        private RestRequest restRequest;

        public SaleSyncProcesser(string authorization):this() {
            Authorization = authorization;
        }

        public SaleSyncProcesser()
        {
            base.ProcessName = nameof(SaleSyncProcesser);
            restClient = new RestClient();
            restClient.Timeout = 1000*60;
        }

        private void downloadDataAsync(int dataCount,string startSyncAt,string endSyncAt)
        {
            Stopwatch appStopWatch = new Stopwatch();
            appStopWatch.Start();

            var downloadParm = new SyncDownloadParameter()
            {
                PageSize = this.DownloadRecordCount * 2,
                SysUid = this.SysUid,
                SysSid = this.SysSid,
                StartSyncAt = startSyncAt,
                EndSyncAt = endSyncAt
            };
            var pageCount = Math.Floor(dataCount * 1.0 / this.DownloadRecordCount * 2.0);

            SQLiteHelper.ExecuteNonQuery(@"ATTACH DATABASE 'D:\workSapce\cheng\data - 副本.db' AS dataCopy;");

            this.SetSynchronous("dataCopy", value: 0);
            SQLiteHelper.ExecuteNonQuery("delete from tmp_sales;delete from tmp_saleitems;");

            Stopwatch stopWatch = new Stopwatch();
            Stopwatch httpStopWatch = new Stopwatch();
            List<string> sqlList = new List<string>();
            for (int i = 1; i <= pageCount; i++)
            {
                httpStopWatch.Restart();
                downloadParm.CurrentPage = i;
                var downloadResp = restClient.RequestPost<ResponseData>(this.DownLoadUrl, this.Authorization, downloadParm);
                httpStopWatch.Stop();

                stopWatch.Restart();
                var saleList = downloadResp.Data.Data;
                if (saleList.Count == 0) {
                    break;
                }
                foreach (var item in saleList)
                {
                    sqlList.Add(item.ToInsertSql());
                    var itemSqls = item?.Items.Select(j => j.ToInsertSql());
                    sqlList.AddRange(itemSqls ?? new List<string>());
                }

                stopWatch.Stop();
                var ms = stopWatch.ElapsedMilliseconds;
                Console.WriteLine($"====================== {i}-执行 {downloadParm.PageSize}条数据,整理耗时{ms}毫秒,http:{httpStopWatch.ElapsedMilliseconds}");
                
                if (i % 50 == 0)
                {
                    stopWatch.Restart();
                    SQLiteHelper.ExecuteNonQueryBatch2(sqlList);
                    httpStopWatch.Stop();
                    Console.WriteLine($"*************** {i}-执行{sqlList.Count}条SQL，耗时{stopWatch.ElapsedMilliseconds}");
                    sqlList.Clear();
                }
            }
            stopWatch.Restart();
            SQLiteHelper.ExecuteNonQueryBatch2(sqlList);
            stopWatch.Stop();
            Console.WriteLine($"***************最后执行{sqlList.Count}条SQL，耗时{stopWatch.ElapsedMilliseconds}");
            //测试用
            SQLiteHelper.ExecuteNonQuery("UPDATE tmp_saleitems SET sale_fingerprint = fingerprint");

            sqlList.Clear();
            sqlList.Add(Accounts.SyncSalesAccountsUpdate);
            sqlList.Add(Sales.SyncSalesUpdate);
            sqlList.Add(Sales.SyncSalesInsert);
            sqlList.Add(SaleItem.SyncSaleitemsUpdate);
            sqlList.Add(SaleItem.SyncSaleitemsInsert);
            sqlList.Add(SaleBlendPays.SyncSaleBlendPays);
            stopWatch.Restart();
            SQLiteHelper.ExecuteNonQueryBatch2(sqlList);
            Console.WriteLine($"***************数据整理，耗时：{stopWatch.ElapsedMilliseconds}");
            stopWatch.Stop();

            appStopWatch.Stop();
            Console.WriteLine($"***************执行完成，总计耗时：{appStopWatch.ElapsedMilliseconds/1000}秒");
            SQLiteHelper.ExecuteNonQuery(@"DETACH DATABASE dataCopy;");

        }

        public override void DownLoadAsync(int dataCount, string startSyncAt, string endSyncAt)
        {
            downloadDataAsync(dataCount, startSyncAt, endSyncAt);
        }

    }
}
