﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Industries.Properties
{
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Industries.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找类似 &lt;!DOCTYPE html&gt;
        ///&lt;html style=&quot;overflow: hidden;margin: 0;padding: 0;&quot;&gt;
        ///&lt;head&gt;
        ///    &lt;title&gt;back&lt;/title&gt;
        ///    &lt;meta charset=&quot;utf-8&quot; /&gt;
        ///    &lt;style type=&quot;text/css&quot;&gt;
        ///        .slide-show p {
        ///            width: 100%;
        ///            position: absolute;
        ///            color: #fff;
        ///            background-color: #000;
        ///            opacity: 0.7;
        ///            filter: alpha(opacity=70);
        ///            bottom: 0px;
        ///            padding: 0.5vmin 0px;
        ///            margin: 0 auto;
        ///            text-align: center;
        ///        }
        /// [字符串的其余部分被截断]&quot;; 的本地化字符串。
        /// </summary>
        internal static string Advert {
            get {
                return ResourceManager.GetString("Advert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap back_bg {
            get {
                object obj = ResourceManager.GetObject("back_bg", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap DefaultProgramIcon {
            get {
                object obj = ResourceManager.GetObject("DefaultProgramIcon", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 &lt;!DOCTYPE html&gt;
        ///&lt;html&gt;
        ///&lt;head&gt;
        ///    &lt;title&gt;back&lt;/title&gt;
        ///    &lt;meta charset=&quot;utf-8&quot; /&gt;
        ///&lt;/head&gt;
        ///&lt;body&gt;
        ///    &lt;div style=&quot;position:absolute; transform: translate(-50%,-50%); left: 50%; top: 50%;&quot;&gt;
        ///        &lt;img src=&quot;data:image/png;base64,{0}&quot; /&gt;
        ///        &lt;hr /&gt;
        ///        &lt;h1 style=&quot;color: white;&quot;&gt;{1}&lt;/h1&gt;
        ///        &lt;h2 style=&quot;color:whitesmoke;&quot;&gt;{2}&lt;/h2&gt;
        ///    &lt;/div&gt;
        ///&lt;/body&gt;
        ///&lt;/html&gt; 的本地化字符串。
        /// </summary>
        internal static string ErrorPage {
            get {
                return ResourceManager.GetString("ErrorPage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似于 (图标) 的 System.Drawing.Icon 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Icon ico {
            get {
                object obj = ResourceManager.GetObject("ico", resourceCulture);
                return ((System.Drawing.Icon)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap logo {
            get {
                object obj = ResourceManager.GetObject("logo", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap LogoImage {
            get {
                object obj = ResourceManager.GetObject("LogoImage", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap qrcode {
            get {
                object obj = ResourceManager.GetObject("qrcode", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap top {
            get {
                object obj = ResourceManager.GetObject("top", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
    }
}
