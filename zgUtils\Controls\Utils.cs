﻿using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Data;
using System.Drawing.Printing;
using System.IO;
using System.Net;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using zgLogging;

namespace zgUtils.Controls
{
    public class Utils
    {
        static string encryptKey = "Oyes";    //定义密钥 

        #region 加密字符串 
        /// <summary> /// 加密字符串  
        /// </summary> 
        /// <param name="str">要加密的字符串</param> 
        /// <returns>加密后的字符串</returns> 
        public static string Encrypt(string str)
        {
            DESCryptoServiceProvider descsp = new DESCryptoServiceProvider();   //实例化加/解密类对象  

            byte[] key = Encoding.Unicode.GetBytes(encryptKey); //定义字节数组，用来存储密钥   

            byte[] data = Encoding.Unicode.GetBytes(str);//定义字节数组，用来存储要加密的字符串 

            MemoryStream MStream = new MemoryStream(); //实例化内存流对象     

            //使用内存流实例化加密流对象  
            CryptoStream CStream = new CryptoStream(MStream, descsp.CreateEncryptor(key, key), CryptoStreamMode.Write);

            CStream.Write(data, 0, data.Length);  //向加密流中写入数据     

            CStream.FlushFinalBlock();              //释放加密流     

            return Convert.ToBase64String(MStream.ToArray());//返回加密后的字符串 
        }
        #endregion

        #region 解密字符串  
        /// <summary> 
        /// 解密字符串  
        /// </summary> 
        /// <param name="str">要解密的字符串</param> 
        /// <returns>解密后的字符串</returns>   
        public static string Decrypt(string str)
        {
            DESCryptoServiceProvider descsp = new DESCryptoServiceProvider();   //实例化加/解密类对象   

            byte[] key = Encoding.Unicode.GetBytes(encryptKey); //定义字节数组，用来存储密钥   

            byte[] data = Convert.FromBase64String(str);//定义字节数组，用来存储要解密的字符串 

            MemoryStream MStream = new MemoryStream(); //实例化内存流对象     

            //使用内存流实例化解密流对象      
            CryptoStream CStream = new CryptoStream(MStream, descsp.CreateDecryptor(key, key), CryptoStreamMode.Write);

            CStream.Write(data, 0, data.Length);      //向解密流中写入数据    

            CStream.FlushFinalBlock();               //释放解密流     

            return Encoding.Unicode.GetString(MStream.ToArray());       //返回解密后的字符串 
        }
        #endregion
        //public static bool UrlIsExist(String url)
        //{
        //    if (!NetworkCenter.IsConnected) return false;
        //    System.Uri u = null;
        //    try
        //    {
        //        u = new Uri(url);
        //    }
        //    catch { return false; }
        //    bool isExist = false;
        //    System.Net.HttpWebRequest r = System.Net.HttpWebRequest.Create(u) as System.Net.HttpWebRequest;
        //    r.Method = "HEAD";
        //    try
        //    {
        //        System.Net.HttpWebResponse s = r.GetResponse() as System.Net.HttpWebResponse;
        //        if (s.StatusCode == System.Net.HttpStatusCode.OK)
        //        {
        //            isExist = true;
        //        }
        //    }
        //    catch (System.Net.WebException x)
        //    {
        //        try
        //        {
        //            isExist = ((x.Response as System.Net.HttpWebResponse).StatusCode != System.Net.HttpStatusCode.NotFound);
        //        }
        //        catch { isExist = (x.Status == System.Net.WebExceptionStatus.Success); }
        //    }
        //    return isExist;
        //}

        ////判断网络文件是否存在
        //public static bool HttpFileExist(string fileUrl)
        //{
        //    try
        //    {
        //        //创建根据网络地址的请求对象
        //        System.Net.HttpWebRequest httpWebRequest = (System.Net.HttpWebRequest)System.Net.WebRequest.CreateDefault(new Uri(fileUrl));
        //        httpWebRequest.Method = "HEAD";
        //        httpWebRequest.Timeout = 1000;
        //        //返回响应状态是否是成功比较的布尔值
        //        using (System.Net.HttpWebResponse response = (System.Net.HttpWebResponse)httpWebRequest.GetResponse())
        //        {
        //            return response.StatusCode == System.Net.HttpStatusCode.OK;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.WriterExceptionLog(fileUrl+":" +ex.Message);
        //        return false;
        //    }
        //}

        public static string getPrinters()
        {
            List<string> printerList = new List<string>();

            try
            {
                // Add list of installed printers found to the combo box.
                // The pkInstalledPrinters string will be used to provide the display string.
                foreach (string print in PrinterSettings.InstalledPrinters)
                {
                    printerList.Add(print);
                }
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }

            return string.Join(",", printerList.ToArray());
        }
        /// <summary>
        /// DataTable -> Model
        /// </summary>
        /// <typeparam name="T">数据项</typeparam>
        /// <param name="dt">DataTable</param>
        /// <returns></returns>
        public static T DataTableToModel<T>(DataTable dt) where T : new()
        {
            try
            {
                if (dt == null || dt.Rows.Count == 0)
                {
                    return default(T);
                }

                T t = new T();
                // 获取行数据
                DataRow dr = dt.Rows[0];
                // 获取栏目
                DataColumnCollection columns = dt.Columns;
                // 获得此模型的公共属性
                PropertyInfo[] propertys = t.GetType().GetProperties();
                foreach (PropertyInfo pi in propertys)
                {
                    string name = pi.Name;
                    // 检查DataTable是否包含此列    
                    if (columns.Contains(name))
                    {
                        if (!pi.CanWrite) continue;

                        object value = dr[name];
                        if (value != DBNull.Value)
                        {
                            pi.SetValue(t, value, null);
                        }
                    }
                }
                return t;
            }
            catch
            {
                throw;
            }
        }

        /// <summary>
                /// DataTable -> List
                /// </summary>
                /// <typeparam name="T">数据项</typeparam>
                /// <param name="dt">DataTable</param>
                /// <returns></returns>
        public static List<T> DataTableToList<T>(DataTable dt) where T : new()
        {
            try
            {
                if (dt == null || dt.Rows.Count == 0)
                {
                    return new List<T>();
                }

                // 定义集合
                List<T> list = new List<T>();

                // 获取栏目
                DataColumnCollection columns = dt.Columns;
                foreach (DataRow dr in dt.Rows)
                {
                    T t = new T();
                    // 获得此模型的公共属性
                    PropertyInfo[] propertys = t.GetType().GetProperties();
                    foreach (PropertyInfo pi in propertys)
                    {
                        string name = pi.Name;
                        // 检查DataTable是否包含此列    
                        if (columns.Contains(name))
                        {
                            if (!pi.CanWrite) continue;

                            object value = dr[name];
                            if (value != DBNull.Value)
                            {
                                pi.SetValue(t, value, null);
                            }
                        }
                    }
                    list.Add(t);
                }
                return list;
            }
            catch
            {
                throw;
            }
        }
        public static string UpLoadLogFile(string url, string filename = "")
        {
            string result = string.Empty;
            if (string.IsNullOrEmpty(filename))
            {
                filename = DateTime.Today.ToString("yyyyMMdd") + ".log";
            }
            string filepath = Log.LogDirectory + filename;
            UpLoadFile(url, filepath);



            return result;
        }
        public static void UpLoadDatadbFile(string url, string filepath)
        {
            UpLoadFile(url, filepath);

        }

        public static void UpLoadFile(string url, string filepath)
        {
            if (File.Exists(filepath))
            {
                try
                {
                    Console.WriteLine(NetworkCenter.Instance.HttpUploadFile(url, filepath));

                }
                catch (Exception ex)
                {
                    Log.WriterExceptionLog("日志回传异常--->" + ex.Message);
                    throw ex;
                }
            }
        }

    }
}
