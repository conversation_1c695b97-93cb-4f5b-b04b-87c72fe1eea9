﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Web.Script.Serialization;

namespace ESCPOS.Printer
{
    public static class PrinterOnlineFactory
    {
        public static void PrintNomal(object data)
        {
            
            string jsonstr = "";
            JavaScriptSerializer js = new JavaScriptSerializer();

            if (data.GetType().Name.Equals("String"))
            {
                string pattern = @"(\\[^bfrnt\\/'\""])";
                jsonstr = System.Text.RegularExpressions.Regex.Replace(Convert.ToString(data), pattern, "\\$1");
            }
            else
            {
                System.IO.StringWriter stringWriter = new System.IO.StringWriter();

                jsonstr = js.Serialize(data);
            }
            
            PrintOnlineClass printNomalClass = js.Deserialize<PrintOnlineClass>(jsonstr);

            printNomalClass.print();
        }

    }
    public class PrintOnlineClass
    {
        public string printername = "";//打印机名字
        public string cols = "";//打印纸张宽度（32，48，64）

        public string storename = "";//标题
        public string pay_type = "";//支付方式
        // --------------------------------------------------------------------
        public string customer_remark = "";//买家备注
        // --------------------------------------------------------------------
        public string createtime = "";//交易时间
        public string orderid = "";//订单编号
        // --------------------------------------------------------------------
        public string shipping_type = "";//配送方式 （”门店自提“ ”商家配送“）
        public string customer_name = "";//买家姓名
        public string customer_mobile = "";//买家手机
        public string shipping_address_title = "";//配送地址（”提货点”，”收货地址”）
        public string shipping_address = "";//配送地址（提货点，收货地址）
        // --------------------------------------------------------------------
        public List<GoodItem> goods = new List<GoodItem>();
        // --------------------------------------------------------------------
        public string pay_amt = "";//应付
        public string accts = "";//付款方式（”会员支付“ ”货到付款“)
        public string actual_amt = "";//会员实际支付金额 
        // --------------------------------------------------------------------
        //打印时间
        public string remark = "";//系统由掌柜智囊提供

        public void print() {
            POSPrinter printer = new POSPrinter(this.printername);
            if (!this.cols.Equals("")) printer.Cols = int.Parse(this.cols);
            printer.PrintOnline(this);

        }
        

    }
}
