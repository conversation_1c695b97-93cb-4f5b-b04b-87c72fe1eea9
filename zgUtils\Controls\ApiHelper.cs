﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.Security.Authentication;
using System.Web;
using zgLogging;

namespace zgUtils.Controls
{
    public class ApiHelper
    {
        #region HttpClient版本
        private static readonly HttpClient _httpClient;
        private static readonly HttpClient _fileClient;

        static ApiHelper()
        {
            ServicePointManager.Expect100Continue = false;
            _httpClient = new HttpClient();
            _fileClient = new HttpClient();

        }
        /// <summary>
        /// 初始化和预热
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <typeparam name="TResult"></typeparam>
        /// <param name="req"></param>
        /// <returns></returns>
        public static void HttpClientHot(string _baseAddress)
        {

            try
            {
                #region 初始化和预热 httpClient
                
                _httpClient.BaseAddress = new Uri(_baseAddress);
                _httpClient.Timeout = TimeSpan.FromMilliseconds(10000);
                _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");//application/xml  想Accept的数据格式
                _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("ContentType", "application /json; charset=UTF-8");
                _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", CommonApp.Authorization);
                if (CommonApp.userinfo?.uid > 0) _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("uid", CommonApp.userinfo.uid.ToString());

                _httpClient.SendAsync(new HttpRequestMessage
                {
                    Method = new HttpMethod("HEAD"),
                    RequestUri = new Uri(_baseAddress + "/actuator/health")
                }).Result.EnsureSuccessStatusCode();

                #endregion


                #region 初始化和预热 fileClient


                _fileClient.BaseAddress = new Uri(_baseAddress + "/actuator/health");
                _fileClient.MaxResponseContentBufferSize = 256000;


                #endregion
            }
            catch (Exception ex)
            {
                zgLogging.Log.Error(ex.Message);
            }

        }
        /// <summary>
        /// http Get请求
        /// </summary>
        /// <typeparam name="T">入参类型</typeparam>
        /// <typeparam name="TResult">出参类型</typeparam>
        /// <param name="req">入参对象</param>
        /// <returns></returns>
        public static async Task<Resp<TResult>> GetAsync<T, TResult>(Req<T> req)
        {
            try
            {
                var result = await _httpClient.GetAsync(req.Url).Result.Content.ReadAsStringAsync();
                return new Resp<TResult>() { RespData = JsonConvert.DeserializeObject<TResult>(result) };
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }
            return new Resp<TResult>() { RespData = JsonConvert.DeserializeObject<TResult>("") };

        }

        /// <summary>
        ///  http Post请求
        /// </summary>
        /// <typeparam name="T">入参类型</typeparam>
        /// <typeparam name="TResult">出参类型</typeparam>
        /// <param name="req">入参对象</param>
        /// <returns></returns>
        public static async Task<Resp<TResult>> PostAsJsonAsync<T, TResult>(Req<T> req)
        {
            _httpClient.DefaultRequestHeaders.Remove("Authorization");
            _httpClient.DefaultRequestHeaders.Remove("ContentType");
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("ContentType", "application /json; charset=UTF-8");
            _httpClient.DefaultRequestHeaders.Add("Authorization", CommonApp.Authorization);
            //var result = await PostAsJsonAsync(_httpClient,req.Url, JsonConvert.DeserializeObject(req.Input)).Result.Content.ReadAsStringAsync();
            //var json = JsonConvert.SerializeObject(model);
            var stringContent = new StringContent(req.Input, Encoding.UTF8, "application/json");
            var result = await _httpClient.PostAsync(req.Url, stringContent).Result.Content.ReadAsStringAsync();

            return new Resp<TResult>() { RespData = JsonConvert.DeserializeObject<TResult>(result) };
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <typeparam name="TResult"></typeparam>
        /// <param name="req"></param>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static async Task<Resp<TResult>> SendFile<T, TResult>(Req<T> req, string filePath)//D:\\white.jpg
        {
            //_fileClient.DefaultRequestHeaders.Add("user-agent", "User-Agent    Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; Touch; MALNJS; rv:11.0) like Gecko");//设置请求头
            // 读文件流
            FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            HttpContent fileContent = new StreamContent(fs);//为文件流提供的HTTP容器
            fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse("multipart/form-data");//设置媒体类型
            MultipartFormDataContent mulContent = new MultipartFormDataContent("----");//创建用于可传递文件的容器
            string fileName = filePath.Substring(filePath.LastIndexOf("/") + 1);
            mulContent.Add(fileContent, "form", fileName);//第二个参数是表单名，第三个是文件名。
            HttpResponseMessage response = await _fileClient.PostAsync(req.Url, mulContent);
            response.EnsureSuccessStatusCode();
            string result = await response.Content.ReadAsStringAsync();
            return new Resp<TResult>() { RespData = JsonConvert.DeserializeObject<TResult>(result) };
        }

        /// <summary>
        /// 下载
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public static async Task<Resp<byte[]>> HttpDownloadData<T>(Req<T> req)
        {
            var byteres = await _fileClient.GetByteArrayAsync(req.Url);
            return new Resp<byte[]>() { RespData = byteres };
        }


        #endregion
    }
    public class Req<T>
    {
        public Req(string _url, string _input)
        {
            Url = _url;
            Input = _input;
        }

        /// <summary>
        /// 传入数据
        /// </summary>
        public string Input { get; set; }
        /// <summary>
        /// 请求地址
        /// </summary>
        public string Url { get; set; }
    }
    public class Resp<T>
    {
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMsg { get; set; }
        /// <summary>
        /// 状态码
        /// </summary>
        public int StatusCode { get; set; }
        /// <summary>
        /// 返回数据
        /// </summary>
        public T RespData { get; set; }
    }
}
