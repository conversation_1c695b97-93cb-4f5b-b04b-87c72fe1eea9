﻿using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Remoting.Messaging;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using System.Xml.Serialization;
using zgPrinter.Model.ZgLabel;
using ZXing;

namespace zgPrinter
{
    public static class ExtendMethod
    {
        #region FetchDescription 
        /// <summary> 
        /// 获取枚举值的描述文本 
        /// </summary> 
        /// <param name="value"></param> 
        /// <returns></returns> 
        public static string FetchDescription(this Enum value)
        {
            FieldInfo fi = value.GetType().GetField(value.ToString());
            DescriptionAttribute[] attributes =
               (DescriptionAttribute[])fi.GetCustomAttributes(
               typeof(DescriptionAttribute), false);

            return (attributes.Length > 0) ? attributes[0].Description : value.ToString();
        }


        #endregion

        public static byte ToByte(this char c)
        {
            return (byte)c;
        }

        public static byte ToByte(this Enum c)
        {
            return (byte)Convert.ToInt16(c);
        }

        public static byte ToByte(this short c)
        {
            return (byte)c;
        }

        public static byte[] AddBytes(this byte[] bytes, byte[] addBytes)
        {
            if (addBytes == null)
                return bytes;

            var list = new List<byte>();
            list.AddRange(bytes);
            list.AddRange(addBytes);
            return list.ToArray();
        }

        public static byte[] AddBytes(this byte[] bytes, string value)
        {
            if (string.IsNullOrEmpty(value))
                return bytes;

            var list = new List<byte>();
            list.AddRange(bytes);
            list.AddRange(Encoding.GetEncoding(850).GetBytes(value));
            return list.ToArray();
        }

        public static byte[] AddLF(this byte[] bytes)
        {
            return bytes.AddBytes("\n");
        }

        public static byte[] AddCrLF(this byte[] bytes)
        {
            return bytes.AddBytes("\r\n");
        }

        public static bool IsNullOrEmpty(this string value)
        {
            return string.IsNullOrEmpty(value);
        }

        public static string JObjElementStrValue(this JToken obj, string key,string defaultValue = "")
        {
            var jobj = (JObject)obj;
            var result = jobj.Value<string>(key) ?? defaultValue;
            return result;
        }

        /// <summary>
        /// 计算内容，右侧空格补齐
        /// </summary>
        /// <param name="content"></param>
        /// <param name="rowWidth"></param>
        /// <param name="columnWidth"></param>
        /// <returns></returns>
        public static string rigthPadContent(this string content, int rowWidth, double columnWidth, Encoding encoding = null)
        {
            encoding = encoding ?? System.Text.Encoding.Default;
            var currentLength = encoding.GetBytes(content).Length;
            var maxWidth = Math.Floor(rowWidth * columnWidth);
            if (currentLength >= maxWidth)
            {
                return content;
            }
            StringBuilder sb = new StringBuilder(content);
            for (int i = 0; i < maxWidth - currentLength; i++)
            {
                sb.Append(' ');
            }
            return sb.ToString();
        }

        public static T GetAttributeValue<T>(this IEnumerable<XAttribute> xmlAttribute,string attributeName,T defaultValue) {
            var attr = xmlAttribute.FirstOrDefault(a => a.Name.Equals(attributeName));
            if (attr != null)
            {
                var result = (T)Convert.ChangeType(attr.Value, typeof(T));
                return result;
            }
            else {
                return defaultValue;
            }
        }

        public static T ConvertXmlNode<T>(this XNode node) {
            
            XmlSerializer serializer = new XmlSerializer(typeof(T));
            var result = (T)Convert.ChangeType(serializer.Deserialize(node.CreateReader()), typeof(T));
            return default;
        }

        public static ZgLabelItem ConvertXmlElement(this XElement node)
        {
            ZgLabelItem item = new ZgLabelItem()
            {
                Type = node.Name.ToString()
            };
            
            var attrs = node.Attributes();
            item.Text = attrs.GetAttributeValue("TEXT",item.Text);
            item.FontSize = attrs.GetAttributeValue("FSIZE", item.FontSize);
            item.X = attrs.GetAttributeValue("X", item.X);
            item.Y = attrs.GetAttributeValue("Y", item.Y);
            item.Display = attrs.GetAttributeValue("DISPLAY", item.Display);
            item.DataMember = attrs.GetAttributeValue("BIND", item.DataMember);
            return item;
        }
    }
}
