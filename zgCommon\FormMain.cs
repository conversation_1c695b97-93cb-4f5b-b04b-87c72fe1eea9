﻿using CefSharp;
using System;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using zgLogging;
using zgpos.Browser;
using zgUtils;
using zgUtils.Controls;
using zgUtils.Model;

namespace zgpos
{

    public partial class FormMain : Form
    {
        
        public FormMain()
        {
            Log.WriterNormalLog("Application Start..............................FormMain");

            BeforeInit();
            CheckForIllegalCrossThreadCalls = false;
            InitializeComponent();
            Icon = zgCommon.Properties.Resources.Icon;
            logo.BackgroundImage = zgCommon.Properties.Resources.LogoImage;
            BackgroundImage = zgCommon.Properties.Resources.top;
            var SystemTitle = CommonApp.systemTitle;
            if (!string.IsNullOrEmpty(SystemTitle)) Text = SystemTitle;

            int x = Screen.PrimaryScreen.WorkingArea.Size.Width;
            int y = Screen.PrimaryScreen.WorkingArea.Size.Height;
            Size = new Size(x, y);//设置窗体大小;
            Location = new Point(0, 0);//设置窗体位置;            
            //设置logo位置
            int xLogo = (int)(0.5 * (x - logo.Width));
            int yLogo = (int)(0.4 * (y - logo.Height));
            logo.Location = new Point(xLogo, yLogo);
            try
            {
                CommonApp.secondaryScreen = Screen.AllScreens
                .Where(s => s.Primary == false)
                .FirstOrDefault();
            }
            catch (Exception ex)
            {
                Log.WriterNormalLog(ex.Message);
            }
        }

        internal void CefShowed()
        {
            panel1.Dock = DockStyle.Fill;
            //ParentForm.Hide();
        }

        /**//*淡入窗体*/
        private void FormMain_Load(object sender, EventArgs e)
        {
            try
            {
                Log.WriterNormalLog("FormMain_Load");

                //注册热键Ctrl+Shift+Alt(7)，这里的1000就是一个ID识别
                UnregisterHotKey(Handle, 1000);
                UnregisterHotKey(Handle, 1001);
                UnregisterHotKey(Handle, 1002);
                RegisterHotKey(Handle, 1000, 7, Keys.C);//关闭副屏
                RegisterHotKey(Handle, 1001, 7, Keys.D);//上传data.db
                RegisterHotKey(Handle, 1002, 7, Keys.L);//上传当日log


                this.InvokeOnUiThreadIfRequired(() => {
                    if (Owner != null) Owner.Hide();
                    panel1.Controls.Clear();
                    panel1.Controls.Add(App.initBrowser(this));
                    if (CommonApp.secondaryScreen != null) this.InvokeOnUiThreadIfRequired(() =>
                    {
                        CommonApp.frmBack = new FormBack(CommonApp.secondaryScreen);
                        CommonApp.frmBack.Show();
                    });
                });
            }
            catch (Exception exception)
            {
                Log.WriterExceptionLog(exception.Message);
            }

        }
        private void BeforeInit()
        {
            Log.WriterNormalLog("zgSQLite.dll静态资源文件拷贝开始...");
            string zgSQLitePath = string.Format(@"{0}\Programs\Libs\zgSQLite.dll", Environment.CurrentDirectory);
            Assembly zgSQLiteAssembly = Assembly.LoadFrom(zgSQLitePath);
            string[] resourceNames = zgSQLiteAssembly.GetManifestResourceNames();
            string fileDirectory = string.Format(@"{0}\Programs\Resources\", Environment.CurrentDirectory);;
            string csvDirectory = string.Format(@"{0}sync-csv\", fileDirectory);
            if (!Directory.Exists(csvDirectory))
            {
                Directory.CreateDirectory(csvDirectory);
            }
            foreach (string resourceName in resourceNames)
            {
                if (!resourceName.StartsWith("zgSQLite.Resources"))
                {
                    continue;
                }

                using (Stream resourceStream = zgSQLiteAssembly.GetManifestResourceStream(resourceName))
                {
                    string path = resourceName.Replace("zgSQLite.Resources.", "");
                    string file = fileDirectory + path.Substring(0, path.LastIndexOf(".")).Replace(".", "\\") + path.Substring(path.LastIndexOf("."));
                    FileInfo fileInfo = new FileInfo(file);
                    if (!Directory.Exists(fileInfo.DirectoryName))
                    {
                        Directory.CreateDirectory(fileInfo.DirectoryName);
                    }
                    using (FileStream fileStream = new FileStream(file, FileMode.Create))
                    {
                        resourceStream.CopyTo(fileStream);
                    }
                }
            }
            Log.WriterNormalLog("zgSQLite.dll静态资源文件拷贝结束...");

            //放在开始调用 start
            CommonApp.step = CommonApp.Setp.Step_0;
            ConfigManager.LoadConfig();
            CommonApp.FirstLogin = string.IsNullOrEmpty(CommonApp.sysUid);
        }
        private void FormMain_FormClosing(object sender, FormClosingEventArgs e)
        {
            Log.WriterNormalLog("FormMain_FormClosing..............................");
            if (!ControlBox)
            {
                e.Cancel = true;
                return;
            }
            if (!CommonApp.isClose)
            {
                Log.WriterNormalLog("showTipsDialog");
                Notify notify = new Notify();
                JavascriptResponse result = (JavascriptResponse)notify.showTipsDialog();
                if (result!=null&&(bool)(result?.Success))
                {
                    e.Cancel = true;
                    return;
                }
            }
            try
            {
                //用来取消注册的热键
                UnregisterHotKey(Handle, 1000);
                UnregisterHotKey(Handle, 1001);
                UnregisterHotKey(Handle, 1002);
                App.browserShowObj?.browser.Dispose();
                App.browserShowObj.browser = null;
                if (Owner!=null)Owner.Visible = true;
                //Configs.AnimateWindow(Handle, 1000, Configs.AW_SLIDE | Configs.AW_HIDE | Configs.AW_BLEND);
            }
            catch (Exception ex)
            {
                Log.WriterNormalLog("FormShow_FormClosing:" + ex.Message);
            }
        }

        private void FormMain_FormClosed(object sender, FormClosedEventArgs e)
        {
            CommonApp.initialize();
            SQLiteHelper.Exit();
            Log.WriterNormalLog("FormMain_FormClosed..................................");
        }

        public const int WM_DEVICECHANGE = 0x219;
        public const int DBT_DEVICEARRIVAL = 0x8000;
        public const int DBT_CONFIGCHANGECANCELED = 0x0019;
        public const int DBT_CONFIGCHANGED = 0x0018;
        public const int DBT_CUSTOMEVENT = 0x8006;
        public const int DBT_DEVICEQUERYREMOVE = 0x8001;
        public const int DBT_DEVICEQUERYREMOVEFAILED = 0x8002;
        public const int DBT_DEVICEREMOVECOMPLETE = 0x8004;
        public const int DBT_DEVICEREMOVEPENDING = 0x8003;
        public const int DBT_DEVICETYPESPECIFIC = 0x8005;
        public const int DBT_DEVNODES_CHANGED = 0x0007;
        public const int DBT_QUERYCHANGECONFIG = 0x0017;
        public const int DBT_USERDEFINED = 0xFFFF;

        public bool AutoUpdateIsUsed { get; private set; } = false;

        protected override void WndProc(ref Message m)
        {
            try
            {
                switch (m.Msg)
                {
                    case 0x0312:                                 //这个是window消息定义的注册的热键消息     
                        try
                        {
                            if (m.WParam.ToString().Equals("1000"))
                            {
                                if (App.browserBackObj != null) App.browserBackObj.CloseAll();
                            }
                            if (m.WParam.ToString().Equals("1001"))
                            {
                                Utils.UpLoadDatadbFile(string.Format("{0}/upload/uploadFile", CommonApp.Config.ServerUrl.USERURL), SQLiteHelper.FullPath);
                            }
                            if (m.WParam.ToString().Equals("1002"))
                            {
                                Utils.UpLoadLogFile(string.Format("{0}/upload/uploadFile", CommonApp.Config.ServerUrl.USERURL));
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.WriterExceptionLog(ex.Message);
                        }
                        break;
                    case WM_DEVICECHANGE:
                        switch (m.WParam.ToInt32())
                        {
                            case WM_DEVICECHANGE:
                                break;
                            case DBT_DEVICEARRIVAL:
                                break;
                            case DBT_CONFIGCHANGECANCELED:
                                break;
                            case DBT_CONFIGCHANGED:
                                break;
                            case DBT_CUSTOMEVENT:
                                break;
                            case DBT_DEVICEQUERYREMOVE:
                                break;
                            case DBT_DEVICEQUERYREMOVEFAILED:
                                break;
                            case DBT_DEVICEREMOVECOMPLETE:
                                break;
                            case DBT_DEVICEREMOVEPENDING:
                                break;
                            case DBT_DEVICETYPESPECIFIC:
                                break;
                            case DBT_DEVNODES_CHANGED:
                                string useCardReader = "0";
                                if (CommonApp.settings != null
                                    && CommonApp.settings.setting != null
                                    && string.IsNullOrEmpty(CommonApp.settings.setting.useCardReader)
                                    ) useCardReader = CommonApp.settings.setting.useCardReader;
                                zgReadCard.ReadCard.OpenClose(App.ReadCard_OnValueChanged, useCardReader);
                                break;
                            case DBT_QUERYCHANGECONFIG:
                                break;
                            case DBT_USERDEFINED:
                                break;
                            default:
                                break;
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            base.WndProc(ref m);
        }

        /// <summary>
        /// 注册热键
        　/// </summary>
        /// <param name="hWnd">为窗口句柄</param>
        /// <param name="id">注册的热键识别ID</param>
        /// <param name="control">组合键代码  Alt的值为1，Ctrl的值为2，Shift的值为4，Shift+Alt组合键为5
        ///  Shift+Alt+Ctrl组合键为7，Windows键的值为8
        /// </param>
        /// <param name="vk">按键枚举</param>
        /// <returns></returns>
        [DllImport("user32")]
        public static extern bool RegisterHotKey(IntPtr hWnd, int id, uint control, Keys vk);

        /// <summary>
        /// 取消注册的热键
        /// </summary>
        /// <param name="hWnd">窗口句柄</param>
        /// <param name="id">注册的热键id</param>
        /// <returns></returns>
        [DllImport("user32")]
        public static extern bool UnregisterHotKey(IntPtr hWnd, int id);

    }
}
