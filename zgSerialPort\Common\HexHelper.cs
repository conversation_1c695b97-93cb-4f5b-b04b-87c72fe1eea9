﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgSerialPort.Common
{
    public static class HexHelper
    {
        /// <summary>
        /// byte[]转为16进制字符串
        /// </summary>
        /// <param name="bytes"></param>
        /// <returns></returns>
        public static string ByteToHexStr(byte[] bytes)
        {
            StringBuilder ret = new StringBuilder();
            foreach (byte b in bytes)
            {
                //{0:X2} 大写
                ret.AppendFormat("{0:x2}", b).Append(" ");
            }
            var hex = ret.ToString();

            return hex;
        }

        /// <summary>
        /// 16进制字符串转字符串
        /// </summary>
        /// <param name="hex"></param>
        /// <returns></returns>
        public static string GetChsFromHex(string hex, System.Text.Encoding encoding = null)
        {
            if (hex == null)
            {
                throw new ArgumentNullException("hex");
            }
            if (hex.Length % 2 != 0)
            {
                hex = "20";//空格
                throw new ArgumentException("hex is not a valid number!", "hex");
            }

            if (encoding == null) {
                encoding= Encoding.UTF8;
            }

            // 需要将 hex 转换成 byte 数组。
            byte[] bytes = new byte[hex.Length / 2];
            for (int i = 0; i < bytes.Length; i++)
            {
                try
                {
                    // 每两个字符是一个 byte。
                    bytes[i] = byte.Parse(hex.Substring(i * 2, 2),
                        System.Globalization.NumberStyles.HexNumber);
                }
                catch
                {
                    // Rethrow an exception with custom message.
                    throw new ArgumentException("hex is not a valid hex number!", "hex");
                }
            }
            System.Text.Encoding chs = encoding;
            return chs.GetString(bytes);
        }

        public static int GetBit(this byte b, byte findByte)
        {
            var result = (b & findByte) == findByte ? 1 : 0;
            return result;
        }
    }
}
