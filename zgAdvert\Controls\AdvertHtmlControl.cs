﻿using zgAdvert.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Timers;
using System.Windows.Forms;
using zgUtils.Controls;
using CefSharp;
using zgUtils;
using zgLogging;

namespace zgAdvert.Controls
{
	public partial class AdvertHtmlControl : UserControl
	{
		private List<ADItem> items;
		private int currntDurtion;
		private int index;
		private bool isRun;
        
        //public string showad { get; set; } = "0";
        
        public AdvertHtmlControl()
		{
			CheckForIllegalCrossThreadCalls = false;
			
			InitializeComponent();
			this.BackgroundImage = AdvertFactory.back_bg;

			this.items = new List<ADItem>();
			this.timer = new System.Timers.Timer();
			this.timer.Interval = 1000.0;
			this.timer.Elapsed += Timer_Elapsed;
			//Dock = DockStyle.Fill;
			
		}
		public void Init()
		{
			try
			{
				Log.WriterNormalLog("广告位没有初始化,开始初始化广告位");

				if (!this.isRun)
				{
					Log.WriterNormalLog("加载广告UI组件成功");
					this.InitAds();
					this.isRun = true;
					return;
				}
				Log.WriterNormalLog("广告位已经初始化,init 方法被重复调用,自动过滤UI生成,只刷新广告");
				this.InitAds();

			}
			catch (Exception ex)
			{
				Log.WriterExceptionLog(ex.Message);
			}
		}
		public void InitAds()
		{
			this.items = new List<ADItem>();
			this.updateAds(AdvertFactory.ADItems_3);
			//AdvertFactory.Instance.GetAds(delegate (List<ADItem> aditems)
			//{
			//	this.updateAds(aditems);
			//});			
		}
		
		private void updateAds(List<ADItem> ads)
		{
			Log.WriterNormalLog("更新广告："+ this.index);
            this.items.Clear();

			if (ads != null && ads.Count > 0)
			{
				foreach (ADItem aditem in ads)
				{
					if (AdvertFactory.isShowAd && aditem.IsDefault == 0 || !AdvertFactory.isShowAd && aditem.IsDefault == 1)
					{
						this.items.Add(aditem);
					}
				}

			}
			else {
				ads = null;

            }
            if (this.items.Count > 0)
            {
				this.index = 0;
				this.currntDurtion = 0;
				this.InitLoad(this.index);
                this.timer.Start();
                this.chromiumWebBrowser1.Dock = DockStyle.Fill;
            }
            else
            {
                this.timer.Stop();
                this.chromiumWebBrowser1.Dock = DockStyle.None;
            }

        }
		
		public void AdControlDispose()
		{
			Log.WriterNormalLog("外部调用释放广告位组件");
			try
			{
				Log.WriterNormalLog("释放广告组件");
				this.isRun = false;
				this.timer.Stop();
                this.items.Clear();
                //if(chromiumWebBrowser1.IsBrowserInitialized)chromiumWebBrowser1.GetBrowser().CloseBrowser(true);
                //this.chromiumWebBrowser1.Dispose();
                //this.Dispose();
                Log.WriterNormalLog("广告组件释放成功");
			}
			catch (Exception ex)
			{
				Log.WriterExceptionLog("释放广告组件异常---->" + ex.Message);
			}
		}
		private void Timer_Elapsed(object sender, ElapsedEventArgs e)
		{
            if (!this.IsHandleCreated || !this.timer.Enabled)
            {
                return;
            }
			base.Invoke(new Action(delegate ()
			{
				try
				{
					if (this.items.Count > 0)
					{
                        if (this.items[this.index].duration > 0 && this.currntDurtion / this.items[this.index].duration > 0)
                        {
                            this.NextPlay();
                        }
                        if (this.items[this.index].MaterialType == "VDO" || this.items[this.index].MaterialType == "IMG")
						{
							if (!Files.FileManager.Instance.IsFileExist(this.items[this.index]))
							{
								this.currntDurtion = 0;
								this.index++;
								if (this.index / this.items.Count > 0)
								{
									this.index = 0;
								}
								return;
							}

						}
						this.currntDurtion++;
						
					}
				}
				catch (Exception ex)
				{
					Log.WriterExceptionLog(ex.Message);
				}
			}));
		}
		private void NextPlay()
		{
			//string showad = "0";
			//try
			//{
			//	showad = this.showad;
			//}
			//catch { }
			this.currntDurtion = 0;
			this.index++;
			if (this.items.Count == 0)
			{
				this.index = 0;
				this.timer.Stop();
				return;
			}
			if (this.index / this.items.Count > 0)
			{
				this.index = 0;
			}
			this.InitLoad(this.index);
		}
		private void InitLoad(int index)
		{
			if (this.items != null && this.items.Count > 0)
			{
				ADItem item = this.items[index];
				if (Files.FileManager.Instance.IsFileExist(item))
				{
					//Log.WriterNormalLog("轮播切换广告--->" + item.ID);
					if (item.MaterialType.Equals("IMG"))
					{
						this.PlayImage(item);
						return;
					}

				}
			}
		}
		private void PlayImage(ADItem item)
		{
			
			string filename = Path.Combine(AdvertFactory.CacheDir,item.location.ToString(), item.img.Remove(0, item.img.LastIndexOf("/") + 1));
			chromiumWebBrowser1.LoadHtml(item.html);
			

		}

        private void chromiumWebBrowser1_FrameLoadEnd(object sender, FrameLoadEndEventArgs e)
        {
			chromiumWebBrowser1.Dock = DockStyle.Fill;
			//chromiumWebBrowser1.GetBrowser().ShowDevTools();
		}

        private void chromiumWebBrowser1_FrameLoadStart(object sender, FrameLoadStartEventArgs e)
        {
            object resourceHandlerFactory = chromiumWebBrowser1.ResourceRequestHandlerFactory;

        }
    }
}
