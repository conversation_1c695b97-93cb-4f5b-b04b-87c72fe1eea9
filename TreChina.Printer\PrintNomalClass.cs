﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Web.Script.Serialization;
using PrintCore;

namespace ESCPOS.Printer
{
    public static class PrinterNomalFactory
    {
        public static void PrintNomal(object data)
        {
            
            string jsonstr = "";
            JavaScriptSerializer js = new JavaScriptSerializer();

            if (data.GetType().Name.Equals("String"))
            {
                string pattern = @"(\\[^bfrnt\\/'\""])";
                jsonstr = System.Text.RegularExpressions.Regex.Replace(Convert.ToString(data), pattern, "\\$1");
            }
            else
            {
                jsonstr = js.Serialize(data);
            }
            
            PrintNomalClass printNomalClass = js.Deserialize<PrintNomalClass>(jsonstr);

            printNomalClass.print();
        }

        public static void PrintCommon(object data,string logoPath)
        {
            string jsonstr = "";
            JavaScriptSerializer js = new JavaScriptSerializer();

            if (data.GetType().Name.Equals("String"))
            {
                string pattern = @"(\\[^bfrnt\\/'\""])";
                jsonstr = System.Text.RegularExpressions.Regex.Replace(Convert.ToString(data), pattern, "\\$1");
            }
            else
            {
                jsonstr = js.Serialize(data);
            }

            PrintCommonClass printCommonClass = js.Deserialize<PrintCommonClass>(jsonstr);
            switch (printCommonClass.printMode)
            {
                case 0:
                    if (string.IsNullOrEmpty(printCommonClass.logo) && string.IsNullOrEmpty(printCommonClass.qrcode))
                    {
                        printCommonClass.print();

                    }
                    else {
                        PrinterFactory.PrintPOS(printCommonClass, logoPath);
                    }
                    break;
                case 1://指令
                    printCommonClass.print(logoPath);
                    break;
                case 2://驱动
                    PrinterFactory.PrintPOS(printCommonClass, logoPath);
                    break;
                default:
                    printCommonClass.print();
                    break;
            }
            

            
            //PrinterFactory.PrintPOS(printCommonClass,logoPath);
            
        }
        public static void PrintTest(object data)
        {

            string jsonstr = "";
            JavaScriptSerializer js = new JavaScriptSerializer();

            if (data.GetType().Name.Equals("String"))
            {
                string pattern = @"(\\[^bfrnt\\/'\""])";
                jsonstr = System.Text.RegularExpressions.Regex.Replace(Convert.ToString(data), pattern, "\\$1");
            }
            else
            {
                jsonstr = js.Serialize(data);
            }

            PrintCommonClass printCommonClass = js.Deserialize<PrintCommonClass>(jsonstr);

            printCommonClass.test();
        }
    }
    public class PrintNomalClass
    {
        private string printername = "";//打印机名字
        public string cols = "";//打印纸张宽度（32，48，64）
        public string orderid = "";//订单编号
        public string storename = "";//标题
        public string operater = "";//操作员
        public string accts = "";//付款方式
        public string pay_amt = "";//实收款
        public string change_amt = "";//找零
        public string createtime = "";//交易时间
        public string remark = "";//提供方电话
        public string member_money_name = "";//会员余额
        public string member_money_value = "";//会员余额
        public string member_mobile_name = "";//会员手机
        public string member_mobile_value = "";//会员手机
        public List<GoodItem> goods = new List<GoodItem>();
        public List<Dictionary<string, object>> groups = new List<Dictionary<string, object>>();

        public void print() {
            POSPrinter printer = new POSPrinter(this.printername);
            if (!this.cols.Equals("")) printer.Cols = int.Parse(this.cols);
            printer.PrintNomal(this);

        }
       

    }
    public class PrintCommonClass
    {
        public string printername = "";//打印机名字
        public string cols = "";//打印纸张宽度（32，48，64）
        public string orderid = "";//订单编号
        public string storename = "";//标题
        public int lineheight = 60;
        public int printMode = 0;
        public string size = "";
        public List<Dictionary<string, object>> groups = new List<Dictionary<string, object>>();

        public string logo { get;  set; }
        public string qrcode { get;  set; }

        public void print(string logoPath="")
        {
            POSPrinter printer = new POSPrinter(this.printername);
            if (!string.IsNullOrEmpty(this.cols)) printer.Cols = int.Parse(this.cols);

            if(!string.IsNullOrEmpty(logoPath)) PrintCore.PrinterFactory.PrintLogo(this.printername, int.Parse(this.cols), this.logo, logoPath);
            printer.PrintCommon(this);
            if (!string.IsNullOrEmpty(logoPath)) PrintCore.PrinterFactory.PrintQrcode(this.printername, int.Parse(this.cols), this.qrcode, logoPath);


        }
        public void test()
        {
            POSPrinter printer = new POSPrinter(this.printername);
            if (!this.cols.Equals("")) printer.Cols = int.Parse(this.cols);
            printer.PrintTest(this);

        }


    }
    public class GoodItem {
        public string good_name;
        public string price;
        public string qty;
        public string name;
        public string sale_price;
        public string number;

    }
}
