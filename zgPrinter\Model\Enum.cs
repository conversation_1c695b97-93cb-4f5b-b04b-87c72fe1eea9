﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgPrinter.Model
{
    /// <summary>
    /// 文本浮动方向
    /// </summary>
    public enum EnumTextAlign
    {
        /// <summary>
        /// 居左
        /// </summary>
        [Description("居左")]
        Left = 16,

        /// <summary>
        /// 居中
        /// </summary>
        [Description("居中")]
        Center = 32,

        /// <summary>
        /// 居右
        /// </summary>
        [Description("居右")]
        Right = 64
    }

    /// <summary>
    /// 内容类型
    /// </summary>
    public enum EnumContentType
    {
        /// <summary>
        /// 文本块，宽度自适应，可以设置便宜量
        /// </summary>
        [Description("文本块")]
        StringBlock = 0,

        /// <summary>
        /// 字符串,占一行，不会偏移
        /// </summary>
        [Description("文本行")]
        String = 1,

        /// <summary>
        /// 表格
        /// </summary>
        [Description("表格")]
        Table = 2,

        /// <summary>
        /// 表格
        /// </summary>
        [Description("商品表格")]
        GoodsTable = 3,

        /// <summary>
        /// 图片
        /// </summary>
        [Description("图片")]
        Image = 4,

        /// <summary>
        /// 图片
        /// </summary>
        [Description("图片块")]
        ImageBlock = 6,

        /// <summary>
        /// 指令
        /// </summary>
        [Description("命令")]
        Command = 5
    }

    /// <summary>
    /// 相关指令
    /// </summary>
    public enum EnumPrintCommand
    {
        /// <summary>
        /// 增加分隔符行
        /// </summary>
        [Description("增加分隔符行")]
        AddSeparatorLine,

        /// <summary>
        /// 增加空白行
        /// </summary>
        [Description("增加空白行")]
        AddEmptyLine
    }

    /// <summary>
    /// 打印票据类型
    /// </summary>
    public enum EnumReceiptType
    {
        [Description("小票")]
        ShoppingReceiptESCPOS = 0,

        [Description("小票画布")]
        ShoppingReceiptGraphics =1,
        
        [Description("价签")]
        PriceTag = 2,

        [Description("标签")]
        BrandLabel = 3,

        [Description("交接班ESCPOS")]
        ChangeShiftsESCPOS = 4,

        [Description("制作单ESCPOS")]
        ProductionNoteESCPOS = 6,

        [Description("制作单画布")]
        ProductionNoteGraphics = 7,

        [Description("商品销售报表画布")]
        GoodsSalesReportGraphics = 9,

        [Description("商品销售报表ESCPOS")]
        GoodsSalesReportESCPOS = 10,

        [Description("销售报表画布")]
        SalesReportGraphics = 11,

        [Description("销售报表ESCPOS")]
        SalesReportESCPOS = 12,

        [Description("公共打印画布")]
        CommonGraphics = 1000,

        [Description("公共打印ESCPOS")]
        CommonESCPOS = 1001,
    }

    ///<Summary>
    /// This enum is used to set postion of barcode label
    /// </Summary>

    public enum EnumPositions
    {
        NotPrint = 0,
        AbovBarcode = 1,
        BelowBarcode = 2,
        Both = 3
    }

    public enum EnumPrinterModeState
    {
        On,
        Off
    }

    ///<Summary>
    /// This enum is used to set font type
    /// </Summary>
    public enum EnumFonts
    {
        FontA,
        FontB,
        FontC,
        FontD,
        FontE,
        SpecialFontA,
        SpecialFontB
    }

    public enum EnumQrCodeSize
    {
        Size0,
        Size1,
        Size2
    }

    public enum EnumReceiptPaperSize
    {
        [Description("50小票")]
        Paper50 = 32,

        [Description("80小票")]
        Paper80 = 48,
    }
}
