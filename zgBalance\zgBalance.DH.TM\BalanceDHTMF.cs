﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using zgBalanceCommon.Balance;
using zgBalanceCommon.Model;
using zgBalanceCommon;
using CefSharp;
using Newtonsoft.Json;
using System.Threading;

namespace zgBalance.DH.TM
{
    /// <summary>
    /// 大华TM-F系列电子秤适配
    /// </summary>
    public class BalanceDHTMF : BalanceAbstract
    {
        Socket tcpClient;
        private EndPoint point;
        Encoding encoding = Encoding.GetEncoding("GB2312");
        string ip;
        int port;


        public BalanceDHTMF()
        {
        }


        public BalanceDHTMF(string ip,int port) {
            this.ip = ip;
            this.port = port;
        }

        /// <summary>
        /// 创建PLU实例
        /// </summary>
        /// <returns></returns>
        public override BasePLU CreatePLUInstance()
        {
            return new DHPLU();
        }

        /// <summary>
        /// 读取标签尺寸
        /// </summary>
        /// <param name="width"></param>
        /// <param name="height"></param>
        public override void ReadTagSize(out int width, out int height)
        {
            try
            {
                initTcpClient();

                var msg = DHPLU.ReqTagBaseInfo();
                var bytes = encoding.GetBytes(msg);
                tcpClient.Send(bytes);
                while (true)
                {
                    byte[] data = new byte[200];
                    tcpClient.Receive(data);//接收返回数据
                    string receiveStr = encoding.GetString(data).Replace("\0", "");
                    var widthAndHeight = receiveStr.Substring(5, 4);
                    var widthStr = widthAndHeight.Substring(0, 2);
                    var heightStr = widthAndHeight.Substring(2, 2);
                    width = Convert.ToInt32(widthStr);
                    height = Convert.ToInt32(heightStr);
                    break;
                }
            }
            catch (Exception)
            {

                throw;
            }
            finally {
                //tcpClient.Shutdown(SocketShutdown.Both);
                tcpClient.Close();
            }
        }

        /// <summary>
        /// 扫描标签
        /// </summary>
        /// <param name="barCode"></param>
        /// <returns></returns>
        public override void ScanBarCode(string barCode, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null)
        {
            try
            {
                var barCodeCharArray = barCode.ToArray();
                DHPLU plu = new DHPLU();
                plu.GoodsCode = barCodeCharArray.SubString(0, 7);
                plu.TotalPrice = float.Parse(barCodeCharArray.SubString(7, 5)) / 100f;

                var checkCode = ZGBarCode.GetCheckCodeEAN13(barCode);
                plu.CheckSuccess = checkCode.Equals(barCodeCharArray[barCodeCharArray.Length - 1].ToString());
                if (onsuccess != null)
                {
                    onsuccess.ExecuteAsync(JsonConvert.SerializeObject(plu));
                }
            }
            catch (Exception ex)
            {
                var msg = ex.Message ?? "error";
                if (onfail != null)
                {
                    onfail.ExecuteAsync(msg);
                }
            }
            
            
        }

        /// <summary>
        /// 下发商品信息到电子秤
        /// </summary>
        /// <param name="goodsInfo"></param>
        /// <returns></returns>
        public void SendGoods(string ip,int port,List<DHPLU> goodsInfo, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null)
        {
            try
            {
                this.ip = ip;
                this.port = port;
                initTcpClient();

                foreach (var item in goodsInfo)
                {
                    var msg = item.ToPLU();
                    var bytes = encoding.GetBytes(msg);
                    tcpClient.Send(bytes);

                    while (true)
                    {
                        byte[] data = new byte[11];
                        tcpClient.Receive(data);//接收返回数据
                        string receiveStr = encoding.GetString(data).Replace("\0", "");
                        if (receiveStr.Length > 0)
                        {
                            break;
                        }
                    }
                    Thread.Sleep(50);
                }
                if (onsuccess != null)
                {
                    onsuccess.ExecuteAsync(1);

                }
            }
            catch (Exception ex)
            {
                var msg = ex.Message ?? "error";
                if (onfail != null)
                {
                    onfail.ExecuteAsync(msg);
                }
            }
            finally {
                //tcpClient.Shutdown(SocketShutdown.Both);
                tcpClient.Close();
            }
        }

        /// <summary>
        /// 设置热键
        /// </summary>
        /// <param name="ip"></param>
        /// <param name="port"></param>
        /// <param name="pluNumList"></param>
        /// <param name="onsuccess"></param>
        /// <param name="onfail"></param>
        public void SendHotKey(string ip, int port,List<string> pluNumList, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null) {
            this.ip = ip;
            this.port = port;
            var step = 36;
            var stringTemplate = "!0L{0}A{1}B\r\n";
            try
            {
                int index = 0;
                initTcpClient();
                for (int i = 0; i < pluNumList.Count ;i+= step)
                {
                    var tempList = pluNumList.Skip(index * step).Take(step).ToList();
                    if (tempList.Count < step) {
                        for (int x = tempList.Count; x < step; x++)
                        {
                            tempList.Add("0");
                        }
                    }
                    var tempList2 = tempList.Select(j => j.PadLeft(4, '0'));
                    var v = string.Join("", tempList2.ToArray());
                    var sendValue = string.Format(stringTemplate, index.ToString().PadLeft(2, '0'), v);
                    tcpClient.Send(encoding.GetBytes(sendValue));
                    Thread.Sleep(100);
                    index++;
                }
            }
            catch (Exception ex)
            {
                throw;
            }
            finally {
                //tcpClient.Shutdown(SocketShutdown.Both);
                tcpClient.Close();
            }
           
        }

        public override void SetHotKey(List<object> keyList)
        {
            throw new NotImplementedException();
        }


        /// <summary>
        /// 设置标题
        /// </summary>
        /// <param name="title"></param>
        public override void SetTitle(string title)
        {
            try
            {
                initTcpClient();
                var code = DHPLU.SetTitleMsg(title);
                tcpClient.Send(encoding.GetBytes(code));
            }
            catch (Exception ex)
            {
                throw;
            }
            finally
            {
                //tcpClient.Shutdown(SocketShutdown.Both);
                tcpClient.Close();
            }
        }

        
        void initTcpClient() {
            if (tcpClient == null || !tcpClient.Connected)
            {
                tcpClient = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
                IPAddress ipaddress = IPAddress.Parse(ip);
                point = new IPEndPoint(ipaddress, port);
                tcpClient.Connect(point);
            }
        }


    }
}
