﻿using zgAdvert.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using zgUtils.Files;
using zgLogging;

namespace zgAdvert.Files
{

    public class FileManager
    {
        private bool isCancel;
        private static FileManager _instance;
        private List<string> downloadUrls;
        private object mute = new object();

        public static FileManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new FileManager();
                }
                return _instance;
            }
        }

        public FileManager()
        {
            downloadUrls = new List<string>();
        }
       
        public void DownLoadDispose()
        {
            isCancel = true;
        }


        private bool IsCancel()
        {
            return isCancel;
        }


        public void DownLoads(List<ADItem> adItems)
        {
            if (adItems == null)
            {
                Log.WriterNormalLog("广告为空");
                return;
            }
            Log.WriterNormalLog("添加广告到下载队列:" + adItems.Count.ToString());
            using (List<ADItem>.Enumerator enumerator = adItems.GetEnumerator())
            {
                while (enumerator.MoveNext())
                {
                    ADItem item = enumerator.Current;
                    if (item.location > 1 && !IsFileExist(item)&& (item.MaterialType.Equals("VDO") || item.MaterialType.Equals("IMG")))
                    {
                        object obj = mute;
                        lock (obj)
                        {
                            if (!downloadUrls.Contains(item.img))
                            {
                                downloadUrls.Add(item.img);
                                Log.WriterNormalLog("当前正在下载的广告数:" + downloadUrls.Count);
                                Log.WriterNormalLog(string.Format("加入下载{0}", item.img));
                                string arg = item.img.Remove(0, item.img.LastIndexOf("/") + 1);
                                string path = string.Format("{0}/{1}.temp", item.location, arg);
                                string path2 = string.Format("{0}/{1}.record", item.location, arg);
                                string recordPath = Path.Combine(AdvertFactory.CacheDir, path2);
                                string filePathTemp = Path.Combine(AdvertFactory.CacheDir, path);
                                new Task(delegate ()
                                {
                                    FileDownLoader.Instance.DowLoadFile(item.img, filePathTemp, new Action<double>(DownloadProgress), new Func<bool>(IsCancel), recordPath, new Action<EDownLoadState>(DownloadCompletely), 0L);
                                }).Start();
                            }
                        }
                    }
                }
            }
        }
        
        private void DownloadCompletely(EDownLoadState state)
        {
        }

        private void DownloadProgress(double value)
        {
        }


        public bool IsFileExist(ADItem item)
        {
            if (string.IsNullOrWhiteSpace(item.img))
            {
                return false;
            }
            string path = item.img.Remove(0, item.img.LastIndexOf("/") + 1);
            return File.Exists(Path.Combine(AdvertFactory.CacheDir,item.location.ToString(), path));
        }
        public bool IsHtmlFileExist(ADItem item)
        {
            if (string.IsNullOrWhiteSpace(item.img))
            {
                return false;
            }
            string path = item.img.Remove(0, item.img.LastIndexOf("/") + 1);
            path = path.Remove(path.LastIndexOf(".")) + ".html";
            return File.Exists(Path.Combine(AdvertFactory.CacheDir, path));
        }



        public bool IsExistDownReportFile(ADItem item)
        {
            return item != null && File.Exists(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "DownLoadStatus", string.Format("{0}", item.id.ToString())));
        }


        public void DeleteDownReportFile(ADItem item)
        {
            try
            {
                string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "DownLoadStatus", string.Format("{0}", item.id.ToString()));
                if (File.Exists(path))
                {
                    File.Delete(path);
                }
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
            }
        }


        private void MarkFile(ADItem item)
        {
            string text = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "DownLoadStatus");
            if (item != null && !Directory.Exists(text))
            {
                Directory.CreateDirectory(text);
            }
            string path = Path.Combine(text, string.Format("{0}", item.id.ToString()));
            if (!File.Exists(path))
            {
                File.Create(path);
            }
        }
 
    }
}
