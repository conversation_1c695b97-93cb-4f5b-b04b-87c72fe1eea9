﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using zgPrinter.Printer.ESCPOS.Interface;

namespace zgPrinter.Printer.ESCPOS.EpsonCommands
{
    public class Image : IImage
    {
        private static BitmapData GetBitmapData(Bitmap bmp)
        {

            var threshold = 127;
            var index = 0;
            var beginIndex = 0;
            double multiplier = 116 * 3; // this depends on your printer model.
            double scale = (multiplier / (double)bmp.Width);
            if (scale > 1)
            {
                scale = 1;
                beginIndex = ((int)multiplier - bmp.Width) / 2;
            }
            int xheight = (int)(bmp.Height * scale);
            int xwidth = (int)(bmp.Width * scale);
            var dimensions = (int)multiplier * xheight;
            var dots = new BitArray(dimensions);

            var scaledBitmap = new Bitmap((int)multiplier, xheight);
            using (var g = Graphics.FromImage(scaledBitmap))
            {
                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                g.DrawImage(bmp, beginIndex, 0, xwidth, xheight);
            }


            for (var y = 0; y < xheight; y++)
            {
                for (var x = 0; x < (int)multiplier; x++)
                {
                    var _x = x;
                    var _y = y;
                    var color = scaledBitmap.GetPixel(_x, _y);
                    var colorA = color.A < 1;
                    var colorR = color.R;
                    var colorG = color.G;
                    var colorB = color.B;

                    if (color.A < 1)
                    {
                        colorG = 255;
                        colorR = 255;
                        colorB = 255;
                    }
                    var luminance = (int)(colorR * 0.3 + colorG * 0.59 + colorB * 0.11);
                    dots[index] = (luminance < threshold);
                    index++;
                }
            }

            return new BitmapData()
            {
                Dots = dots,
                Height = (int)(scaledBitmap.Height),
                Width = (int)(scaledBitmap.Width)
            };

        }

        byte[] IImage.Print(Bitmap image)
        {
            return printPic(image).ToArray();
            
            //var data = GetBitmapData(image);
            //BitArray dots = data.Dots;
            //byte[] width = BitConverter.GetBytes(data.Width);

            //int offset = 0;
            //MemoryStream stream = new MemoryStream();
            //BinaryWriter bw = new BinaryWriter(stream);

            //bw.Write((char)0x1B);
            //bw.Write('@');

            //bw.Write((char)0x1B);
            //bw.Write('3');
            //bw.Write((byte)24);

            //while (offset < data.Height)
            //{
            //    bw.Write((char)0x1B);
            //    bw.Write('*');         // bit-image mode
            //    bw.Write((byte)33);    // 24-dot double-density
            //    bw.Write(width[0]);  // width low byte
            //    bw.Write(width[1]);  // width high byte

            //    for (int x = 0; x < data.Width; ++x)
            //    {
            //        for (int k = 0; k < 3; ++k)
            //        {
            //            byte slice = 0;
            //            for (int b = 0; b < 8; ++b)
            //            {
            //                int y = (((offset / 8) + k) * 8) + b;
            //                // Calculate the location of the pixel we want in the bit array.
            //                // It'll be at (y * width) + x.
            //                int i = (y * data.Width) + x;

            //                // If the image is shorter than 24 dots, pad with zero.
            //                bool v = false;
            //                if (i < dots.Length)
            //                {
            //                    v = dots[i];
            //                }
            //                slice |= (byte)((v ? 1 : 0) << (7 - b));
            //            }

            //            bw.Write(slice);
            //        }
            //    }
            //    offset += 24;
            //    bw.Write((char)0x0A);
            //}
            //// Restore the line spacing to the default of 30 dots.
            //bw.Write((char)0x1B);
            //bw.Write('3');
            //bw.Write((byte)30);

            //bw.Flush();
            //byte[] bytes = stream.ToArray();
            //bw.Dispose();
            //return bytes;
        }

        void t()
        {
            byte[] array = new byte[3];
            array[0] = 27;
            array[1] = 51;
        }


        public List<byte> printPic(Bitmap bmp)
        {
            byte[] array13 = new byte[3];
            array13[0] = 27;
            array13[1] = 51;

            List<byte> list = new List<byte>();
            bool flag = bmp != null;
            if (flag)
            {
                list.AddRange(array13);
                byte[] collection = new byte[]
                {
            27,
            64
                };
                list.AddRange(collection);
                byte[] array = new byte[3];
                array[0] = 27;
                array[1] = 51;
                byte[] array2 = array;
                list.AddRange(array2);
                array2[0] = 0;
                array2[1] = 0;
                array2[2] = 0;
                byte[] array3 = new byte[5];
                array3[0] = 27;
                array3[1] = 42;
                byte[] array4 = array3;
                array4[2] = 33;
                array4[3] = (byte)(bmp.Width % 256);
                array4[4] = (byte)(bmp.Width / 256);
                var a = new byte[] { 27, 'a'.ToByte(), 1 };
                for (int i = 0; i < bmp.Height / 24 + 1; i++)
                {
                    list.AddRange(a);
                    list.AddRange(array4);
                    for (int j = 0; j < bmp.Width; j++)
                    {
                        for (int k = 0; k < 24; k++)
                        {
                            bool flag2 = i * 24 + k < bmp.Height;
                            if (flag2)
                            {
                                bool flag3 = bmp.GetPixel(j, i * 24 + k).GetBrightness() < 0.9;
                                if (flag3)
                                {
                                    byte[] array5 = array2;
                                    int num = k / 8;
                                    array5[num] += (byte)(128 >> k % 8);
                                }
                            }
                        }
                        list.AddRange(array2);
                        array2[0] = 0;
                        array2[1] = 0;
                        array2[2] = 0;
                    }
                    list.Add(13);
                    list.AddRange(new byte[]
                    {
                27,
                74,
                1
                    });
                }
                list.Add(10);
            }
            return list;
        }
    }



    public class BitmapData
    {
        public BitArray Dots { get; set; }
        public int Height { get; set; }
        public int Width { get; set; }
    }
}

