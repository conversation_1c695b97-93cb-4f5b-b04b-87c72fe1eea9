﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Threading;
using System.Windows.Forms;

using zgpos.ProgramTemplate;

namespace DefaultPorgrams
{
    public class ClothingClass : ProgramTemplateClass
    {
        Form ProgramForm = new Form();
        public ClothingClass()
        {
            this.Name = "服装版";
            this.Description = "服装版 [via leon]";
            this.Icon = DefaultProgramResource.ClothingIcon;
        }

        public override string FileName => System.Reflection.Assembly.GetExecutingAssembly().ManifestModule.ScopeName;

        protected override Form CreateProgramForm()
        {
            Assembly PluginAssembly = null;
            string AssemblyPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Programs", "Clothing.dll");
            if (!File.Exists(AssemblyPath)) {
                AssemblyPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Programs", "Clothing.exe");
            }
            //PluginAssembly = Assembly.LoadFrom(AssemblyPath);
            try
            {
                // 把链接库文件读入内存后从内存加载，允许程序在运行时更新链接库
                using (FileStream AssemblyStream = new FileStream(AssemblyPath, FileMode.Open, FileAccess.Read))
                {
                    using (BinaryReader AssemblyReader = new BinaryReader(AssemblyStream))
                    {
                        byte[] AssemblyBuffer = AssemblyReader.ReadBytes((int)AssemblyStream.Length);
                        PluginAssembly = Assembly.Load(AssemblyBuffer);
                        AssemblyReader.Close();
                    }
                    AssemblyStream.Close();
                }
            }
            catch (Exception ex)
            {
                return null;
            }
            if (PluginAssembly != null) {
                LoadProgram(PluginAssembly);
            }
            return ProgramForm;
        }
        public void LoadProgram(Assembly PluginAssembly)
        {
            Mutex m = new Mutex(true, Application.ProductName+"-Clothing", out bool ret);
            if (ret)
            {
                
                Type t = PluginAssembly.GetType("Clothing.MainProgram");
                t.InvokeMember("Start", BindingFlags.DeclaredOnly | BindingFlags.Public | BindingFlags.Static | BindingFlags.InvokeMethod, null, null, null);

                m.ReleaseMutex();
            }
            else
            {
                MessageBox.Show(null, "有一个和本程序相同的应用程序已经在运行，请不要同时运行多个本程序。\n\n这个程序即将退出。", Application.ProductName, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                // 提示信息，可以删除。
                Application.Exit();//退出程序
            }
            
        }
    }
}
