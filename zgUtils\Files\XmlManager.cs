﻿using System;
using System.IO;
using System.Xml.Serialization;

namespace zgUtils.Files
{
	
	public class XmlManager
	{
		
		public static void SaveFile(object obj, string filename)
		{
			FileStream fileStream = null;
			try
			{
				fileStream = new FileStream(filename, FileMode.Create, FileAccess.Write, FileShare.ReadWrite);
				XmlSerializer s = XmlSerializer.FromTypes(new[] { obj.GetType() })[0];
				s.Serialize(fileStream, obj);
			}
			catch (Exception ex)
			{
				throw ex;
			}
			finally
			{
				if (fileStream != null)
				{
					fileStream.Close();
				}
			}
		}

		
		public static object Load(Type type, string filename)
		{
			FileStream fileStream = null;
			object result;
			try
			{
				fileStream = new FileStream(filename, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
				result = new XmlSerializer(type).Deserialize(fileStream);
			}
			catch (Exception ex)
			{
				throw ex;
			}
			finally
			{
				if (fileStream != null)
				{
					fileStream.Close();
				}
			}
			return result;
		}

		
		public static T Load<T>(string fileName) where T : new()
		{
			FileStream fileStream = null;
			T result;
			try
			{
				fileStream = new FileStream(fileName, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
				result = (T)((object)new XmlSerializer(typeof(T)).Deserialize(fileStream));
			}
			catch (Exception ex)
			{
				throw ex;
			}
			finally
			{
				if (fileStream != null)
				{
					fileStream.Close();
				}
			}
			return result;
		}
	}
}
