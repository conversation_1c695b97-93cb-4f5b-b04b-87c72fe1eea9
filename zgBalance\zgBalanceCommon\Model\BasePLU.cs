﻿namespace zgBalanceCommon.Model
{
    public class BasePLU
    {
        /// <summary>
        /// 商品名
        /// </summary>
        public string GoodsName { get; set; } = string.Empty;

        /// <summary>
        /// 商品序号
        /// </summary>
        public int GoodsNum { get; set; }

        /// <summary>
        /// 商品代码
        /// </summary>
        public string GoodsCode { get; set; } = string.Empty;

        /// <summary>
        /// 商品单价，元
        /// </summary>
        public double UnitPrice { get; set; }

        /// <summary>
        /// 总价
        /// </summary>
        public double TotalPrice { get; set; }

        /// <summary>
        /// 称重方式
        /// </summary>
        public EnumWeighingWay WeighingWay { get; set; }

        /// <summary>
        /// 门店号
        /// </summary>
        public string StoreCode { get; set; } = string.Empty;

        /// <summary>
        /// 皮重，KG.最大1位数+3位小数
        /// </summary>
        public double Tare { get; set; }

        /// <summary>
        /// 有效期
        /// </summary>
        public int Validity { get; set; }

        public bool CheckSuccess { get; set; }

        /// <summary>
        /// 转义成PLU报文
        /// </summary>
        /// <returns></returns>
        public virtual string ToPLU() { return null; }


        /// <summary>
        /// 从PLU报文解析成实体
        /// </summary>
        /// <param name="message"></param>
        public virtual void ConvertFromImportMessage(string message) { }
    }
}
