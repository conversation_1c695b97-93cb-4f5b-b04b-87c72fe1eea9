﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{B25CF8AC-F511-4415-B9D2-83AFD0C8C8BC}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>zgPrinter</RootNamespace>
    <AssemblyName>zgPrinter</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\out\Programs\Libs\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\out\Programs\Libs\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="zgLogging, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\zgLogging.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zxing" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AbstractPrinter.cs" />
    <Compile Include="ExtendMethod.cs" />
    <Compile Include="ImageProcess.cs" />
    <Compile Include="Model\Enum.cs" />
    <Compile Include="Model\KeyValueItem.cs" />
    <Compile Include="Model\PrintConfig.cs" />
    <Compile Include="Model\PrintElment\ElementExtAttribute.cs" />
    <Compile Include="Model\PrintElment\LabelElement.cs" />
    <Compile Include="Model\PrintElment\LabelTemplate.cs" />
    <Compile Include="Model\PrintElment\PrintElement.cs" />
    <Compile Include="Model\PrintElment\PrintElementCommand.cs" />
    <Compile Include="Model\PrintElment\PrintElementGoodsDataTable.cs" />
    <Compile Include="Model\PrintElment\PrintElementImage.cs" />
    <Compile Include="Model\PrintElment\PrintElementImageBlock.cs" />
    <Compile Include="Model\PrintElment\PrintElementShoppingItemTable.cs" />
    <Compile Include="Model\PrintElment\PrintElementString.cs" />
    <Compile Include="Model\PrintElment\PrintElementStringBlock.cs" />
    <Compile Include="Model\PrintElment\PrintElementTable.cs" />
    <Compile Include="Model\ResendContent.cs" />
    <Compile Include="Model\SocketWrapper.cs" />
    <Compile Include="Model\ZgLabel\ZgLabel.cs" />
    <Compile Include="Model\ZgLabel\ZgLabelItem.cs" />
    <Compile Include="Printer.DrawImg\GraphicsPrinter.cs" />
    <Compile Include="Printer.ESCPOS\EpsonCommands\Alignment.cs" />
    <Compile Include="Printer.ESCPOS\EpsonCommands\BarCode.cs" />
    <Compile Include="Printer.ESCPOS\EpsonCommands\Drawer.cs" />
    <Compile Include="Printer.ESCPOS\EpsonCommands\EscPos.cs" />
    <Compile Include="Printer.ESCPOS\EpsonCommands\FontMode.cs" />
    <Compile Include="Printer.ESCPOS\EpsonCommands\FontWidth.cs" />
    <Compile Include="Printer.ESCPOS\EpsonCommands\Image.cs" />
    <Compile Include="Printer.ESCPOS\EpsonCommands\InitializePrint.cs" />
    <Compile Include="Printer.ESCPOS\EpsonCommands\LineHeight.cs" />
    <Compile Include="Printer.ESCPOS\EpsonCommands\PaperCut.cs" />
    <Compile Include="Printer.ESCPOS\EpsonCommands\QrCode.cs" />
    <Compile Include="Printer.ESCPOS\ESCPOSPrinter.cs" />
    <Compile Include="Printer.ESCPOS\Interface\IAlignment.cs" />
    <Compile Include="Printer.ESCPOS\Interface\IBarCode.cs" />
    <Compile Include="Printer.ESCPOS\Interface\IDrawer.cs" />
    <Compile Include="Printer.ESCPOS\Interface\IFontMode.cs" />
    <Compile Include="Printer.ESCPOS\Interface\IFontWidth.cs" />
    <Compile Include="Printer.ESCPOS\Interface\IImage.cs" />
    <Compile Include="Printer.ESCPOS\Interface\IInitializePrint.cs" />
    <Compile Include="Printer.ESCPOS\Interface\ILineHeight.cs" />
    <Compile Include="Printer.ESCPOS\Interface\IPaperCut.cs" />
    <Compile Include="Printer.ESCPOS\Interface\IPrintCommand.cs" />
    <Compile Include="Printer.ESCPOS\Interface\IQrCode.cs" />
    <Compile Include="Printer.ESCPOS\RawPrinterHelper.cs" />
    <Compile Include="Printer.TSPL\TSPLPrinter.cs" />
    <Compile Include="Printer.Usb\UsbPrinter.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resource.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resource.resx</DependentUpon>
    </Compile>
    <Compile Include="SetupAPINativeMethods.cs" />
    <Compile Include="SocketPool.cs" />
    <Compile Include="ZGPrintTool.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resource.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="Resources\report1.rdlc" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\printer20211126152543.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\交接班报表20211203100902.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\制作单20211203130400.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\商品销售报销小票20211203154358.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\销售报表小票20211207093214.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\退菜单20211217160802.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\压桌单20211220112828.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\预结单20211220160830.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\结账单20211221101225.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\printer202111261525431.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\退货单202112171608021.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\餐饮制作单20211203130400.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\餐饮退菜单20211217160802.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\烘焙会员充值20211229153755.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\餐饮交接班报表202112031009021.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\次卡购买20220309143953.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\次卡消费20220309161620.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\服装销售小票20220312092308.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\服装挂单小票20220312102246.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\服装退货单202112171608021.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\积分兑换20220309161620 .txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\压桌单202206211336.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\烘焙销售小票80.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\销售报表小票80.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\餐饮退菜单80.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\餐饮预结单80.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\退货单80.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\结账单80.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\退菜单80.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\转桌单.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\取件单.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\寄件单.txt" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>