﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Drawing;

namespace zgPrinter.Model
{
    /// <summary>
    /// 打印元素-文本。独占一行
    /// </summary>
    public class PrintElementString : PrintElement
    {

        public PrintElementString() { }
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="index">序号</param>
        /// <param name="content">内容</param>
        /// <param name="font">字体，默认为空，可以在BaseReceipt子类中赋值</param>
        /// <param name="textAlign">为本浮动，默认居中</param>
        /// <param name="wordWrap">是否自动换行</param>
        public PrintElementString(int index, string content, Font font = null, EnumTextAlign textAlign = EnumTextAlign.Center, bool wordWrap = false)
        {
            this.ContentType = EnumContentType.String;
            this.Index = index;
            this.ContentFont = font;
            this.TextAlign = textAlign;
            this.Content = content;
        }

        /// <summary>
        /// 文本内容
        /// </summary>
        public new string Content { get; set; }

        /// <summary>
        /// 文本是否自动换行
        /// </summary>
        public bool WordWrap { get; set; }

        /// <summary>
        /// 文本模板，执行string.format用
        /// </summary>
        public string Format { get; set; } = "{0}";

        public override JObject ToJObject()
        {
            var jsonObject = new JObject();
            jsonObject.Add("index", this.Index);
            jsonObject.Add("contenttype", (int)this.ContentType);
            jsonObject.Add("textalign", (int)this.TextAlign);
            jsonObject.Add("contentfont", JsonConvert.SerializeObject(this.ContentFont));
            jsonObject.Add("content", this.Content.ToString());
            jsonObject.Add("datamember", this.DataMember);
            jsonObject.Add("format", this.Format);
            return jsonObject;
        }

        
    }
}
