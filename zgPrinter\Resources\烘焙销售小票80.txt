﻿[{
	"index": 10,
	"contenttype": 4,
	"textalign": 0,
	"datamember": "logo",
	"displayname": "",
	"imgpath": ""
}, {
	"index": 20,
	"contenttype": 1,
	"textalign": 32,
	"contentfont": "\"仿宋, 12pt, style=Bold\"",
	"content": "澎湖山庄家家悦",
	"datamember": "storename",
	"format": "{0}",
	"defaultcontent": "",
	"displayname": "文本",
	"wordwarp": false
}, {
	"index": 21,
	"contenttype": 5,
	"textalign": 0,
	"content": 1
}, {
	"index": 30,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 12pt, style=Bold\"",
	"content": "流水号",
	"datamember": "流水号",
	"format": "{0}",
	"defaultcontent": "",
	"displayname": "流水号",
	"wordwarp": false
}, {
	"index": 32,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9pt\"",
	"content": "销售单号：11111",
	"datamember": "销售单号",
	"format": "销售单号：{0}",
	"defaultcontent": "",
	"displayname": "订单号",
	"wordwarp": false
}, {
	"index": 33,
	"contenttype": 5,
	"textalign": 0,
	"content": 0
}, {
	"index": 39,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9pt\"",
	"content": "",
	"datamember": "挂单时间",
	"format": "挂单时间：{0}",
	"defaultcontent": "",
	"displayname": "挂单时间",
	"wordwarp": false
}, {
	"index": 40,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9pt\"",
	"content": "",
	"datamember": "交易时间",
	"format": "交易时间：{0}",
	"defaultcontent": "",
	"displayname": "交易时间",
	"wordwarp": false
}, {
	"index": 41,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9pt\"",
	"content": "",
	"datamember": "收银员",
	"format": "收银员：{0}",
	"defaultcontent": "",
	"displayname": "收银员",
	"wordwarp": false
}, {
	"index": 50,
	"contenttype": 5,
	"textalign": 0,
	"content": 0
}, {
	"index": 60,
	"contenttype": 3,
	"headtextalign": 32,
	"bodytextalign": 16,
	"datamember": "groups",
	"printhead": true,
	"printseparatorline": true,
	"headfont": "\"仿宋, 7.5pt\"",
	"bodyfont": "\"仿宋, 7.5pt\"",
	"config": "[{\"col_width\":\"48\",\"col_align\":\"Left\",\"col_head\":\"商品名称\",\"col_dataMember\":\"商品名称\",\"col_singleLine\":\"false\",\"col_isPrint\":\"true\"},{\"col_width\":\"14\",\"col_align\":\"Center\",\"col_head\":\"数量\",\"col_dataMember\":\"数量\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\"},{\"col_width\":\"18\",\"col_align\":\"Right\",\"col_head\":\"金额\",\"col_dataMember\":\"金额\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\"},{\"col_width\":\"17\",\"col_align\":\"Right\",\"col_head\":\"优惠价\",\"col_dataMember\":\"优惠价\",\"col_singleLine\":\"False\",\"col_isPrint\":\"true\"}]",
	"displayname": "商品信息"
}, {
	"index": 70,
	"contenttype": 5,
	"textalign": 0,
	"content": 0
}, {
	"index": 80,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9pt\"",
	"content": "",
	"datamember": "合计",
	"format": "金额合计：{0}",
	"defaultcontent": " ",
	"displayname": "合计",
	"wordwarp": false
}, {
	"index": 90,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9pt\"",
	"content": "",
	"datamember": "其他优惠",
	"format": "优惠：{0}",
	"defaultcontent": "",
	"displayname": "其他优惠",
	"wordwarp": false
},{
	"index": 91,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9pt\"",
	"content": "",
	"datamember": "积分抵扣",
	"format": "{0}",
	"defaultcontent": "",
	"displayname": "其他优惠",
	"wordwarp": false
}, {
	"index": 100,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9pt\"",
	"content": "",
	"datamember": "应付",
	"format": "应付：{0}",
	"defaultcontent": "",
	"displayname": "应付",
	"wordwarp": false
}, {
	"index": 110,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9pt\"",
	"content": "",
	"datamember": "支付方式",
	"format": "{0}",
	"defaultcontent": "",
	"displayname": "支付方式",
	"wordwarp": false
}, {
	"index": 120,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9pt\"",
	"content": "",
	"datamember": "找零",
	"format": "找零：{0}",
	"defaultcontent": "",
	"displayname": "找零",
	"wordwarp": false
}, {
	"index": 140,
	"contenttype": 5,
	"textalign": 0,
	"content": 1
}, {
	"index": 150,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9pt\"",
	"content": "",
	"datamember": "会员名",
	"format": "会员名：{0}",
	"defaultcontent": "",
	"displayname": "会员名",
	"wordwarp": false
}, {
	"index": 160,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9pt\"",
	"content": "",
	"datamember": "会员手机号",
	"format": "会员手机号：{0}",
	"defaultcontent": "",
	"displayname": "会员手机号",
	"wordwarp": false
}, {
	"index": 170,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9pt\"",
	"content": "",
	"datamember": "余额",
	"format": "余额：{0}",
	"defaultcontent": "",
	"displayname": "余额",
	"wordwarp": false
}, {
	"index": 180,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9pt\"",
	"content": "",
	"datamember": "积分",
	"format": "积分：{0}",
	"defaultcontent": "",
	"displayname": "积分",
	"wordwarp": false
}, {
	"index": 190,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9pt\"",
	"content": "",
	"datamember": "备注",
	"format": "备注：{0}",
	"defaultcontent": "",
	"displayname": "备注",
	"wordwarp": false
}, {
	"index": 210,
	"contenttype": 5,
	"textalign": 0,
	"content": 0
}, {
	"index": 220,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9.75pt, style=Bold\"",
	"content": "",
	"datamember": "备注内容",
	"format": "{0}",
	"defaultcontent": "",
	"displayname": "结尾",
	"wordwarp": false
}, {
	"index": 230,
	"contenttype": 1,
	"textalign": 16,
	"contentfont": "\"仿宋, 9pt\"",
	"content": "",
	"datamember": "打印时间",
	"format": "打印时间：{0}",
	"defaultcontent": "",
	"displayname": "打印时间",
	"wordwarp": false
}, {
	"index": 240,
	"contenttype": 4,
	"textalign": 0,
	"datamember": "qrcode",
	"displayname": "",
	"imgpath": ""
}]