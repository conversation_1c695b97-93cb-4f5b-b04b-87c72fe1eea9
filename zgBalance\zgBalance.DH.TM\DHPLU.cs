﻿using zgBalanceCommon.Model;
using zgBalanceCommon;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgBalance.DH.TM
{
    public class DHPLU : BasePLU
    {
        /// <summary>
        /// 从PLU报文解析成实体
        /// </summary>
        /// <param name="message"></param>
        public override void ConvertFromImportMessage(string message)
        {
            var messageContentArray = message.ToArray();
            char[] goodsNumCharArray = new char[4];
            Array.Copy(messageContentArray,3, goodsNumCharArray, 0, 4);
            this.GoodsNum = messageContentArray.SubInt32(3, 4);// Convert.ToInt32(new String(goodsNumCharArray));
            this.GoodsCode = messageContentArray.SubString(8, 7);
            this.UnitPrice = messageContentArray.SubInt32(15, 6)/100f;
            this.WeighingWay = (EnumWeighingWay)messageContentArray.SubInt32(21,1);
            this.Validity = messageContentArray.SubInt32(28, 3);
            this.StoreCode = messageContentArray.SubString(31, 2);
            this.Tare = messageContentArray.SubInt32(48, 5);
            var tempGoodsName = messageContentArray.SubString(82, message.LastIndexOf("CDE") - 82);
            this.GoodsName = tempGoodsName.ConvertCoding2Chinese();
        }

        /// <summary>
        /// 转义成PLU报文
        /// </summary>
        /// <returns></returns>
        public override string ToPLU()
        {
            var sb = new StringBuilder("!0V");// 2
            sb.Append(this.GoodsNum.ToString("0000"));//商品序号 6
            sb.Append("A");// 7
            sb.Append(this.GoodsCode.PadLeft(7,'0'));//商品代码,7位
            sb.Append(this.UnitPrice.ConvertPrice4DHPLU());//单价，6位
            sb.Append((int)this.WeighingWay);//称重方式，1位
            sb.Append("00");//特殊信息1
            sb.Append("00");//特殊信息2
            sb.Append("00");//特殊信息3
            sb.Append(this.Validity.ToString("000"));//有效期，3位
            sb.Append(this.StoreCode.PadLeft(2,'0'));//门店号，2位
            sb.Append("00");//部门号
            sb.Append("0000000000000");//数字代号
            sb.Append((this.Tare * 1000).ToString("00000"));//皮重,克
            sb.Append("00");//标签号
            sb.Append("00");//是否打折
            sb.Append("00");//第一时间段起始时间
            sb.Append("00");//第一时间段结束时间
            sb.Append("00");//第一时间段内折扣
            sb.Append("00");//第二时间段起始时间
            sb.Append("00");//第二时间段结束时间
            sb.Append("00");//第二时间段内折扣
            sb.Append("00");//第三时间段起始时间
            sb.Append("00");//第三时间段结束时间
            sb.Append("00");//第三时间段内折扣
            sb.Append("00");//第四时间段起始时间
            sb.Append("00");//第四时间段结束时间
            sb.Append("00");//第四时间段内折扣
            sb.Append("B");
            sb.Append(this.GoodsName.ConvertChinese2Coding());
            sb.Append("CDE");
            sb.Append("\r\n");

            var result = sb.ToString();
            return result;
        }

        /// <summary>
        /// 设置电子秤标题报文
        /// </summary>
        /// <param name="title"></param>
        /// <returns></returns>
        public static string SetTitleMsg(string title) {
            var msgBody = title.ConvertChinese2Coding();
            var result = $"!0Z01A{msgBody}0000B\r\n";
            return result;
        }

        /// <summary>
        /// 请求基础信息的报文
        /// </summary>
        /// <returns></returns>
        public static string ReqTagBaseInfo() {
            var result = $"!0S00A\r\n";
            return result;
        }


    }
}
