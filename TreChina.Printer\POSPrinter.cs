﻿using ESCPOS.Printer.Enums;
using ESCPOS.Printer.Commands;
using ESCPOS.Printer.Extensions;
using ESCPOS.Printer.Helper;
using ESCPOS.Printer.Interfaces.Command;
using ESCPOS.Printer.Interfaces.Printer;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.IO;

namespace ESCPOS.Printer
{
    public class POSPrinter : IPrinter
    {
        private byte[] _buffer;
        private readonly string _printerName;
        private readonly IPrintCommand _command;

        public POSPrinter(string printerName)
        {
            _printerName = string.IsNullOrEmpty(printerName) ? "GP - L80160 Series" : printerName.Trim();
            _command = new EscPos();
            Cols = _command.ColsNomal;
        }

        public int ColsNomal
        {
            get
            {
                return _command.ColsNomal;
            }
        }
        private int _cols;
        public int Cols
        {
            set
            {
                _command.Cols = value;
                _cols = value;
            }
            get
            {
                
                return _cols;
            }
        }

        public int ColsCondensed
        {
            get
            {
                return _command.ColsCondensed;
            }
        }

        public int ColsExpanded
        {
            get
            {
                return _command.ColsExpanded;
            }
        }

        public void PrintDocument()
        {
            if (_buffer == null)
                return;
            if (!RawPrinterHelper.SendBytesToPrinter(_printerName, _buffer))
                throw new ArgumentException("Unable to access printer : " + _printerName);
        }

        public void Append(string value)
        {
            AppendString(value, true);
        }

        public void Append(byte[] value)
        {
            if (value == null)
                return;
            var list = new List<byte>();
            if (_buffer != null)
                list.AddRange(_buffer);
            list.AddRange(value);
            _buffer = list.ToArray();
        }

        public void AppendWithoutLf(string value)
        {
            AppendString(value, false);
        }

        private void AppendString(string value, bool useLf)
        {
            if (string.IsNullOrEmpty(value))
                return;
            if (useLf)
                value += "\n";
            var list = new List<byte>();
            if (_buffer != null)
                list.AddRange(_buffer);
            var bytes = Encoding.GetEncoding(PrinterExtensions._Encoding).GetBytes(value);
            list.AddRange(bytes);
            _buffer = list.ToArray();
        }

        public void NewLine()
        {
            Append("\r");
        }

        internal void PrintTest(PrintCommonClass data)
        {
            SetLineHeight((byte)data.lineheight);//行间隔
            Separator();
            FontSize("1...5...10...15...20...25...30...35...40..", 1);//42
            FontSize("1...5...10...15...20...25...30..",0);//32
            FontSize("1...5...10...15...20.", 17);//21
            FontSize("1...5...10...15.", 16);//16
            Separator();

            FontSize("交易时间:2020-09-05 09:49:44", 1);//42
            FontSize("交易时间:2020-09-05 09:49:44", 0);//32
            FontSize("交易时间:2020-09-05 09:49:44", 17);//21
            FontSize("交易时间:2020-09-05 09:49:44", 16);//16
            Separator();
            
            NewLines(6);
            PrintDocument();
        }

        public void NewLines(int lines)
        {
            for (int i = 1, loopTo = lines - 1; i <= loopTo; i++)
                NewLine();
        }

        public void Clear()
        {
            _buffer = null;
        }

        public void Separator(char speratorChar = '-')
        {
            Append(_command.Separator(speratorChar ));
        }

        public void AutoTest()
        {
            Append(_command.AutoTest());
        }

        public void TestPrinter()
        {
            Append("NORMAL - 48 COLUMNS");
            Append("1...5...10...15...20...25...30...35...40...45.48");
            Separator();
            Append("Text Normal");
            BoldMode("Bold Text");
            UnderlineMode("Underlined text");
            Separator();
            ExpandedMode(PrinterModeState.On);
            Append("Expanded - 23 COLUMNS");
            Append("1...5...10...15...20..23");
            ExpandedMode(PrinterModeState.Off);
            Separator();
            CondensedMode(PrinterModeState.On);
            Append("Condensed - 64 COLUMNS");
            Append("1...5...10...15...20...25...30...35...40...45...50...55...60..64");
            CondensedMode(PrinterModeState.Off);
            Separator();
            DoubleWidth2();
            Append("Font Width 2");
            DoubleWidth3();
            Append("Font Width 3");
            NormalWidth();
            Append("Normal width");
            Separator();
            AlignRight();
            Append("Right aligned text");
            AlignCenter();
            Append("Center-aligned text");
            AlignLeft();
            Append("Left aligned text");
            Separator();
            Font("Font A", Fonts.FontA);
            Font("Font B", Fonts.FontB);
            Font("Font C", Fonts.FontC);
            Font("Font D", Fonts.FontD);
            Font("Font E", Fonts.FontE);
            Font("Font Special A", Fonts.SpecialFontA);
            Font("Font Special B", Fonts.SpecialFontB);
            Separator();
            InitializePrint();
            SetLineHeight(24);
            Append("This is first line with line height of 30 dots");
            SetLineHeight(40);
            Append("This is second line with line height of 24 dots");
            Append("This is third line with line height of 40 dots");
            NewLines(3);
            Append("End of Test :)");
            Separator();
        }
        private int GetByeCount(string str) {
            int charNum = 0; //统计字节位数
            char[] _charArray = str.ToCharArray();
            for (int i = 0; i < _charArray.Length; i++)
            {
                char _eachChar = _charArray[i];
                if (_eachChar >= 0x4e00 && _eachChar <= 0x9fa5) //判断中文字符
                    charNum += 2;
                else
                    charNum += 1;
            }
            return charNum;
        }
        private int getGoodColumnLong_32(List<string> head) {

            int intLong = Cols - GetByeCount(head[0]);
            if (head.Count > 1) intLong = intLong / (head.Count - 1);
            return intLong;
        }
        private string getGood_32(List<string> head) {
            return getGood_32(head, null);
        }
        
        private string getGood_32(List<string> head,List<string> detail)
        {
            string result = "";
            if (detail == null)
            {
                detail = head;
                result = head[0];
            }
            else {
                result = new string(' ', GetByeCount(head[0])); 
            } 
            int intLong = getGoodColumnLong_32(head);
            for (int i = 1; i < detail.Count; i++)
            {
                if (i == detail.Count - 1)
                {
                    intLong = Cols - GetByeCount(result);
                }
                int itemLong = intLong - GetByeCount(detail[i]);
                
                result = result + new string(' ', itemLong<0?0:itemLong) + detail[i];
            }
            return result;
        }
        public void PrintTitle(string title, Fonts state) {
            Append(_command.FontMode.Bold(PrinterModeState.On));
                      
            DoubleHeight2();
            Append(title);
            NormalHeight();

            Append(_command.FontMode.Bold(PrinterModeState.Off));
        }
        public void PrintCommon(PrintCommonClass data)
        {

            //OpenDrawer();
            SetLineHeight((byte)data.lineheight);//行间隔
            NewLine();
            AlignCenter();
            PrintTitle(data.storename, Fonts.FontE);

            NewLines(2);
            AlignLeft();
            int cnt = 0;
            String size = data.size ;
            foreach (Dictionary<string, object> dataSet in data.groups)
            {
                if (cnt++>0) Separator();
                Dictionary<string, object> style=null;
                if (dataSet.ContainsKey("style"))
                {
                    style = (Dictionary<string, object>)dataSet["style"];
                }
                foreach (KeyValuePair<string, object> item in dataSet)  //查找某个字段与值
                {
                    if (item.Key == "style") continue;
                    String title = item.Key.StartsWith("l_") ? "" : (item.Key + "：");
                    Append(title + Convert.ToString(item.Value), style, size);
                }
               
            }
            if (string.IsNullOrEmpty(data.qrcode))
            {
                NewLines(6);
                PartialPaperCut();
            }
            PrintDocument();
        }
        
        private void SetSize(String size,PrinterModeState state) {
            switch (size) {
                case "1":
                    DoubleHeight2();
                    break;
                case "2":
                    
                    break;
                case "3":
                    CondensedMode(state);
                    break;
                default:
                    
                    break;
            }           
        }
        private void Append(string v, Dictionary<string, object> style,String size)
        {
            SetSize(size, PrinterModeState.On);
            if (style != null) {
                if (style.ContainsKey("size")) {
                    string title = "：";
                    string value = "";
                    if (v.Split('：').Length > 0) title = v.Split('：')[0]+ title;
                    if (v.Split('：').Length > 1) value = v.Split('：')[1];
                    AppendString(title,false);
                    SetSize(size, PrinterModeState.Off);
                    DoubleHeight2();
                    Append(value);
                    SetSize(size, PrinterModeState.On);
                }
            }
            else
            {
                Append(v);
                
            }
            SetSize(size, PrinterModeState.Off);
        }

        public void PrintNomal(PrintNomalClass data) {

            //OpenDrawer();
            SetLineHeight(60);//行间隔
            NewLine();
            AlignCenter();
            PrintTitle(data.storename,Fonts.FontE);
            
            NewLines(2);
            AlignLeft();
            if(!data.orderid.IsNullOrEmpty()) Append("销售单号：" + data.orderid);
            if (!data.createtime.IsNullOrEmpty()) Append("交易时间：" + data.createtime);
            Append("收银员：" + data.operater);
            Separator();
                List<string> head = new List<string>("商品,数量,单价,小计".Split(','));
                Append(getGood_32(head));

                double amt = 0;
                foreach (var obj in data.goods)  //查找某个字段与值
                {
                    string prodname = string.Empty;
                    string prodprc = string.Empty;
                    string prodqty = string.Empty;
                    prodname = obj.name.IsNullOrEmpty() ? obj.good_name : obj.name;
                    prodprc = obj.sale_price.IsNullOrEmpty() ? obj.price : obj.sale_price;
                    prodqty = obj.number.IsNullOrEmpty() ? obj.qty : obj.number;
                    string goodtotal = (double.Parse(prodprc) * double.Parse(prodqty)).ToString("f2");

                    Append(prodname);
                    List<string> detail = new List<string>();
                    detail.Add("");
                    detail.Add(prodqty);
                    detail.Add(prodprc);
                    detail.Add(goodtotal);
                    Append(getGood_32(head, detail));
                    amt += double.Parse(prodprc) * double.Parse(prodqty);

                }
                Separator();

                Append("合计：" + amt.ToString("f2"));
                NewLines(2);
                Append("应付：" + (double.Parse(data.pay_amt) - double.Parse(data.change_amt)).ToString("f2"));
                Append(data.accts + "：" + data.pay_amt);
                Append("找零：" + data.change_amt);

                if (!data.member_money_name.IsNullOrEmpty()) Append(data.member_money_name + "：" + data.member_money_value);//会员余额
                if (!data.member_mobile_name.IsNullOrEmpty()) Append(data.member_mobile_name + "：" + data.member_mobile_value);//会员手机

                Separator();
            Append("打印时间：" + DateTime.Now.ToString("g"));
            Append(data.remark);
            //AlignCenter();
            //Append("谢谢惠顾，欢迎下次光临！");
            NewLines(6);
            PartialPaperCut();
            PrintDocument();
        }
        public void BoldMode(string value)
        {
            Append(_command.FontMode.Bold(value));
        }

        public void BoldMode(PrinterModeState state)
        {
            Append(_command.FontMode.Bold(state));
        }

        public void Font(string value, Fonts state)
        {
            Append(_command.FontMode.Font(value, state));
        }
        public void FontSize(string value, byte size)
        {
            Append(_command.FontMode.FontSize(value, size));
        }

        public void UnderlineMode(string value)
        {
            Append(_command.FontMode.Underline(value));
        }

        public void UnderlineMode(PrinterModeState state)
        {
            Append(_command.FontMode.Underline(state));
        }

        public void ExpandedMode(string value)
        {
            Append(_command.FontMode.Expanded(value));
        }

        public void ExpandedMode(PrinterModeState state)
        {
            Append(_command.FontMode.Expanded(state));
        }

        public void CondensedMode(string value)
        {
            Append(_command.FontMode.Condensed(value));
        }

        public void CondensedMode(PrinterModeState state)
        {
            Append(_command.FontMode.Condensed(state));
        }

        public void NormalHeight()
        {
            Append(_command.FontHeight.Normal());
        }

        public void DoubleHeight2()
        {
            Append(_command.FontHeight.DoubleHeight2());
        }

        public void DoubleHeight3()
        {
            Append(_command.FontHeight.DoubleHeight3());
        }

        public void NormalWidth()
        {
            Append(_command.FontWidth.Normal());
        }

        public void DoubleWidth2()
        {
            Append(_command.FontWidth.DoubleWidth2());
        }

        public void DoubleWidth3()
        {
            Append(_command.FontWidth.DoubleWidth3());
        }

        public void AlignLeft()
        {
            Append(_command.Alignment.Left());
        }

        public void AlignRight()
        {
            Append(_command.Alignment.Right());
        }

        public void AlignCenter()
        {
            Append(_command.Alignment.Center());
        }

        public void FullPaperCut()
        {
            Append(_command.PaperCut.Full());
        }

        public void PartialPaperCut()
        {
            Append(_command.PaperCut.Partial());
        }

        public void OpenDrawer()
        {
            Append(_command.Drawer.Open());
        }

        public void QrCode(string qrData)
        {
            Append(_command.QrCode.Print(qrData));
        }

        public void QrCode(string qrData, QrCodeSize qrCodeSize )
        {
            Append(_command.QrCode.Print(qrData, qrCodeSize));
        }

        public void Code128(string code, Positions printString = Positions.NotPrint)
        {
            Append(_command.BarCode.Code128(code,  printString));
        }

        public void Code39(string code, Positions printString=Positions.NotPrint)
        {
            Append(_command.BarCode.Code39(code,  printString));
        }

        public void Ean13(string code, Positions printString = Positions.NotPrint)
        {
            Append(_command.BarCode.Ean13(code,  printString));
        }

        public void InitializePrint()
        {
            RawPrinterHelper.SendBytesToPrinter(_printerName, _command.InitializePrint.Initialize());
        }

        public void Image(Bitmap image)
        {
            Append(_command.Image.Print(image));
        }
        public void NormalLineHeight()
        {
            Append(_command.LineHeight.Normal());
        }

        public void SetLineHeight(byte height)
        {
            Append(_command.LineHeight.SetLineHeight(height));
        }
        public void PrintOnline(PrintOnlineClass data)
        {
            //public string storename = "";//标题
            //public string pay_type = "";//支付方式
            //// --------------------------------------------------------------------
            //public string customer_remark = "";//买家备注
            //// --------------------------------------------------------------------
            //public string createtime = "";//交易时间
            //public string orderid = "";//订单编号
            //// --------------------------------------------------------------------
            //public string shipping_type = "";//配送方式 （”门店自提“ ”商家配送“）
            //public string customer_name = "";//买家姓名
            //public string customer_mobile = "";//买家手机
            //public string shipping_address_title = "";//配送地址（”提货点”，”收货地址”）
            //public string shipping_address = "";//配送地址（提货点，收货地址）
            //// --------------------------------------------------------------------
            //public List<GoodItem> goods = new List<GoodItem>();
            //// --------------------------------------------------------------------
            //public string pay_amt = "";//应付
            //public string accts = "";//付款方式（”会员支付“ ”货到付款“)
            //public string actual_amt = "";//会员实际支付金额 
            //// --------------------------------------------------------------------
            ////打印时间
            //public string remark = "";//系统由掌柜智囊提供

            //OpenDrawer();
            SetLineHeight(60);//行间隔
            AlignCenter();
            PrintTitle(data.storename, Fonts.FontE);//店铺名
            PrintTitle(data.pay_type, Fonts.SpecialFontB);//支付方式
            AlignLeft();
            Separator();
            if (!data.customer_remark.IsNullOrEmpty())
            {
                BoldMode("买家备注：" + data.customer_remark);
                Separator();
            }

            if (!data.createtime.IsNullOrEmpty()) Append("交易时间：" + data.createtime);
            if (!data.orderid.IsNullOrEmpty()) Append("订单号：" + data.orderid);

            Separator();
            PrintTitle(data.shipping_type, Fonts.FontE);//配送方式 （”门店自提“ ”商家配送“）
            NewLine();
            Append("姓名：" + data.customer_name);
            Append("手机号：" + data.customer_mobile);
            Append(data.shipping_address_title + "：" + data.shipping_address);

            Separator();
            List<string> head = new List<string>("商品,数量,单价,小计".Split(','));
            Append(getGood_32(head));
            double amt = 0;
            foreach (var obj in data.goods)  //查找某个字段与值
            {
                string prodname = string.Empty;
                string prodprc = string.Empty;
                string prodqty = string.Empty;
                prodname = obj.name.IsNullOrEmpty() ? obj.good_name : obj.name;
                prodprc = obj.sale_price.IsNullOrEmpty() ? obj.price : obj.sale_price;
                prodqty = obj.number.IsNullOrEmpty() ? obj.qty : obj.number;
                string goodtotal = (double.Parse(prodprc) * double.Parse(prodqty)).ToString("f2");

                Append(prodname);
                List<string> detail = new List<string>();
                detail.Add("");
                detail.Add(prodqty);
                detail.Add(prodprc);
                detail.Add(goodtotal);
                Append(getGood_32(head, detail));
                amt += double.Parse(prodprc) * double.Parse(prodqty);

            }
            Separator();

            Append("合计：" + amt.ToString("f2"));
            NewLines(2);

            Append("应付：" + (double.Parse(data.pay_amt)).ToString("f2"));
            Append(data.accts + ((!data.actual_amt.IsNullOrEmpty()) ? ("：" + data.actual_amt) : ""));

            Separator();
            Append("打印时间：" + DateTime.Now.ToString("g"));
            Append(data.remark);
            //AlignCenter();
            //Append("谢谢惠顾，欢迎下次光临！");
            NewLines(6);
            PartialPaperCut();
            PrintDocument();
        }
    }
}

