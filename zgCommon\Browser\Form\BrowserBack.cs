﻿using CefSharp;
using CefSharp.WinForms;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using zgAdvert;
using zgAdvert.Controls;
using zgLogging;
using zgpos.EventHandlers;
using zgUtils;
using zgUtils.Controls;
using zppos.Controls;

namespace zgpos
{
    public class BrowserBack : BrowserBase
    {
        //AdSlotControl slot;
        //AdSlotModel adSlotModel = new AdSlotModel();
        AdvertHtmlControl advert = null;
        private SplitContainer splitContainer = null;
        private int priceisshow ;
        static string address = (string.IsNullOrEmpty(App.MainPageUrl) ? CommonApp.ConfigBase.MainPageUrl : App.MainPageUrl+"/static") + @"/screen2.html?" + Guid.NewGuid().ToString("N");

        public override void AfterInit()
        {
            browser.ConsoleMessage += DoConsoleMessage;
            browser.JavascriptMessageReceived += DoJavascriptMessage;
        }
        public BrowserBack(FormBack form):base(form, address)
        {
            Log.WriterNormalLog("副屏广告初始化开始：BrowserBack");
            splitContainer = form.splitContainer1;
            form.splitContainer1.Panel2.Controls.Add(browser);
            Advert_init();
            
        }
        public static void DoConsoleMessage(object sender, ConsoleMessageEventArgs e)
            => Log.WriterNormalLog($"{e.Source} [{e.Level}] {e.Line} => {e.Message}");

        public static void DoJavascriptMessage(object sender, JavascriptMessageReceivedEventArgs e)
            => Log.WriterNormalLog($"{e.Browser} => {e.Frame.Name} {e.Message}");

        public void Advert_close()
        {
            ////释放广告位组件
            //if (slot != null)
            //{
            //    slot.AdControlDispose();
            //    //释放 物联网
            //    YxfSdkManager.Instance.Dispose();
            //}
            if (advert != null) {
                advert.AdControlDispose();
            }
        }
        public void switchwindow(int priceisshow)
        {
            try
            {
                this.splitContainer.InvokeOnUiThreadIfRequired(() => {
                    if (this.priceisshow == priceisshow) return;
                    this.priceisshow = priceisshow;
                    if (priceisshow == 1)
                    {  //半屏
                    
                            this.splitContainer.Panel2Collapsed = false; 
                            int top = (int)(splitContainer.Height * 0.3) / 2;
                            splitContainer.Panel1.Padding = new System.Windows.Forms.Padding(0, top, 0, top);
                   
                    

                    }
                    else
                    {    //全屏
                        splitContainer.Panel2Collapsed = true;
                        splitContainer.Panel1.Padding = new System.Windows.Forms.Padding(0, 0, 0, 0);
                    }
                    splitContainer.Panel1.Refresh();
                });

            }
            catch (Exception ex) {
                Log.WriterExceptionLog(ex.Message);
            }
            
        }
        private void Advert_init(bool? isShowAd=null)
        {
            AdvertFactory.Instance.ReloadAll();
            splitContainer.Panel1?.Invoke(new Action(delegate
            {
                if (!string.IsNullOrEmpty(CommonApp.sysUid))
                {
                    zgAdvert.AdvertFactory.isShowAd = isShowAd??CommonApp.settings?.setting?.show_ad?.Equals("1") ?? false;
                    zgAdvert.AdvertFactory.Instance.Init(CommonApp.Config.ServerUrl.AdvertBaseUrl, CommonApp.Config.Base.AdvertLocalUrl, CommonApp.Directory, CommonApp.Authorization, CommonApp.DeviceId);

                }
                if (advert == null)
                {
                    splitContainer.Panel1.Controls.Clear();
                    //splitContainer.Panel1.Dispose();
                    splitContainer.Panel1.Refresh();
                    advert = new AdvertHtmlControl();
                    advert.Init();
                    advert.Dock = DockStyle.Fill;
                    splitContainer.Panel1.Controls.Add(advert);
                }
                else
                { 
                    advert.InitAds(); 
                }
            }));

        }
        public void reloadAdvertData(bool? isShowAd=null) {
            //AdvertFactory.isShowAd = true;
            
            Advert_init(isShowAd);
        }
        public Boolean Reload(object data) {
//#if DEBUG
//            if(this.browser!=null && this.browser.IsBrowserInitialized) this.browser.ShowDevTools();
//#endif
            string jsonstr=JsonConvert.SerializeObject(data);
            RunJS("makeHtml("+ jsonstr + ");");
            var dict = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonstr);
            if (dict["screen2ShowList"] == null)
            {
                switchwindow(0);
            }
            else
            {
                switchwindow(1);
            }
            return true;
        }

        internal void CloseAll()
        {
            try {
                if (App.browserBackObj != null)
                {
                    App.browserBackObj.Advert_close();
                    App.browserBackObj.CloseApp();
                    App.browserBackObj = null;
                }
            }
            catch (Exception ex) {
                Log.WriterExceptionLog(ex.Message);
            }
        }

        internal void Reload_FromUpSetting()
        {
            //if (advert!=null&&!advert.showad.Equals(string.IsNullOrEmpty(UserHelper.settings.setting.show_ad) ? "1" : UserHelper.settings.setting.show_ad))reloadAdvertData();

        }
    }
}
