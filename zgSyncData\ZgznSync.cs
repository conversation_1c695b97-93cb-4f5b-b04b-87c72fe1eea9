﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using zgSyncData.Model;

namespace zgSyncData
{
    public class ZgznSync
    {
        protected RestClient restClient = new RestClient();
        public void Execute() {
			


			/*
            Request URL: https://dev.zhangguizhinang.com/duobao-users/sync/syncCheck?ver=3.3.0.0401
                req:{"syncHistoryId":null,"deviceCode":1}
                resp:{
	                    "code":200,
	                    "data":{
		                    "createAt":"2022-04-09 10:55:54.543649",
		                    "id":171,
		                    "modify":false,
		                    "syncedAt":""
	                    },
	                    "msg":"SERVER_SUCCESS",
	                    "status":200
                    }
            Request URL: https://dev.zhangguizhinang.com/duobao-users/sync/getSyncData?ver=3.3.0.0401
                req：[{"syncAt":"2000-01-01","endSyncAt":"2022-04-09 10:55:54.543649","tbl":"sysinfo"},{"syncAt":"2000-01-01","endSyncAt":"2022-04-09 10:55:54.543649","tbl":"shifthistories"}]
                resp:{
						"code":200,
						"data":[
							{
								"endSyncAt":"2022-04-09 10:55:54.913",
								"cnt":1,
								"startSyncAt":"2000-01-01",
								"tbl":"sysinfo"
							},
							{
								"endSyncAt":"2022-04-09 10:55:54.918",
								"cnt":65,
								"startSyncAt":"2000-01-01",
								"tbl":"shifthistories"
							}
						],
						"msg":"SERVER_SUCCESS",
						"status":200
					}
			https://dev.zhangguizhinang.com/duobao-pos/sync/downSales?ver=3.3.0.0401
				req:{"currentPage":1,"pageSize":500,"startSyncAt":null,"endSyncAt":"2022-04-09 10:55:54.543649"}
			resp:[
					{
						"acctId":1,
						"acctSyncG":"",
						"billAmt":38.0,
						"blendPays":[],
						"changeAmt":0.0,
						"code":"XSD20220100007.1",
						"companyId":0,
						"companySyncG":"",
						"createAt":"2022-01-12 14:06:09.000",
						"deductionAmt":0.0,
						"disc":1.0,
						"discAmt":38.0,
						"expressAmt":0.0,
						"id":1,
						"info1":"",
						"info2":"",
						"isCalc":0,
						"isDel":0,
						"isNew":1,
						"isSync":0,
						"items":[
							{
								"amt":38.0,
								"disc":1.0,
								"id":1,
								"info1":"",
								"info2":"",
								"isDel":0,
								"isSync":0,
								"itemDisc":1.0,
								"mprc":0.0,
								"mqty":0.0,
								"oprc":0.0,
								"prc":19.0,
								"productId":0,
								"productSyncG":"715711ba574c7134aae3d587b8653b02",
								"qty":2.0,
								"remark":"",
								"saleId":1,
								"saleSyncG":"7f72d081-3cd8-42f6-b3ff-f197063c2ae5",
								"sorderSyncG":"",
								"syncAt":"2022-01-12 14:09:43.058788",
								"syncG":"8f0d8ab1-c88d-4a87-9359-f215b9bb421e",
								"syncN":0,
								"syncV":"1",
								"sysSid":1,
								"sysUid":"18053510016",
								"utye":1
							}
						],
						"optOn":"2022-01-12",
						"oweAmt":38.0,
						"payAmt":38.0,
						"remark":"",
						"reviseAt":"2022-01-12 14:06:09.000",
						"sorderId":0,
						"sorderSyncG":"",
						"syncAt":"2022-01-12 14:09:43.058788",
						"syncG":"7f72d081-3cd8-42f6-b3ff-f197063c2ae5",
						"syncN":0,
						"syncU":"",
						"syncV":"1",
						"sysSid":1,
						"sysUid":"18053510016",
						"tradeSrc":0,
						"tradeType":0,
						"tye":1,
						"uid":1,
						"userId":0,
						"userSyncG":"",
						"username":"",
						"vipid":0,
						"vipmobile":"",
						"vipname":""
					}]
            */
		}

		private SyncCheckResult syncCheck(string authorization,object checkParm) {
			var checkResp = restClient.RequestPost<ResponseData>(SyncConfig.URL_SYNC_CHECK, authorization, checkParm);
			if (checkResp.StatusCode != System.Net.HttpStatusCode.OK)
			{
				//记录LOG
				return null;
			}
			var jsonResult = new JObject(checkResp.Data);
			var result = jsonResult.ToObject<SyncCheckResult>();
			return result;
		}
	
		 
	}
}
