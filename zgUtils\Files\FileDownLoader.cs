﻿using Newtonsoft.Json.Linq;
using System;
using System.IO;
using System.Net;
using zgLogging;
namespace zgUtils.Files
{

    public class FileDownLoader
	{
		private static FileDownLoader instance;

		public static FileDownLoader Instance
		{
			get
			{
				if (instance == null)
				{
                    instance = new FileDownLoader();
					return instance;
				}
				return instance;
			}
		}
		
		public void DowLoadFile(string url, string savePath, Action<double> progressCallBack, Func<bool> isCancelDownLoad, string recordpath, Action<EDownLoadState> compelete, long size = 0L)
		{
			if (isCancelDownLoad())
			{
				return;
			}
			FileStream fileStream;
			long num;
			if (File.Exists(savePath))
			{
				fileStream = File.OpenWrite(savePath);
				num = fileStream.Length;
				fileStream.Seek(num, SeekOrigin.Current);
			}
			else
			{
				string directoryName = Path.GetDirectoryName(savePath);
				if (!Directory.Exists(directoryName))
				{
					Directory.CreateDirectory(directoryName);
				}
				fileStream = new FileStream(savePath, FileMode.Create);
				num = 0L;
			}
			HttpWebRequest httpWebRequest = null;
			try
			{
				if (size == 0L)
				{
					size = GetFileContentLength(url);
					if (size != 0L)
					{
						File.WriteAllText(recordpath, size.ToString());
					}
				}
				if (size != 0L && size == num)
				{
					fileStream.Close();
					if (File.Exists(recordpath))
					{
						File.Delete(recordpath);
					}
					string destFileName = savePath.Remove(savePath.Length - 4, 4);
					File.Move(savePath, destFileName);
					compelete(EDownLoadState.Success);
				}
				else if (size == 0L)
				{
					fileStream.Close();
					//this.DowLoadFile(url, savePath, progressCallBack, isCancelDownLoad, recordpath, compelete, size);
				}
				else
				{
					httpWebRequest = (HttpWebRequest)WebRequest.Create(url);
					httpWebRequest.ReadWriteTimeout = 20000;
					httpWebRequest.Timeout = 35000;
					if (num > 0L)
					{
						httpWebRequest.AddRange((int)num);
					}
					WebResponse response = httpWebRequest.GetResponse();
					long num2 = response.ContentLength + num;
					long num3 = num;
					int num4 = (int)(num3 / num2 * 100L);
					Log.WriterNormalLog(string.Format("{0}--->{1}", url, num4.ToString()));
					Stream responseStream = response.GetResponseStream();
					byte[] buffer = new byte[512];
					int num5 = 0;
					do
					{
						fileStream.Write(buffer, 0, num5);
						num5 = responseStream.Read(buffer, 0, 512);
						num3 += num5;
						if (num4 < (int)(num3 / (double)num2 * 100.0))
						{
							num4 = (int)(num3 / (double)num2 * 100.0);
							progressCallBack(num4);
						}
						fileStream.Flush();
					}
					while (num5 > 0 && !isCancelDownLoad());
					responseStream.Close();
					fileStream.Close();
					if (num3 != num2 && !isCancelDownLoad())
					{
						throw new Exception();
					}
					if (httpWebRequest != null)
					{
						httpWebRequest.Abort();
						if (isCancelDownLoad())
						{
							compelete(EDownLoadState.Cancel);
						}
						else
						{
							if (File.Exists(recordpath))
							{
								File.Delete(recordpath);
							}
							string destFileName2 = savePath.Remove(savePath.Length - 4, 4);
							File.Move(savePath, destFileName2);
							Log.WriterNormalLog("下载成功：->" + url);
							compelete(EDownLoadState.Success);
						}
					}
				}
			}
			catch (Exception ex)
			{
				Log.WriterExceptionLog(string.Format("下载失败：" + ex.Message, "----->" + url));
				if (httpWebRequest != null)
				{
					httpWebRequest.Abort();
				}
				ex.ToString();
				fileStream.Close();
				//this.DowLoadFile(url, savePath, progressCallBack, isCancelDownLoad, recordpath, compelete, size);
			}
		}
				
		public void SimpleDownLoadFile(string url, string savePath)
		{
			try
			{
				//HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
				//Stream responseStream = ((HttpWebResponse)request.GetResponse()).GetResponseStream();

				zgUtils.Controls.Req<string> req = new zgUtils.Controls.Req<string>(url, "");
				byte[] array = zgUtils.Controls.ApiHelper.HttpDownloadData(req).Result.RespData;
				FileStream fileStream = new FileStream(savePath, FileMode.Create);
				fileStream.Write(array, 0, array.Length);
				fileStream.Close();
				fileStream.Dispose();

				//using (MemoryStream memoryStream = new MemoryStream())
				//{
				//	responseStream.CopyTo(memoryStream);
				//	byte[] array = memoryStream.ToArray();
				//	responseStream.Close();
				//	FileStream fileStream = new FileStream(savePath, FileMode.Create);
				//	fileStream.Write(array, 0, array.Length);
				//	fileStream.Close();
				//	fileStream.Dispose();
				//}
			}
			catch (Exception ex)
			{
				Log.WriterExceptionLog(ex.Message);
			}
		}
				
		public long GetFileContentLength(string url)
		{
			HttpWebRequest httpWebRequest = null;
			long result;
			try
			{
				httpWebRequest = (HttpWebRequest)WebRequest.Create(url);
				httpWebRequest.Timeout = 35000;
				httpWebRequest.ReadWriteTimeout = 20000;
				WebResponse response = httpWebRequest.GetResponse();
				httpWebRequest.Abort();
				result = response.ContentLength;
			}
			catch
			{
				if (httpWebRequest != null)
				{
					httpWebRequest.Abort();
				}
				result = 0L;
			}
			return result;
		}
	}
}
