﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using zgLogging;

namespace zgUtils.Security
{

    /// <summary>
    /// Generates a 16 byte Unique Identification code of a computer
    /// Example: 4876-8DB5-EE85-69D3-FE52-8CF7-395D-2EA9
    /// </summary>
    public class FingerPrint
    {
        private static string fingerPrint = string.Empty;
        public static string Value()
        {
            try
            {
                Stopwatch sw = new Stopwatch();
                sw.Start();
                if (string.IsNullOrEmpty(fingerPrint))
                {
                    var info = "CPU >> " + cpuId() +
                        "\nBIOS >> " + biosId() +
                        "\nBASE >> " + baseId() +
                        "\nDISK >> " + diskId() +
                        "\nVIDEO >> " + videoId() +
                        "\nMAC >> " + macId();
                    Log.Info(info);
                    fingerPrint = GetHash(info);
                }
                sw.Stop();
                var time = sw.ElapsedMilliseconds;
            }
            catch (Exception ex) {
                Log.WriterExceptionLog(ex.Message);
            }
            return fingerPrint;
        }
        private static string GetHash(string s)
        {
            MD5 sec = new MD5CryptoServiceProvider();
            ASCIIEncoding enc = new ASCIIEncoding();
            byte[] bt = enc.GetBytes(s);
            return GetHexString(sec.ComputeHash(bt));
        }
        private static string GetHexString(byte[] bt)
        {
            string s = string.Empty;
            for (int i = 0; i < bt.Length; i++)
            {
                byte b = bt[i];
                int n, n1, n2;
                n = (int)b;
                n1 = n & 15;
                n2 = (n >> 4) & 15;
                if (n2 > 9)
                    s += ((char)(n2 - 10 + (int)'A')).ToString();
                else
                    s += n2.ToString();
                if (n1 > 9)
                    s += ((char)(n1 - 10 + (int)'A')).ToString();
                else
                    s += n1.ToString();
                if ((i + 1) != bt.Length && (i + 1) % 2 == 0) s += "-";
            }
            return s;
        }
        #region Original Device ID Getting Code
        //Return a hardware identifier
        private static string identifier
        (string wmiClass, string wmiProperty, string wmiMustBeTrue)
        {
            string result = "";
            System.Management.ManagementClass mc =
        new System.Management.ManagementClass(wmiClass);
            System.Management.ManagementObjectCollection moc = mc.GetInstances();
            foreach (System.Management.ManagementObject mo in moc)
            {
                if (mo[wmiMustBeTrue].ToString() == "True")
                {
                    //Only get the first one
                    if (result == "")
                    {
                        try
                        {
                            result = mo[wmiProperty].ToString();
                            break;
                        }
                        catch
                        {
                        }
                    }
                }
            }
            return result;
        }
        //Return a hardware identifier
        private static string identifier(string wmiClass, string wmiProperty)
        {
            string result = "";
            System.Management.ManagementClass mc =
        new System.Management.ManagementClass(wmiClass);
            System.Management.ManagementObjectCollection moc = mc.GetInstances();
            foreach (System.Management.ManagementObject mo in moc)
            {
                //Only get the first one
                if (result == "")
                {
                    try
                    {
                        if (mo[wmiProperty] != null) result = mo[wmiProperty].ToString();
                        break;
                    }
                    catch
                    {
                    }
                }
            }
            return result;
        }

        private static Dictionary<string, string> identifier(string wmiClass, List<string> wmiPropertyList)
        {
            Dictionary<string, string> result = new Dictionary<string, string>();
            System.Management.ManagementClass mc = new System.Management.ManagementClass(wmiClass);
            System.Management.ManagementObjectCollection moc = mc.GetInstances();
            foreach (System.Management.ManagementObject mo in moc)
            {
                //Only get the first one
                if (result.Keys.Count > 0)
                {
                    break;
                }

                try
                {
                    foreach (var wmiProperty in wmiPropertyList)
                    {
                        if (mo[wmiProperty] != null)
                        {
                            result[wmiProperty] = mo[wmiProperty].ToString();
                        }
                    }
                    break;
                }
                catch
                {
                }
            }
            wmiPropertyList.ForEach(i => {
                if (!result.ContainsKey(i))
                {
                    result[i] = null;
                }
            });
            return result;
        }

        private static string cpuId()
        {
            try
            {
                //Uses first CPU identifier available in order of preference
                //Don't get all identifiers, as it is very time consuming
                List<string> propertyNameList = new List<string>() {
                "UniqueId","ProcessorId","Name","Manufacturer","MaxClockSpeed"
                };
                var propertyDict = identifier("Win32_Processor", propertyNameList);

                string retVal = propertyDict["UniqueId"];
                if (retVal == "") //If no UniqueID, use ProcessorID
                {
                    retVal = propertyDict["ProcessorId"];
                    if (retVal == "") //If no ProcessorId, use Name
                    {
                        retVal = propertyDict["Name"];
                        if (retVal == "") //If no Name, use Manufacturer
                        {
                            retVal = propertyDict["Manufacturer"];
                        }
                        //Add clock speed for extra security
                        retVal += propertyDict["MaxClockSpeed"];
                    }
                }
                return retVal;
            }
            catch (Exception ex) {
                Log.WriterExceptionLog(ex.Message);
                return string.Empty;
            }
            
        }
        //BIOS Identifier
        private static string biosId()
        {
            try {
                string result = string.Empty;
                List<string> propertyNameList = new List<string>() {
                "Manufacturer","SMBIOSBIOSVersion","IdentificationCode","SerialNumber","ReleaseDate","Version"
                };
                var propertyDict = identifier("Win32_BIOS", propertyNameList);
                propertyNameList.ForEach(i => result += propertyDict[i]);
                return result;
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
                return string.Empty;
            }
            
        }
        //Main physical hard drive ID
        private static string diskId()
        {
            try
            {
                string result = string.Empty;
                List<string> propertyNameList = new List<string>() {
                "Model","Manufacturer","Signature","TotalHeads"
                };
                var propertyDict = identifier("Win32_DiskDrive", propertyNameList);
                propertyNameList.ForEach(i => result += propertyDict[i]);
                return result;
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
                return string.Empty;
            }
            
        }
        //Motherboard ID
        private static string baseId()
        {
            try
            {
                string result = string.Empty;
                List<string> propertyNameList = new List<string>() {
                "Model","Manufacturer","Name","SerialNumber"
                };
                var propertyDict = identifier("Win32_BaseBoard", propertyNameList);
                propertyNameList.ForEach(i => result += propertyDict[i]);
                return result;
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
                return string.Empty;
            }
            
        }
        //Primary video controller ID
        private static string videoId()
        {
            try
            {
                string result = string.Empty;
                List<string> propertyNameList = new List<string>() {
                "DriverVersion","Name"
                };
                var propertyDict = identifier("Win32_VideoController", propertyNameList);
                propertyNameList.ForEach(i => result += propertyDict[i]);
                return result;
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
                return string.Empty;
            }
           
        }
        //First enabled network card ID
        private static string macId()
        {
            try
            {
                return identifier("Win32_NetworkAdapterConfiguration",
                "MACAddress", "IPEnabled");
            }
            catch (Exception ex)
            {
                Log.WriterExceptionLog(ex.Message);
                return string.Empty;
            }
            
        }
        #endregion
    }
}
