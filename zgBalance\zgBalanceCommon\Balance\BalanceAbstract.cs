﻿using CefSharp;
using System.Collections.Generic;
using zgBalanceCommon.Model;

namespace zgBalanceCommon.Balance
{
    public abstract class BalanceAbstract
    {
        /// <summary>
        /// 下发条码到电子秤
        /// </summary>
        /// <param name="goodsInfo"></param>
        /// <returns></returns>
        //public abstract void SendGoods(string ip, int port, List<BasePLU> goodsInfo, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null);

        /// <summary>
        /// 设置收银机标题
        /// </summary>
        /// <param name="title"></param>
        /// <returns></returns>
        public abstract void SetTitle(string title);

        /// <summary>
        /// 设置热键
        /// </summary>
        /// <param name="keyList"></param>
        public abstract void SetHotKey(List<object> keyList);

        /// <summary>
        /// 解析条码内容
        /// </summary>
        /// <param name="barCode">条码</param>
        /// <param name="checkSuccess">校验是否通过</param>
        /// <returns></returns>
        public abstract void ScanBarCode(string barCode, IJavascriptCallback onsuccess = null, IJavascriptCallback onfail = null);

        /// <summary>
        /// 读取标签尺寸
        /// </summary>
        /// <param name="width"></param>
        /// <param name="height"></param>
        public abstract void ReadTagSize(out int width,out int height);

        /// <summary>
        /// 创建PLU实例
        /// </summary>
        /// <returns></returns>
        public abstract BasePLU CreatePLUInstance();
    }
}
