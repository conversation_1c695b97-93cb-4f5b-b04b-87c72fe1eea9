﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Runtime.ExceptionServices;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using zgPrinter.Model;
using zgPrinter.Printer.ESCPOS.EpsonCommands;

namespace zgPrinter.Printer.DrawImg
{
    public class GraphicsPrinter : AbstractPrinter
    {
        #region Property

        Brush brush = Brushes.Black;//画笔
        PrintDocument printDoc = new PrintDocument();
        Font fontTitle = new Font("Impact", 15, FontStyle.Bold);//标题字体
        Font fontContent = new Font("宋体", 7, FontStyle.Bold);//内容字体
        StringFormat formatLeft;//文本居左
        StringFormat formatCenter;//文本居中
        StringFormat formatRight;//文本居右

        /// <summary>
        /// 走纸行数
        /// </summary>
        public int SpaceLine { get; set; } = 4;

        /// <summary>
        /// 标题字体
        /// </summary>
        public Font FontTitle
        {
            get => fontTitle;
            set => this.fontTitle = value;
        }

        /// <summary>
        /// 内容字体
        /// </summary>
        public Font FontContent
        {
            get => fontContent;
            set => this.fontContent = value;
        }

        /// <summary>
        /// 打印页面尺寸
        /// </summary>
        public PaperSize PaperSize { get; set; }

        #endregion

        public GraphicsPrinter(PaperSize paperSize)
        {
            this.PaperSize = paperSize;

            printDoc.PrintController = new System.Drawing.Printing.StandardPrintController();
            printDoc.DefaultPageSettings.PaperSize = this.PaperSize;
            printDoc.PrintPage += new PrintPageEventHandler(onPrintPage);

            formatLeft = new StringFormat(StringFormatFlags.NoClip);
            formatCenter = new StringFormat(formatLeft);
            formatRight = new StringFormat(formatLeft);

            formatCenter.Alignment = StringAlignment.Center;
            formatRight.Alignment = StringAlignment.Far;
            formatLeft.Alignment = StringAlignment.Near;
        }

        /// <summary>
        /// 读套打模板
        /// </summary>
        public void LoadRdlc(XElement xe, out PaperSize tagPaperSize)
        {
            List<PrintElement> list = new List<PrintElement>();
            var allElements = xe.Descendants();
            var items = allElements.First(i => "ReportItems".Equals(i.Name.LocalName));
            var body = allElements.First(i => "Body".Equals(i.Name.LocalName));
            var page = allElements.First(i => "Page".Equals(i.Name.LocalName)).Descendants();
            var pageHeight = page.First(i => "PageHeight".Equals(i.Name.LocalName)).Value.Replace("cm", "");
            var pageWidth = page.First(i => "PageWidth".Equals(i.Name.LocalName)).Value.Replace("cm", "");

            PaperSize currentPaperSize = new PaperSize();
            currentPaperSize.Width = (int)CentimeterToPixel(Convert.ToDouble(pageWidth) * 10);
            currentPaperSize.Height = (int)CentimeterToPixel(Convert.ToDouble(pageHeight) * 10);

            tagPaperSize = currentPaperSize;
            foreach (var item in items.Elements())
            {
                var itemElements = item.Descendants();

                var eleStyleT = itemElements.First(i => "TextRun".Equals(i.Name.LocalName)).Elements().First(i => "Style".Equals(i.Name.LocalName));
                var eleStyle = eleStyleT.Elements();
                var fontSize = eleStyle.FirstOrDefault(i => "FontSize".Equals(i.Name.LocalName))?.Value;
                var fontWeight = eleStyle.FirstOrDefault(i => "FontWeight".Equals(i.Name.LocalName))?.Value;

                var currentFont = fontContent;
                fontSize = fontSize ?? currentFont.Size.ToString();
                fontWeight = fontWeight ?? FontStyle.Regular.ToString();
                var f = new Font(currentFont.Name, float.Parse(fontSize.Replace("pt", "")), (FontStyle)Enum.Parse(typeof(FontStyle), fontWeight));

                var tagName = item.Attribute("Name").Value;
                var tagIndex = tagName.IndexOf("_");

                var attrName = tagName.Substring(tagIndex + 1);
                var tagType = tagName.Substring(0, tagIndex);

                var topCm = itemElements.First(i => i.Name.LocalName.Equals("Top")).Value.Replace("cm", "");
                var leftCm = itemElements.First(i => i.Name.LocalName.Equals("Left")).Value.Replace("cm", "");
                var heightCm = itemElements.First(i => i.Name.LocalName.Equals("Height")).Value.Replace("cm", "");
                var widthCm = itemElements.First(i => i.Name.LocalName.Equals("Width")).Value.Replace("cm", "");
                var top = CentimeterToPixel(Convert.ToDouble(topCm) * 10);
                var left = CentimeterToPixel(Convert.ToDouble(leftCm) * 10);
                var width = CentimeterToPixel(Convert.ToDouble(widthCm) * 10);
                var height = CentimeterToPixel(Convert.ToDouble(heightCm) * 10);
                if (!attrName.ToLower().Equals("code"))
                {
                    PrintElementStringBlock psb = new PrintElementStringBlock();
                    psb.X = (float)left;
                    psb.Y = (float)top;
                    psb.Width = (float)width;

                    psb.ContentFont = f;
                    if ("label".Equals(tagType))
                    {
                        psb.Content = itemElements.First(i => i.Name.LocalName.Equals("Value")).Value;
                        psb.DataMember = Guid.NewGuid().ToString();
                    }
                    else
                    {
                        psb.DataMember = attrName;
                    }
                    list.Add(psb);
                }
                else
                {
                    PrintElementImageBlock imgBlcok = new PrintElementImageBlock();
                    imgBlcok.X = (float)left;
                    imgBlcok.Y = (float)top;
                    imgBlcok.Width = (int)width;
                    imgBlcok.Height = (int)height;
                    imgBlcok.DataMember = attrName;
                    list.Add(imgBlcok);
                }
            }

            this.PrintItems = list;
        }

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="printerName">打印机名</param>
        /// <param name="printCount">打印份数</param>
        [HandleProcessCorruptedStateExceptions]
        public override void Print(string printerName, int printCount, bool isTag = false, int spaceLine = 4)
        {
            foreach (var item in this.PrintItems)
            {
                item.Content = null;
                if (item.ContentType != EnumContentType.Command && !DataSource.ContainsKey(item.DataMember))
                {
                    continue;
                }
                if (item.ContentType == EnumContentType.Image)
                {
                    var imgItem = item as PrintElementImage;
                    imgItem.Content = new Bitmap(DataSource[item.DataMember].ToString());
                }
                if (item.ContentType == EnumContentType.ImageBlock)
                {
                    var imgItem = item as PrintElementImageBlock;
                    imgItem.Content = DataSource[item.DataMember] as Bitmap;
                }
                if (item.ContentType == EnumContentType.GoodsTable)
                {
                    var gtItem = item as PrintElementGoodsDataTable;
                    gtItem.Content = DataSource[item.DataMember] as DataTable;
                }
                if (item.ContentType == EnumContentType.String)
                {
                    var strItem = item as PrintElementString;
                    strItem.Content = string.Format(strItem.Format, DataSource[item.DataMember]);
                }
                if (item.ContentType == EnumContentType.StringBlock)
                {
                    var strItem = item as PrintElementStringBlock;
                    if (DataSource.ContainsKey(item.DataMember))
                    {
                        strItem.Content = DataSource[item.DataMember].ToString();
                    }
                }
            }
            this.printDoc.PrinterSettings.PrinterName = printerName;

            if (!isTag)
            {
                var pageHeight = GetPaperHeight();
                printDoc.DefaultPageSettings.PaperSize = new PaperSize() { Width = this.PaperSize.Width, Height = (int)pageHeight + (spaceLine * 30) };
                printDoc.DefaultPageSettings.Margins = new Margins(0, 0, 0, 0);
            }
            for (int i = 0; i < printCount; i++)
            {
                this.printDoc.Print();
            }
            foreach (var item in this.PrintItems)
            {
                if (item.ContentType == EnumContentType.Image)
                {
                    var imgItem = item as PrintElementImage;
                    imgItem?.Content?.Dispose();
                }
            }

        }

        public override void NetPrint(string ip, int printCount, bool isTag = false, int port = 9100, int spaceLine = 2) { }

        #region private method

        /// <summary>
        /// 毫米转像素
        /// </summary>
        /// <param name="length"></param>
        /// <returns></returns>
        public static double CentimeterToPixel(double length)
        {
            System.Drawing.Graphics g = System.Drawing.Graphics.FromHwnd(IntPtr.Zero);
            IntPtr hdc = g.GetHdc();
            int width = GetDeviceCaps(hdc, 4);     // HORZRES
            int pixels = GetDeviceCaps(hdc, 8);     // BITSPIXEL
            g.ReleaseHdc(hdc);
            return (((double)pixels / (double)width) * (double)length);
        }

        [DllImport("gdi32.dll")]
        private static extern int GetDeviceCaps(IntPtr hdc, int Index);

        /// <summary>
        /// 打印事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="ev"></param>
        private void onPrintPage(object sender, PrintPageEventArgs ev)
        {
            var graphics = ev.Graphics;
            
            var printableArea = ev.PageSettings.PrintableArea;
            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
            graphics.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;
            graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
            graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;

            float startX = 0;
            float startY = this.LineHeight;
            float Offset = 0;
            var printItem = this.PrintItems.OrderBy(i => i.Index).ToList();

            foreach (var item in printItem)
            {
                if (!string.IsNullOrEmpty(item.DisplayGroup) && !DataSource.ContainsKey(item.DisplayGroup))
                {
                    continue;
                }
                if (item.ContentType != EnumContentType.Command && !DataSource.ContainsKey(item.DataMember))
                {
                    continue;
                }
                if (item.ContentType == EnumContentType.StringBlock)//处理文本类型
                {
                    printStringBlock(item, graphics);
                }
                if (item.ContentType == EnumContentType.String)//处理文本类型
                {
                    var layoutHeight = printString(item, graphics, printableArea, startX, startY + Offset);
                    Offset += layoutHeight == 0 ? 0 : (layoutHeight + this.LineHeight);
                }
                if (item.ContentType == EnumContentType.Image)//处理图片类型
                {
                    var yIndex = (int)(startY + Offset);
                    var layoutHeight = printImage(item, graphics, printableArea, (int)startX, yIndex);
                    Offset += layoutHeight == 0 ? 0 : (layoutHeight + this.LineHeight);
                }
                if (item.ContentType == EnumContentType.ImageBlock)//处理图片类型
                {
                    var layoutHeight = printImageBlock(item, graphics);
                }
                if (item.ContentType == EnumContentType.Table)//处理表格类型
                {
                    var layoutHeight = printDataTable(item, graphics, printableArea, (int)startX, (int)(startY + Offset), 2);
                    Offset += layoutHeight == 0 ? 0 : (layoutHeight + this.LineHeight);
                }
                if (item.ContentType == EnumContentType.GoodsTable)//处理表格类型
                {
                    var layoutHeight = printGoodsDataTable(item, graphics, printableArea, (int)startX, (int)(startY + Offset), 2);
                    Offset += layoutHeight == 0 ? 0 : (layoutHeight + this.LineHeight);
                }
                if (item.ContentType == EnumContentType.Command)//处理命令类型
                {
                    var printItemCmd = item as PrintElementCommand;
                    var cmd = printItemCmd.Content.ToString().ToUpper();
                    if (cmd.Equals(EnumPrintCommand.AddSeparatorLine.ToString().ToUpper()))
                    {
                        var layoutHeight = printSeparator(graphics, printableArea, startX, startY + Offset);
                        Offset += layoutHeight;
                    }
                    if (cmd.Equals(EnumPrintCommand.AddEmptyLine.ToString().ToUpper()))
                    {
                        var layoutHeight = printSeparator(graphics, printableArea, startX, startY + Offset, "  ");
                        Offset += layoutHeight;
                    }

                    Offset += this.LineHeight;
                }
            }

            if (SpaceLine > 0)
            {
                var fontHeight = fontContent.GetHeight(graphics) + LineHeight;
                graphics.DrawString(".", fontContent, brush, 0, Offset + (fontHeight * SpaceLine) - LineHeight);
            }
        }

        public float GetPaperHeight()
        {
            float Offset = 0;
            float startX = 0;
            float startY = this.LineHeight;
            using (Graphics graphics = Graphics.FromImage(new Bitmap(this.PaperSize.Width, 400)))
            {
                RectangleF printableArea = new RectangleF();
                printableArea.Width = this.PaperSize.Width;
                printableArea.Height = 400;
                foreach (var item in this.PrintItems)
                {
                    var contentType = item.ContentType;
                    //处理文本类型
                    if (EnumContentType.String == contentType)
                    {
                        var printItem = item as PrintElementString;
                        if (!string.IsNullOrEmpty(printItem.Content))
                        {
                            var layoutHeight = printString(item, graphics, printableArea, startX, startY + Offset);
                            Offset += (layoutHeight + this.LineHeight);
                        }
                    }

                    //处理图片类型
                    if (EnumContentType.Image == contentType)
                    {
                        var printItem = item as PrintElementImage;
                        var img = printItem.Content;
                        if (img != null)
                        {
                            var imgHeight = 0;
                            if (img.Width < printableArea.Width)
                            {
                                imgHeight = img.Height;
                            }
                            else
                            {
                                var bili = printableArea.Width / (float)img.Width;
                                imgHeight = (int)Math.Ceiling(img.Height * bili);
                            }
                            Offset += (imgHeight + this.LineHeight);
                        }
                    }

                    //处理命令类型
                    if (EnumContentType.Command == contentType)
                    {
                        var printItemCmd = item as PrintElementCommand;
                        var cmd = printItemCmd.Content.ToString().ToUpper();
                        if (cmd.Equals(EnumPrintCommand.AddSeparatorLine.ToString().ToUpper()))
                        {
                            var layoutHeight = printSeparator(graphics, printableArea, startX, startY + Offset);
                            Offset += layoutHeight;
                        }
                        if (cmd.Equals(EnumPrintCommand.AddEmptyLine.ToString().ToUpper()))
                        {
                            var layoutHeight = printSeparator(graphics, printableArea, startX, startY + Offset, "  ");
                            Offset += layoutHeight;
                        }

                        Offset += this.LineHeight;
                    }

                    //处理商品表格
                    if (EnumContentType.GoodsTable == contentType)
                    {
                        var printItem = item as PrintElementGoodsDataTable;
                        if (printItem.Content != null)
                        {
                            var tempOffset = printGoodsDataTable(item, graphics, printableArea, (int)startX, (int)(startY + Offset), 2);
                            Offset += (tempOffset + this.LineHeight);
                        }
                    }
                }
                var fontHeight = fontContent.GetHeight(graphics);
                Offset += fontHeight * SpaceLine;
                Offset += (LineHeight * SpaceLine) - LineHeight;
            }
            return Offset;
        }


        /// <summary>
        /// 打印文本块
        /// </summary>
        /// <param name="item"></param>
        /// <param name="graphics"></param>
        /// <returns></returns>
        private float printStringBlock(PrintElement item, Graphics graphics)
        {
            var printItem = item as PrintElementStringBlock;
            var printContent = printItem.Content;
            if (string.IsNullOrEmpty(printContent))
            {
                return 0f;
            }
            var printContentFont = printItem.ContentFont;
            var layoutSize = graphics.MeasureString(printContent, printContentFont, (int)printItem.Width);

            RectangleF layout = new RectangleF(new PointF(printItem.X, printItem.Y), new SizeF(printItem.Width, layoutSize.Height));//矩形坐标
            graphics.DrawString(printContent, printItem.ContentFont, brush, layout, convert2StringFormat(printItem.TextAlign));

            return layoutSize.Height;
        }

        /// <summary>
        /// 打印绝对定位图片块
        /// </summary>
        /// <param name="item"></param>
        /// <param name="graphics"></param>
        /// <returns></returns>
        private float printImageBlock(PrintElement item, Graphics graphics)
        {
            var printItem = item as PrintElementImageBlock;
            if (printItem.Content == null)
            {
                return 0f;
            }
            var printContentFont = printItem.ContentFont;
            var destReact = new System.Drawing.Rectangle((int)printItem.X, (int)printItem.Y, printItem.Width, printItem.Height);
            graphics.DrawImage(printItem.Content,
                        destReact,
                         new System.Drawing.Rectangle(0, 0, printItem.Content.Width, printItem.Content.Height),
                         GraphicsUnit.Pixel);

            return 0;
        }

        /// <summary>
        /// 打印文本,根据打印区域会换行
        /// </summary>
        /// <param name="item">打印对象</param>
        /// <param name="graphics">画布</param>
        /// <param name="printArea">打印区域</param>
        /// <param name="x">打印起始X坐标</param>
        /// <param name="y">打印起始Y坐标</param>
        /// <returns></returns>
        private float printString(PrintElement item, Graphics graphics, RectangleF printArea, float x, float y)
        {
            var printItem = item as PrintElementString;
            var printContent = printItem.Content;
            if (string.IsNullOrEmpty(printContent))
            {
                return 0f;
            }
            var contentList = printContent.Split('↵').ToList();
            var textAlign = convert2StringFormat(item.TextAlign);
            return this.printStringList(graphics, contentList, (int)x, (int)y, printItem.ContentFont, (int)printArea.Width, textAlign);
        }

        /// <summary>
        /// 打印图片
        /// </summary>
        /// <param name="item">打印对象</param>
        /// <param name="graphics">画布</param>
        /// <param name="printArea">打印区域</param>
        /// <param name="x">打印开始X坐标</param>
        /// <param name="y">打印开始Y坐标</param>
        /// <returns></returns>
        private float printImage(PrintElement item, Graphics graphics, RectangleF printArea, int x, int y)
        {
            float blockHeight = 0f;
            var printItem = item as PrintElementImage;
            if (printItem.Content == null)
            {
                return blockHeight;
            }
            var img = printItem.Content;
            if (img.Width < printArea.Width)
            {
                var marginCount = printArea.Width - img.Width;//图片两侧空白的差
                graphics.DrawImage(img, x + (marginCount / 2), y);//图片居中
                blockHeight = img.Height;
            }
            else
            {


                var bili = printArea.Width / (float)img.Width;
                var imgWidth = (int)Math.Ceiling(img.Width * bili);
                var imgHeight = (int)Math.Ceiling(img.Height * bili);
                var destReact = new System.Drawing.Rectangle(x, y, imgWidth, imgHeight);
                graphics.DrawImage(img,
                         destReact,
                          new System.Drawing.Rectangle(0, 0, img.Width, img.Height),
                          GraphicsUnit.Pixel);
                blockHeight = destReact.Height;
            }
            //img.Dispose();

            return blockHeight;
        }

        /// <summary>
        /// 打印表格
        /// </summary>
        /// <param name="item">打印对象</param>
        /// <param name="graphics">画布</param>
        /// <param name="printArea">打印区域</param>
        /// <param name="x">打印开始X坐标</param>
        /// <param name="y">打印开始Y坐标</param>
        /// <param name="leadingHeight"></param>
        /// <returns></returns>
        private float printDataTable(PrintElement printItem, Graphics graphics, RectangleF printArea, int x, int y, float leadingHeight = 5)
        {
            var item = printItem as PrintElementTable;

            var offset = 0f;
            var yOffset = 0f;
            var currentFont = item.ContentFont ?? fontContent;
            var table = item.Content as DataTable;
            if (table == null)
            {
                return yOffset;
            }
            var columnWidth = (int)Math.Floor(printArea.Width / table.Columns.Count);
            var currentFormat = convert2StringFormat(item.TextAlign);
            var columnList = table.Columns.Cast<DataColumn>().Select(dc => dc.ColumnName).ToList();

            if (item.PrintHead)
            {
                var headFont = item.HeadFont ?? currentFont;
                //打印表头
                yOffset = printStringList(graphics, columnList, x, y, headFont, columnWidth, formatCenter, 0);
                offset += (yOffset + leadingHeight);

                //打印分割线
                yOffset = printSeparator(graphics, printArea, x, y + (int)offset);
                offset += (yOffset + leadingHeight);
            }
            //逐行打印内容
            for (int i = 0; i < table.Rows.Count; i++)
            {
                var row = table.Rows[i];
                List<string> rowContent = columnList.Select(j => row[j].ToString()).ToList();
                yOffset = printStringList(graphics, rowContent, x, y + (int)offset, currentFont, columnWidth, currentFormat);
                offset += (yOffset + leadingHeight);
            }

            return offset;
        }

        /// <summary>
        /// 打印商品表格
        /// </summary>
        /// <param name="printItem"></param>
        /// <param name="graphics"></param>
        /// <param name="printArea"></param>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <param name="leadingHeight"></param>
        /// <returns></returns>
        private float printGoodsDataTable(PrintElement printItem, Graphics graphics, RectangleF printArea, int x, int y, float leadingHeight = 5)
        {
            var item = printItem as PrintElementGoodsDataTable;
            var offset = 0f;
            var yOffset = 0f;
            var table = item.Content;
            if (table == null)
            {
                return yOffset;
            }
            var dataStruct = item.DataStruct;
            var headFont = item.HeadFont ?? FontTitle;
            var bodyFont = item.BodyFont ?? FontContent;
            var headTextAlign = convert2StringFormat(item.HeadTextAlign);

            var columnList = dataStruct.AsEnumerable().Where(i =>
                       !Convert.ToBoolean(i["col_singleLine"]) && Convert.ToBoolean(i["col_isPrint"]))
                       .Select(i => i["col_head"].ToString()).ToList();
            var columnWidth = (int)Math.Floor(printArea.Width / columnList.Count());//只计算不独自一行的列数量

            if (item.PrintHead)
            {
                //打印表头

                var headOffSet = 0;
                foreach (DataRow configRow in dataStruct.Rows)
                {
                    var isPrint = Convert.ToBoolean(configRow["col_isPrint"]);
                    var isSingleLine = Convert.ToBoolean(configRow["col_singleLine"]);
                    if (!isPrint || isSingleLine)
                    {
                        continue;
                    }
                    var colWidth = configRow.Table.Columns.Contains("col_width") ? (int)(printArea.Width * Convert.ToInt32(configRow["col_width"]) / 100) : columnWidth;
                    var colAlignStr = configRow["col_align"].ToString();
                    var contentAlign = (EnumTextAlign)Enum.Parse(typeof(EnumTextAlign), String.IsNullOrEmpty(colAlignStr) ? ((int)EnumTextAlign.Left).ToString() : colAlignStr);
                    var textAlign = convert2StringFormat(contentAlign);
                    var currentYOffset = printString(graphics, configRow["col_head"].ToString(), x + headOffSet, y + (int)offset, headFont, colWidth, textAlign);
                    headOffSet += colWidth;
                    if (yOffset < currentYOffset)
                    {
                        yOffset = currentYOffset;
                    }
                }
                offset += yOffset;


                if (item.PrintSeparatorLine)
                {
                    //打印分割线
                    yOffset = printSeparator(graphics, printArea, x, y + (int)offset);
                    offset += (yOffset + leadingHeight);
                }
            }
            var dataSourceColumn = table.Columns;
            //逐行打印内容
            for (int i = 0; i < table.Rows.Count; i++)
            {
                var row = table.Rows[i];
                var xOffset = 0;
                var tempYOffset = 0f;
                int rowIndex = 0;
                foreach (DataRow configRow in dataStruct.Rows)
                {
                    var colAlignStr = configRow["col_align"].ToString();
                    var contentAlign = (EnumTextAlign)Enum.Parse(typeof(EnumTextAlign), string.IsNullOrEmpty(colAlignStr) ? ((int)EnumTextAlign.Left).ToString() : colAlignStr);
                    var textAlign = convert2StringFormat(contentAlign);
                    var isPrint = Convert.ToBoolean(configRow["col_isPrint"]);
                    if (!isPrint)
                    {
                        rowIndex++;
                        continue;
                    }
                    var isSingleLine = Convert.ToBoolean(configRow["col_singleLine"]);
                    var columnDataMember = configRow["col_dataMember"].ToString();
                    if (!dataSourceColumn.Contains(columnDataMember))
                    {
                        continue;
                    }

                    var columnValue = row[columnDataMember].ToString();

                    if (isSingleLine)
                    {
                        yOffset = printString(graphics, columnValue, x, y + (int)offset, bodyFont, (int)printArea.Width, textAlign);
                        offset += yOffset;
                        xOffset = 0;
                        tempYOffset = 0f;
                    }
                    else
                    {
                        var currentColumnWidth = (int)(printArea.Width * Convert.ToInt32(configRow["col_width"]) / 100);
                        if (dataStruct.Rows.Count == 1)
                        {
                            currentColumnWidth = columnWidth;
                        }
                        var colWidth = configRow.Table.Columns.Contains("col_width") ? currentColumnWidth : columnWidth;
                        var currentYOffset = printString(graphics, columnValue, x + xOffset, y + (int)offset, bodyFont, colWidth, textAlign);
                        if (tempYOffset < currentYOffset)
                        {
                            tempYOffset = currentYOffset;
                        }
                        if (rowIndex < dataStruct.Rows.Count - 1)
                        {
                            var nextRowSingleLine = Convert.ToBoolean(dataStruct.Rows[rowIndex + 1]["col_singleLine"]);
                            if (nextRowSingleLine)
                            {
                                xOffset = 0;
                                offset += tempYOffset;
                                tempYOffset = 0f;
                            }
                            else
                            {
                                xOffset += colWidth;
                                if (xOffset / printArea.Width > 0.9)
                                {
                                    xOffset = 0;
                                    offset += tempYOffset;
                                    tempYOffset = 0f;
                                }
                            }
                        }
                        else
                        {
                            xOffset = 0;
                            offset += tempYOffset;
                            tempYOffset = 0f;
                        }

                    }
                    rowIndex++;
                }
            }

            return offset;
        }

        /// <summary>
        /// 打印多行文本
        /// </summary>
        /// <param name="graphics">画布</param>
        /// <param name="list">文本列表</param>
        /// <param name="x">打印开始X坐标</param>
        /// <param name="y">打印开始Y坐标</param>
        /// <param name="font">文本字体</param>
        /// <param name="colWidth">单个文本块宽度</param>
        /// <param name="strFormat">居左中右</param>
        /// <param name="leadingHeight">文本块高度间距</param>
        /// <returns></returns>
        private float printStringList(Graphics graphics, List<string> list, int x, int y, Font font, int colWidth, StringFormat strFormat, int leadingHeight = 5)
        {
            var tempXOffset = 0;
            var tempYOffset = 0f;
            foreach (var item in list)
            {
                SizeF sf = graphics.MeasureString(item, fontContent, colWidth);
                var layout = new RectangleF(new PointF(x + tempXOffset, y + tempYOffset), new Size(colWidth, (int)(sf.Height + leadingHeight)));
                graphics.DrawString(item, font, brush, layout, strFormat);
                if (tempYOffset < layout.Height)
                {
                    tempYOffset = layout.Height;
                }
                tempXOffset += (colWidth + 1);
            }

            return tempYOffset;
        }

        private float printStringList(Graphics graphics, List<string> list, int x, int y, Font font, int colWidth, StringFormat strFormat)
        {
            var tempXOffset = 0;
            var tempYOffset = 0f;
            foreach (var item in list)
            {
                SizeF sf = graphics.MeasureString(item, font, colWidth);
                var layout = new RectangleF(new PointF(x + tempXOffset, y + tempYOffset), new Size(colWidth, (int)(sf.Height)));
                graphics.DrawString(item, font, brush, layout, strFormat);
                tempYOffset += (layout.Height + 5);
            }

            return tempYOffset;
        }

        private float printSpaceLine(Graphics graphics, int count, int x, int y, Font font, int colWidth, string content = ".")
        {
            var tempXOffset = 0;
            var tempYOffset = 0f;
            for (int i = 0; i < count; i++)
            {
                SizeF sf = graphics.MeasureString(content, font, colWidth);
                var layout = new RectangleF(new PointF(x + tempXOffset, y + tempYOffset), new Size(colWidth, (int)(sf.Height)));
                graphics.DrawString(content, font, Brushes.Black, layout);
                tempYOffset += (layout.Height + 5);
            }

            return tempYOffset;
        }

        private float printString(Graphics graphics, string item, int x, int y, Font font, int colWidth, StringFormat strFormat, int leadingHeight = 5)
        {
            item = item.Replace("↵", "\n");
            var tempXOffset = 0;
            var tempYOffset = 0f;
            SizeF sf = graphics.MeasureString(item, font, colWidth);
            var layout = new RectangleF(new PointF(x + tempXOffset, y), new Size(colWidth, (int)(sf.Height)));
            graphics.DrawString(item, font, brush, layout, strFormat);
            if (tempYOffset < layout.Height)
            {
                tempYOffset = layout.Height;
            }

            return tempYOffset;
        }

        /// <summary>
        /// 画一条分割线
        /// </summary>
        /// <param name="graphics">画布</param>
        /// <param name="printArea">画布打印区域</param>
        /// <param name="x">打印开始X坐标</param>
        /// <param name="y">打印开始Y坐标</param>
        /// <returns></returns>
        private float printSeparator(Graphics graphics, RectangleF printArea, float x, float y, string separaator = "·  ")
        {

            Font font = new Font("Impact", 8, FontStyle.Bold);//内容字体
            SizeF size = new SizeF(printArea.Width, font.Height);
            var separator = getSeparator(graphics, font, size, separaator);
            RectangleF layout = new RectangleF(new PointF(x, y), size);//矩形坐标
            graphics.DrawString(separator, font, brush, layout, formatCenter);

            return layout.Height;
        }

        /// <summary>
        /// 返回分割线字符串
        /// </summary>
        /// <param name="graphic">画布</param>
        /// <param name="font">字体</param>
        /// <param name="size">文字块矩形size</param>
        /// <param name="separator">分割线文字</param>
        /// <returns></returns>
        private string getSeparator(Graphics graphic, Font font, SizeF size, string separator = "·  ")
        {
            var sizrF = graphic.MeasureString(separator, font, size, StringFormat.GenericTypographic);
            var elCount = Math.Ceiling(size.Width / sizrF.Width);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < elCount; i++)
            {
                sb.Append(separator);
            }
            return sb.ToString();
        }

        private StringFormat convert2StringFormat(EnumTextAlign textAlign)
        {
            if (textAlign == EnumTextAlign.Left)
            {
                return formatLeft;
            }
            else if (textAlign == EnumTextAlign.Center)
            {
                return formatCenter;
            }
            else if (textAlign == EnumTextAlign.Right)
            {
                return formatRight;
            }
            return null;

        }

        public override void Print(string printerName, IEnumerable<Tuple<int, byte[]>> printData, int sleepMS)
        {
            throw new NotImplementedException();
        }

        public override void NetPrint(string ip, IEnumerable<Tuple<int, byte[]>> printData, int sleepMS, int port = 9100)
        {
            throw new NotImplementedException();
        }


        #endregion
    }
}
