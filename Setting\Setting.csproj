﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{CFE1991D-D1F5-47C9-A5A5-FFFD4515803F}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>Setting</RootNamespace>
    <AssemblyName>Setting</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Form2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form2.Designer.cs">
      <DependentUpon>Form2.cs</DependentUpon>
    </Compile>
    <Compile Include="Win32Utility.cs" />
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form2.resx">
      <DependentUpon>Form2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <None Include="Program\install\out\ver.xml.init" />
    <None Include="Program\install\out\ver.xml.prod" />
    <None Include="Program\install\scripts\isxdl\chinese.ini" />
    <None Include="Program\install\scripts\isxdl\czech.ini" />
    <None Include="Program\install\scripts\isxdl\dutch.ini" />
    <None Include="Program\install\scripts\isxdl\english.ini" />
    <None Include="Program\install\scripts\isxdl\french.ini" />
    <None Include="Program\install\scripts\isxdl\french2.ini" />
    <None Include="Program\install\scripts\isxdl\french3.ini" />
    <None Include="Program\install\scripts\isxdl\german.ini" />
    <None Include="Program\install\scripts\isxdl\german2.ini" />
    <None Include="Program\install\scripts\isxdl\german3.ini" />
    <None Include="Program\install\scripts\isxdl\italian.ini" />
    <None Include="Program\install\scripts\isxdl\japanese.ini" />
    <None Include="Program\install\scripts\isxdl\korean.ini" />
    <None Include="Program\install\scripts\isxdl\norwegian.ini" />
    <None Include="Program\install\scripts\isxdl\polish.ini" />
    <None Include="Program\install\scripts\isxdl\portugues.ini" />
    <None Include="Program\install\scripts\isxdl\portuguese.ini" />
    <None Include="Program\install\scripts\isxdl\russian.ini" />
    <None Include="Program\install\scripts\isxdl\spanish.ini" />
    <None Include="Program\install\scripts\isxdl\swedish.ini" />
    <None Include="Program\install\scripts\products.pas" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Program\Formats\" />
    <Folder Include="Program\zgStart\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Program\install\out\ver.xml" />
    <Content Include="Program\install\POS.iss" />
    <Content Include="Program\install\POS_beta.iss" />
    <Content Include="Program\install\POS_new.iss" />
    <Content Include="Program\install\POS_new_product.iss" />
    <Content Include="Program\install\POS_product.iss" />
    <Content Include="Program\install\POS_test.iss" />
    <Content Include="Program\install\scripts\isxdl\isxdl.dll" />
    <Content Include="Program\install\scripts\isxdl\isxdl.iss" />
    <Content Include="Program\install\scripts\lang\chinese.iss" />
    <Content Include="Program\install\scripts\lang\dutch.iss" />
    <Content Include="Program\install\scripts\lang\english.iss" />
    <Content Include="Program\install\scripts\lang\french.iss" />
    <Content Include="Program\install\scripts\lang\german.iss" />
    <Content Include="Program\install\scripts\lang\italian.iss" />
    <Content Include="Program\install\scripts\lang\japanese.iss" />
    <Content Include="Program\install\scripts\lang\polish.iss" />
    <Content Include="Program\install\scripts\lang\russian.iss" />
    <Content Include="Program\install\scripts\products.iss" />
    <Content Include="Program\install\scripts\products\directxruntime.iss" />
    <Content Include="Program\install\scripts\products\dotnetfx45.iss" />
    <Content Include="Program\install\scripts\products\dotnetfx46.iss" />
    <Content Include="Program\install\scripts\products\dotnetfx47.iss" />
    <Content Include="Program\install\scripts\products\dotnetfxversion.iss" />
    <Content Include="Program\install\scripts\products\fileversion.iss" />
    <Content Include="Program\install\scripts\products\msi20.iss" />
    <Content Include="Program\install\scripts\products\msi31.iss" />
    <Content Include="Program\install\scripts\products\msi45.iss" />
    <Content Include="Program\install\scripts\products\msiproduct.iss" />
    <Content Include="Program\install\scripts\products\stringversion.iss" />
    <Content Include="Program\install\scripts\products\vcredist2017.iss" />
    <Content Include="Program\install\scripts\products\winversion.iss" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>