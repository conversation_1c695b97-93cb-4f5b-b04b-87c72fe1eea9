﻿namespace zgUtils.Model
{
    public class MqttOptions
    {
        private string _accessKey = "33OohKRynCO2yTe9gExn5xIwgqqgcEbfQmvOuYWnSj4=";
        private string _secretKey = "Qacjdk9BwQ31SNF9J/GGA8GOo07LIUH9Py4VtL0kQF0=";

        //实例 ID，购买后从控制台获取
        public string instanceId { get; set; } = "post-cn-7pp2hcglc22";
        //此处填写购买得到的 MQTT 接入点域名
        public string brokerUrl { get; set; } = "post-cn-7pp2hcglc22.mqtt.aliyuncs.com";
        //此处填写阿里云帐号 AccessKey
        public string accessKey { get { return zgUtils.Security.AESHelper.Decrypt(_accessKey); } set { _accessKey = value; } }
        //此处填写阿里云帐号 SecretKey
        public string secretKey { get { return zgUtils.Security.AESHelper.Decrypt(_secretKey); } set { _secretKey = value; } }
        //此处填写在 MQ 控制台创建的 Topic，作为 MQTT 的一级 Topic
        public string parentTopic { get; set; } = "topic-order";
        public string defaultTopic { get; set; }
        //GroupId 需要先在 MQ 控制台创建
        public string groupId { get; set; } = "GID_ZGZN";
        public byte qos { get; set; } = 1;
        public bool cleanSession { get; set; } = true;
        public string commonTopic { get;  set; }
    }
}