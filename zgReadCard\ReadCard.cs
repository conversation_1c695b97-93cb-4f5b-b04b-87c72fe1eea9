﻿using System;
using System.Text;
using System.Threading.Tasks;
using zgLogging;
using System.Threading;
using System.Runtime.InteropServices;

namespace zgReadCard
{
    public class ReadCard
    {
        ////定义的委托 
        public delegate void delValueChange(object sender, EventArgs e);
        //委托相关联的事件 
        public static event delValueChange OnValueChanged;
        private static int icdev;
        private static string cardInfo = "";
        private static bool launch = false;
        public static string CardInfo
        {
            get { return cardInfo; }
            set
            {
                if (value != cardInfo)
                {
                    WhenValueChange(value);
                }
                cardInfo = value;
            }
        }
        public static void WhenValueChange(String val)
        {
            if (OnValueChanged != null)
            {
                if (!val.Equals("")) rf_beep(icdev, 25);
                OnValueChanged(val, null);//做你的任务

            }
        }

        public static void init(Action action)
        {
            try
            {
                icdev = rf_usbinit();

                if (launch || icdev == 0) return;
                launch = true;
                action();

                TaskFactory sd = new TaskFactory();
                sd.StartNew(() => {
                    while (launch)
                    {

                        try
                        {
                            ReadCardInfo();
                        }
                        catch (Exception ex)
                        {
                            Log.WriterNormalLog(ex.Message);
                        }
                        Thread.Sleep(1000);
                    }
                });
            }
            catch (Exception ex)
            {
                Log.WriterNormalLog(ex.Message);
            }
        }
        #region 读会员卡       

        public static void CloseConnection()
        {
            launch = false;
            if (OnValueChanged != null)
            {
                Delegate[] delArray = OnValueChanged.GetInvocationList();
                for (int i = 0; i < delArray.Length; i++)
                {
                    OnValueChanged -= delArray[i] as delValueChange;
                }
                OnValueChanged = null;

            }
            if (icdev > 0) rf_exit(icdev);

        }

        public static void ReadCardInfo()
        {
            byte[] snr = new byte[5];
            int status = -1;
            if (icdev > 0) status = rf_card(icdev, 1, snr);
            if (status == 0)
            {
                byte[] snr1 = new byte[8];
                hex_a(snr, snr1, 4);
                rf_halt(icdev);
                //rf_beep(icdev, 25);
                CardInfo = Encoding.ASCII.GetString(snr1);
            }
            else
            {
                CardInfo = "";
            }
            if (status < 0)
            {
                CloseConnection();
            }

        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="readCard_OnValueChanged"></param>
        /// <param name="useCardReader">是否读卡：0-不读，1-读</param>
        public static void OpenClose(ReadCard.delValueChange readCard_OnValueChanged, string useCardReader)
        {
            if ("1".Equals(useCardReader))
            {
                ReadCard.init(delegate ()
                {
                    ReadCard.OnValueChanged += readCard_OnValueChanged;
                });
            }
            else
            {
                ReadCard.CloseConnection();
            }
        }
        #endregion
        [DllImport("mwrf32.dll", EntryPoint = "rf_usbinit", SetLastError = true,
               CharSet = CharSet.Auto, ExactSpelling = false,
               CallingConvention = CallingConvention.StdCall)]
        public static extern int rf_usbinit();

        [DllImport("mwrf32.dll", EntryPoint = "rf_exit", SetLastError = true,
               CharSet = CharSet.Auto, ExactSpelling = false,
               CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_exit(int icdev);

        [DllImport("mwrf32.dll", EntryPoint = "rf_beep", SetLastError = true,
               CharSet = CharSet.Auto, ExactSpelling = false,
               CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_beep(int icdev, int msec);

        [DllImport("mwrf32.dll", EntryPoint = "rf_get_status", SetLastError = true,
              CharSet = CharSet.Auto, ExactSpelling = false,
              CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_get_status(int icdev, [MarshalAs(UnmanagedType.LPArray)] byte[] state);

        [DllImport("mwrf32.dll", EntryPoint = "rf_load_key", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_load_key(int icdev, int mode, int secnr, [MarshalAs(UnmanagedType.LPArray)] byte[] keybuff);

        [DllImport("mwrf32.dll", EntryPoint = "rf_load_key_hex", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_load_key_hex(int icdev, int mode, int secnr, [MarshalAs(UnmanagedType.LPArray)] byte[] keybuff);


        [DllImport("mwrf32.dll", EntryPoint = "a_hex", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        public static extern short a_hex([MarshalAs(UnmanagedType.LPArray)] byte[] asc, [MarshalAs(UnmanagedType.LPArray)] byte[] hex, int len);

        [DllImport("mwrf32.dll", EntryPoint = "hex_a", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        public static extern short hex_a([MarshalAs(UnmanagedType.LPArray)] byte[] hex, [MarshalAs(UnmanagedType.LPArray)] byte[] asc, int len);

        [DllImport("mwrf32.dll", EntryPoint = "rf_reset", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_reset(int icdev, int msec);

        [DllImport("mwrf32.dll", EntryPoint = "rf_request", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_request(int icdev, int mode, out ushort tagtype);


        [DllImport("mwrf32.dll", EntryPoint = "rf_anticoll", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_anticoll(int icdev, int bcnt, out uint snr);

        [DllImport("mwrf32.dll", EntryPoint = "rf_select", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_select(int icdev, uint snr, out byte size);

        [DllImport("mwrf32.dll", EntryPoint = "rf_card", SetLastError = true,
            CharSet = CharSet.Auto, ExactSpelling = false,
            CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_card(int icdev, int mode, [MarshalAs(UnmanagedType.LPArray)] byte[] snr);

        [DllImport("mwrf32.dll", EntryPoint = "rf_authentication", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_authentication(int icdev, int mode, int secnr);

        [DllImport("mwrf32.dll", EntryPoint = "rf_authentication_2", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_authentication_2(int icdev, int mode, int keynr, int blocknr);

        [DllImport("mwrf32.dll", EntryPoint = "rf_read", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_read(int icdev, int blocknr, [MarshalAs(UnmanagedType.LPArray)] byte[] databuff);

        [DllImport("mwrf32.dll", EntryPoint = "rf_read_hex", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_read_hex(int icdev, int blocknr, [MarshalAs(UnmanagedType.LPArray)] byte[] databuff);

        [DllImport("mwrf32.dll", EntryPoint = "rf_write_hex", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_write_hex(int icdev, int blocknr, [MarshalAs(UnmanagedType.LPArray)] byte[] databuff);

        [DllImport("mwrf32.dll", EntryPoint = "rf_write", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_write(int icdev, int blocknr, [MarshalAs(UnmanagedType.LPArray)] byte[] databuff);

        [DllImport("mwrf32.dll", EntryPoint = "rf_halt", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_halt(int icdev);

        [DllImport("mwrf32.dll", EntryPoint = "rf_changeb3", SetLastError = true,
            CharSet = CharSet.Auto, ExactSpelling = false,
            CallingConvention = CallingConvention.StdCall)]
        public static extern short rf_changeb3(int icdev, int sector, [MarshalAs(UnmanagedType.LPArray)] byte[] keya, int B0, int B1,
              int B2, int B3, int Bk, [MarshalAs(UnmanagedType.LPArray)] byte[] keyb);
    }
}
