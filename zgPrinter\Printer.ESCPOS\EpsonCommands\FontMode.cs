﻿using zgPrinter.Model;
using zgPrinter.Printer.ESCPOS.Interface;

namespace zgPrinter.Printer.ESCPOS.EpsonCommands
{
    public class FontMode : IFontMode
    {

        public byte[] Bold(string value)
        {
            return Bold(EnumPrinterModeState.On)
                .AddBytes(value)
                .AddBytes(Bold(EnumPrinterModeState.Off))
                .AddLF();
        }

        public byte[] Bold(EnumPrinterModeState state)
        {
            return state == EnumPrinterModeState.On
                ? new byte[] { 27, 'E'.ToByte(), 1 }
                : new byte[] { 27, 'E'.ToByte(), 0 };
        }

        public byte[] Underline(string value)
        {
            return Underline(EnumPrinterModeState.On)
                .AddBytes(value)
                .AddBytes(Underline(EnumPrinterModeState.Off))
                .AddLF();
        }

        public byte[] Underline(EnumPrinterModeState state)
        {
            return state == EnumPrinterModeState.On
                ? new byte[] { 27, '-'.ToByte(), 1 }
                : new byte[] { 27, '-'.ToByte(), 0 };
        }

        public byte[] Expanded(string value)
        {
            return Expanded(EnumPrinterModeState.On)
                .AddBytes(value)
                .AddBytes(Expanded(EnumPrinterModeState.Off))
                .AddLF();
        }

        public byte[] Expanded(EnumPrinterModeState state)
        {
            return state == EnumPrinterModeState.On
                ? new byte[] { 29, '!'.ToByte(), 16 }
                : new byte[] { 29, '!'.ToByte(), 0 };
        }

        public byte[] Condensed(string value)
        {
            return Condensed(EnumPrinterModeState.On)
                .AddBytes(value)
                .AddBytes(Condensed(EnumPrinterModeState.Off))
                .AddLF();
        }

        public byte[] Condensed(EnumPrinterModeState state)
        {
            return state == EnumPrinterModeState.On
                ? new byte[] { 27, '!'.ToByte(), 1 }
                : new byte[] { 27, '!'.ToByte(), 0 };
        }
        public byte[] Font(string value, EnumFonts state)
        {
            return Font(state)
           .AddBytes(value)
           .AddBytes(Font(EnumFonts.FontA))
           .AddLF();
        }

        public byte[] Font(EnumFonts state)
        {
            byte fnt = 0;
            switch (state)
            {
                case EnumFonts.FontA:
                    {
                        fnt = 0;
                        break;
                    }

                case EnumFonts.FontB:
                    {
                        fnt = 1;
                        break;
                    }

                case EnumFonts.FontC:
                    {
                        fnt =2;
                        break;
                    }

                case EnumFonts.FontD:
                    {
                        fnt = 3;
                        break;
                    }

                case EnumFonts.FontE:
                    {
                        fnt = 4;
                        break;
                    }

                case EnumFonts.SpecialFontA:
                    {
                        fnt = 5;
                        break;
                    }

                case EnumFonts.SpecialFontB:
                    {
                        fnt = 6;
                        break;
                    }
            }
            return new byte[] { 27, 'M'.ToByte(), fnt };
        }
    }
}

