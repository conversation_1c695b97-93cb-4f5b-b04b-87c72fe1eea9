﻿using System;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading;
using zgSerialPort.Common;

namespace zgSerialPort.ACS
{
    [ACSAttribute(ModelName = "友声通讯秤5", IsRelease = false, Index = 4)]
    public class YouSheng5ACS : BaseACS
    {

        /// <summary>
        /// 去除协议头后报文内容长度
        /// </summary>
        private readonly int contentLength = 7;

        /// <summary>
        /// 协议头
        /// </summary>
        private readonly byte[] protocolHeader = new byte[] { 0x3d };

        /// <summary>
        /// 默认编码
        /// </summary>
        private Encoding encoding = System.Text.Encoding.UTF8;

        public override void CommDataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            var serialPort = (System.IO.Ports.SerialPort)sender;
            if (serialPort.IsOpen)
            {
                try
                {
                    Thread.Sleep(50);

                    byte[] buffer = new byte[20];
                    serialPort.Read(buffer, 0, buffer.Length);
                    int startIndex = IndexOf(buffer, protocolHeader);
                    var result = buffer.Skip(startIndex + 1).Take(contentLength);

                    if (result.Count() == contentLength && startIndex > -1)
                    {
                        var lastByte = result.Last();
                        var ratio = 1;
                        if (lastByte != 0x20)
                        {
                            ratio = -1;
                        }
                        var content = new byte[6];
                        Array.Copy(result.ToArray(), 0, content, 0, 6);
                        Array.Reverse(content);
                        var contentStr = System.Text.Encoding.UTF8.GetString(content).Trim();
                        var showValue = Convert.ToDouble(contentStr) * ratio;
                        CallbackAction(showValue.ToString());
                    }
                }
                catch
                {

                }
            }
        }
    }
}
