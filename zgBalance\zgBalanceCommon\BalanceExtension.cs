﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgBalanceCommon
{
    public static class BalanceExtension
    {
        /// <summary>
        /// 文字转区域码
        /// </summary>
        /// <param name="chinese">中文</param>
        /// <returns></returns>
        public static string ConvertChinese2Coding(this string chinese) {
            var encoding = System.Text.Encoding.GetEncoding("GB2312");
            var result = string.Empty;
            var strArray = chinese.ToCharArray();
            foreach (var item in strArray)
            {
                byte[] b = encoding.GetBytes(item.ToString());
                short p1 = (short)b[0];
                if (b.Length == 2) {   
                    short p2 = (short)b[1];
                    result += (p1 - 160).ToString("00") + (p2 - 160).ToString("00");
                }
                else
                {
                    result += "03" + (p1 - 32).ToString("00");
                }
            }
            return result;
        }

        /// <summary>
        /// 区域码转文字
        /// </summary>
        /// <param name="coding"></param>
        /// <returns></returns>
        public static string ConvertCoding2Chinese(this string coding) {
            var result = string.Empty;
            if (coding.Length % 4 != 0) {
                throw new System.Exception("编码格式不正确");
            }
            var encoding = System.Text.Encoding.GetEncoding("GB2312");
            for (int i = 0; i < coding.Length; i += 4) {
                byte[] pArray = new byte[2];
                string front = coding.Substring(i, 2);
                string back = coding.Substring(i + 2, 2);
                if ("03".Equals(front))
                {
                    byte[] pArray2 = new byte[1];
                    pArray2[0] = (byte)(Convert.ToInt16(back) + 32);
                    result += encoding.GetString(pArray2);
                }
                else
                {
                    pArray[0] = (byte)(Convert.ToInt16(front) + 160);
                    pArray[1] = (byte)(Convert.ToInt16(back) + 160);
                    result += encoding.GetString(pArray);
                }
            }
            return result;
        }

        /// <summary>
        /// 转换价格为大华PLU格式
        /// </summary>
        /// <param name="price"></param>
        /// <returns></returns>
        public static string ConvertPrice4DHPLU(this double price) {
            var strPrice = price.ToString("n5");
            var result = strPrice.Substring(0, strPrice.LastIndexOf('.') + 3);
            result = result.Replace(".", "").PadLeft(6,'0');
            return result;
        }

        public static int SubInt32(this char[] charArray,int beginIndex,int count)
        {
            char[] distCharArray = new char[count];
            Array.Copy(charArray, beginIndex, distCharArray, 0, count);
            var result = Convert.ToInt32(new String(distCharArray));
            return result;
        }

        public static string SubString(this char[] charArray, int beginIndex, int count)
        {
            char[] distCharArray = new char[count];
            Array.Copy(charArray, beginIndex, distCharArray, 0, count);
            var result = new String(distCharArray);
            return result;
        }

        /// <summary>
        /// 字节数组转16进制字符串：空格分隔
        /// </summary>
        /// <param name="byteDatas"></param>
        /// <returns></returns>
        public static string ToHexStrFromByte(this byte[] byteDatas)
        {
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < byteDatas.Length; i++)
            {
                builder.Append(string.Format("{0:X2} ", byteDatas[i]));
            }
            return builder.ToString().Trim();
        }
    }
}
