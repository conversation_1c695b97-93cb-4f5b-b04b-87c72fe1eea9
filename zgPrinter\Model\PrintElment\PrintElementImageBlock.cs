﻿using System.Drawing;

namespace zgPrinter.Model
{
    public class PrintElementImageBlock: zgPrinter.Model.PrintElement
    {
        public PrintElementImageBlock(string imgPath) : this(new Bitmap(imgPath))
        {
        }

        public PrintElementImageBlock(Image image)
        {
            this.Content = image;
            this.ContentType = EnumContentType.ImageBlock;
        }

        public PrintElementImageBlock() {
            this.ContentType = EnumContentType.ImageBlock;
        }

        public float X { get; set; }

        public float Y { get; set; }

        public int Width { get; set; }

        public int Height { get; set; }

        public new Image Content { get; set; }
    }
}
