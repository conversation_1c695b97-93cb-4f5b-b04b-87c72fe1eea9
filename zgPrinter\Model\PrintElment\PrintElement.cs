﻿using Newtonsoft.Json.Linq;
using System;
using System.Drawing;

namespace zgPrinter.Model
{
    /// <summary>
    /// 小票打印元素基类
    /// </summary>
    public class PrintElement
    {
        /// <summary>
        /// 元素顺序
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 元素内容
        /// </summary>
        public virtual Object Content { get; set; }

        /// <summary>
        /// 元素内容类型
        /// </summary>
        public EnumContentType ContentType { get; set; }

        /// <summary>
        /// 内容字体，当类型为图片和指令时，设置此属性无效
        /// </summary>
        public Font ContentFont { get; set; }

        /// <summary>
        /// 文本浮动方向，当类型为图片和指令时，设置此属性无效
        /// </summary>
        public EnumTextAlign TextAlign { get; set; }

        /// <summary>
        /// 数据key
        /// </summary>
        public string DataMember { get; set; }

        /// <summary>
        /// 显示关联的datamember
        /// </summary>
        public string DisplayGroup { get; set; }

        /// <summary>
        /// 输出成JSON对象
        /// </summary>
        /// <returns></returns>
        public virtual JObject ToJObject() {
            return null;
        }
    }
}
