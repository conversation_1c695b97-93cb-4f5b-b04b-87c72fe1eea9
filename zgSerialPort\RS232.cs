﻿using zgSerialPort.ACS;
using zgSerialPort.Common;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgSerialPort
{
    public class RS232
    {
        #region Property

        /// <summary>
        /// 指令执行响应时触发该事件。
        /// </summary>
        public event ExecuteResponseHandle ExecuteResponse;

        /// <summary>
        /// 操作端口
        /// </summary>
        public System.IO.Ports.SerialPort SerialPort { get; }

        /// <summary>
        /// 执行脚本
        /// </summary>
        public String Script { set; get; }

        /// <summary>
        /// 上一次保存数据
        /// </summary>
        private String oldData = null;

        /// <summary>
        /// 当前电子秤
        /// </summary>
        private BaseACS electronicScale = null;

        #endregion

        public RS232(string electronicScaleName)
        {
            electronicScale = ACSFactory.GetACS(electronicScaleName);
            electronicScale.CallbackAction = (msg) => { ExecuteResponse?.Invoke(msg); };
        }

        /// <summary>
        /// 初始化控制台新实例，配置串口。
        /// </summary>
        /// <param name="mPort">串口名。</param>
        /// <param name="mBaudrate">波特率。</param>
        /// <param name="mDatabits">数据位。</param>
        /// <param name="mParity">奇偶校验。</param>
        /// <param name="mStopbits">停止位。</param>
        public RS232(ExecuteResponseHandle callback, string mPort, int mBaudrate, int mDatabits, Parity mParity, StopBits mStopbits, string electronicScaleName = "HongHaiACS") : this(electronicScaleName)
        {
            SerialPort = new System.IO.Ports.SerialPort
            {
                PortName = mPort,
                BaudRate = mBaudrate,
                DataBits = mDatabits,
                Parity = mParity,
                StopBits = mStopbits,
                RtsEnable = false,
                DtrEnable = false,
                ReceivedBytesThreshold = 1
            };

            SerialPort.DataReceived += electronicScale.CommDataReceived;
            SerialPort.ErrorReceived += onPortErrorReceived;
            if (!OpenPort())
            {
                throw new Exception("串口“" + mPort + "”尚未打开，请稍后再试！");
            }
            ExecuteResponse += callback;
        }


        /// <summary>
        /// 异常接收事件。
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void onPortErrorReceived(object sender, SerialErrorReceivedEventArgs e)
        {
            ExecuteResponse?.Invoke(Enum.GetName(typeof(SerialError), e.EventType));
        }

        /// <summary>
        /// 打开串口。
        /// </summary>
        private bool OpenPort()
        {
            if (SerialPort.IsOpen)
            {
                SerialPort.Close();
                SerialPort.Open();
            }
            else
            {
                SerialPort.Open(); // 打开串口
            }
            return SerialPort.IsOpen;
        }

        /// <summary>
        /// 关闭串口。
        /// </summary>
        public string ClosePort()
        {
            string comname = string.Empty;
            try
            {
                comname = SerialPort.PortName;
                if (SerialPort.IsOpen)
                {
                    SerialPort.Close();
                }
                SerialPort.Dispose();
                return comname;
            }
            catch (Exception)
            {
                return comname;
            }
        }

        /// <summary>
        /// 向串口写入数据。
        /// </summary>
        /// <param name="command">指令。</param>
        public void WritePort(string command)
        {
            if (!string.IsNullOrEmpty(command))
            {
                SerialPort.WriteLine(command);
            }
            else
            {
                //ExecuteResponse?.Invoke("请输入要执行的命令！");
            }
        }

        public bool Changed(string data)
        {
            if (this.oldData == null || !data.Equals(this.oldData))
            {
                this.oldData = data;
                return true;
            }
            else
            {
                return false;
            }

        }

    }
}
