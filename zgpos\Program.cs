﻿#undef ISPRODCT
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Threading;
using System.Windows.Forms;
using zgStart.Properties;
namespace zgzn
{
    static class Program
    {
        /// <summary>
        /// 允许运行此进程
        /// </summary>
        private static bool CreateNew = true;

        /// <summary>
        /// 创建互斥体，使程序仅可单实例运行；
        /// </summary>
        private static readonly Mutex UnityMutex = new Mutex(true, Application.ProductName, out CreateNew);


        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            //检查单实例进程运行
            if (!CreateNew)
            {
                Debug.Print("已经有实例运行，程序即将退出...");
                MessageBox.Show("已经有实例运行，程序即将退出...");
                return;
            }

            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);


            bool
#if (ISPRODCT)                
            ISPRODCT = true;
#else
            ISPRODCT = false;

#endif

            Dictionary<string, object> dics = new Dictionary<string, object>() {
                { "UpdateURL" ,ISPRODCT ? "https://www.zhangguizhinang.com/zgzn/" : "https://dev.zhangguizhinang.com/zgzn/" },
                { "DefaultConfigURL", ISPRODCT ? @"kwwsv=22}j}q1}kdqjjxl}klqdqj1frp2}j}q0vhwwlqjv" : @"kwwsv=22ghy1}kdqjjxl}klqdqj1frp2}j}q0vhwwlqjv" },
                { "Authorization",@"Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1aWQiOjAsImNsaWVudEFnZW50IjoiUEMiLCJzeXN0ZW1OYW1lIjoiemd6biIsInBhcnRpdGlvbklkIjowLCJjcmVhdGVkIjoxNjQyMzg1ODg3ODcyLCJleHBpcmF0aW9uIjpmYWxzZSwic3lzU2lkIjoxfQ.IRnVjtudmX8UOH7MQYnutMZHSW-CbdqiJBnq-97g8xKXYewVxqFmZrjjtQUvWjea258-LfpfkG0_e-gPD2EDEQ" },
                { "top",Resources.top},{ "ico",Resources.ico},{ "logo_title",Resources.logo_title },{ "pc_cloud_loading",Resources.pc_cloud_loading},
                { "ISPRODCT", ISPRODCT},{ "Assembly", System.Reflection.Assembly.GetExecutingAssembly()},
                { "UnityMutex",UnityMutex}
            };
            Invoke(dics);

        }

        private static void Invoke(Dictionary<string, object> dics)
        {
            try
            {
                ServicePointManager.CheckCertificateRevocationList = false;
                ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3
                    | SecurityProtocolType.Tls
                    | (SecurityProtocolType)0x300
                    | (SecurityProtocolType)0xC00;

                string commonDllPath = Environment.CurrentDirectory + @"\Programs\Libs\zgUpdater.dll";
                //if (!File.Exists(commonDllPath))
                //{
                //    string DownPath = dics["UpdateURL"].ToString() + @"Libs/zgUpdater.dll";
                //    WebClient client = new WebClient();
                //    client.DownloadFile(DownPath, commonDllPath);
                //}
                Assembly assembly = Assembly.Load(System.IO.File.ReadAllBytes(commonDllPath));
                if (assembly == null)
                    return;
                Type tp = assembly.GetType("zgUpdater.UpdaterHelperCenter");
                if (tp == null)
                    return;
                MethodInfo method = tp.GetMethod("Start");
                if (method == null)
                    return;

                method.Invoke(null, new Object[] { dics });
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("初始化Error：{0}", ex.Message));
            }

        }




    }

}


