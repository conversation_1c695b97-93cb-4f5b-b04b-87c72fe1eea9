﻿using System;
using CefSharp;
using zgLogging;
using zgUtils.Controls;

namespace zgpos.EventHandlers
{
    public static class ChromiumWebBrowserMessageHandler
    {
        public static void DoConsoleMessage(object sender, ConsoleMessageEventArgs e)
            => Log.WriterNormalLog($"{e.Source} [{e.Level}] {e.Line} => {e.Message}");

        public static void DoJavascriptMessage(object sender, JavascriptMessageReceivedEventArgs e)
        {
            if (e.Message.ToString().IndexOf("会员支付成功") >= 0 ||e.Message.ToString().IndexOf("saleService.settlement--args") >= 0  ) return;

            Log.WriterNormalLog($"{e.Browser} => Action => {e.Message}"); 
        
        }
    }
}
