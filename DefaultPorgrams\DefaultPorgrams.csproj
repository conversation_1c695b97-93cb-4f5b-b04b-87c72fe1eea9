﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{292361FA-1E23-4748-8C4C-6990EE3A8A2C}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DefaultPorgrams</RootNamespace>
    <AssemblyName>DefaultPorgrams</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\out\Programs\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\Release\Programs\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ProgramTemplate, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\out\Programs\Libs\ProgramTemplate.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DefaultProgramForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DefaultProgramForm.Designer.cs">
      <DependentUpon>DefaultProgramForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DefaultProgramResource.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DefaultProgramResource.resx</DependentUpon>
    </Compile>
    <Compile Include="ZoneIsolationClass.cs" />
    <Compile Include="WaitingClass.cs" />
    <Compile Include="UAVCameraClass.cs" />
    <Compile Include="ThreeDMapClass.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="DefaultProgramForm.resx">
      <DependentUpon>DefaultProgramForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DefaultProgramResource.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>DefaultProgramResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\HackSystemLogo.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\IronMan.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Close_Down.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Close_Enter.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Close_Normal.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\DefaultIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\IronManIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ThreeDMap.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ThreeDMapIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\UAVCamera.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\UAVCameraIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ActionIndication.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ActionIndicationIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AgentInfo.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AgentInfoIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AirDefence.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AirDefenceIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ARToolkit.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ARToolkitIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AttackData.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AttackDataIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\BallisticMissile.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\BallisticMissileIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Combat.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\CombatIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Decrypt.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\DecryptIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\DigitalRain.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\DigitalRainIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\DrivingSystem.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\DrivingSystemIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Face3DModel.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Face3DModelIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\GraphicOS.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\GraphicOSIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\LifeSupport.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\LifeSupportIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Missile.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\MissileIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\NetworkAttack.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\NetworkAttackIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\NOVA6.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\NOVA6Icon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Satellite.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\SatelliteIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ThinkingExport.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ThinkingExportIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Waiting.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\WaitingIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ZoneIsolation.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ZoneIsolationIcon.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>