<?xml version="1.0" encoding="utf-8"?>
<AnalysisConfig xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.sonarsource.com/msbuild/integration/2015/1">
  <SonarConfigDir>C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\conf</SonarConfigDir>
  <SonarOutputDir>C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\out</SonarOutputDir>
  <SonarBinDir>C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\bin</SonarBinDir>
  <SonarScannerWorkingDirectory>C:\Users\<USER>\Documents\POSVueChrome</SonarScannerWorkingDirectory>
  <HasBeginStepCommandLineCredentials>false</HasBeginStepCommandLineCredentials>
  <SonarQubeHostUrl>http://***********:9000</SonarQubeHostUrl>
  <SonarQubeVersion>7.6.0.21501</SonarQubeVersion>
  <SonarProjectKey>POSVueChrome</SonarProjectKey>
  <SonarProjectVersion>1.9</SonarProjectVersion>
  <SonarProjectName>POSVueChrome</SonarProjectName>
  <AdditionalConfig>
    <ConfigSetting Id="BuildUri" />
    <ConfigSetting Id="TfsUri" />
    <ConfigSetting Id="settings.file.path" Value="D:\MS\SonarQube.Analysis.xml" />
  </AdditionalConfig>
  <ServerSettings>
    <Property Name="sonaranalyzer-cs.nuget.packageVersion">7.10.0.7896</Property>
    <Property Name="sonar.cs.ignoreHeaderComments">true</Property>
    <Property Name="sonar.typescript.file.suffixes">.ts,.tsx</Property>
    <Property Name="sonar.javascript.jQueryObjectAliases">$, jQuery</Property>
    <Property Name="sonaranalyzer-vbnet.nuget.packageVersion">7.10.0.7896</Property>
    <Property Name="sonar.go.file.suffixes">.go</Property>
    <Property Name="email.fromName">SonarQube</Property>
    <Property Name="sonar.python.xunit.skipDetails">false</Property>
    <Property Name="sonar.dbcleaner.weeksBeforeKeepingOnlyAnalysesWithVersion">104</Property>
    <Property Name="sonar.go.exclusions">**/vendor/**</Property>
    <Property Name="sonar.forceAuthentication">false</Property>
    <Property Name="sonar.notifications.delay">60</Property>
    <Property Name="sonar.vbnet.roslyn.ignoreIssues">false</Property>
    <Property Name="sonar.flex.file.suffixes">as</Property>
    <Property Name="sonar.ruby.file.suffixes">.rb</Property>
    <Property Name="sonaranalyzer-cs.ruleNamespace">SonarAnalyzer.CSharp</Property>
    <Property Name="sonar.python.xunit.reportPath">xunit-reports/xunit-result-*.xml</Property>
    <Property Name="sonar.builtInQualityProfiles.disableNotificationOnUpdate">false</Property>
    <Property Name="sonar.javascript.globals">angular,goog,google,OpenLayers,d3,dojo,dojox,dijit,Backbone,moment,casper</Property>
    <Property Name="sonar.dbcleaner.hoursBeforeKeepingOnlyOneSnapshotByDay">24</Property>
    <Property Name="sonar.javascript.exclusions">**/node_modules/**,**/bower_components/**</Property>
    <Property Name="sonar.css.file.suffixes">.css,.less,.scss</Property>
    <Property Name="sonar.java.failOnException">false</Property>
    <Property Name="sonar.organizations.createPersonalOrg">false</Property>
    <Property Name="sonar.jacoco.reportPaths">target/jacoco.exec, target/jacoco-it.exec</Property>
    <Property Name="sonar.html.file.suffixes">.html,.xhtml,.cshtml,.vbhtml,.aspx,.ascx,.rhtml,.erb,.shtm,.shtml</Property>
    <Property Name="sonaranalyzer-vbnet.nuget.packageId">SonarAnalyzer.VisualBasic</Property>
    <Property Name="sonar.cpd.cross_project">false</Property>
    <Property Name="email.from">noreply@nowhere</Property>
    <Property Name="sonaranalyzer-vbnet.pluginVersion">7.10.0.7896</Property>
    <Property Name="sonar.vbnet.ignoreHeaderComments">true</Property>
    <Property Name="sonar.typescript.node">node</Property>
    <Property Name="sonar.dbcleaner.weeksBeforeKeepingOnlyOneSnapshotByWeek">4</Property>
    <Property Name="sonar.leak.period">previous_version</Property>
    <Property Name="sonaranalyzer-vbnet.analyzerId">SonarAnalyzer.VisualBasic</Property>
    <Property Name="email.prefix">[SONARQUBE]</Property>
    <Property Name="sonar.scala.file.suffixes">.scala</Property>
    <Property Name="sonar.cs.roslyn.ignoreIssues">false</Property>
    <Property Name="sonaranalyzer-cs.pluginKey">csharp</Property>
    <Property Name="sonaranalyzer-vbnet.ruleNamespace">SonarAnalyzer.VisualBasic</Property>
    <Property Name="sonar.javascript.ignoreHeaderComments">true</Property>
    <Property Name="sonar.dbcleaner.daysBeforeDeletingClosedIssues">30</Property>
    <Property Name="sonar.dbcleaner.weeksBeforeKeepingOnlyOneSnapshotByMonth">52</Property>
    <Property Name="sonar.lf.gravatarServerUrl">https://secure.gravatar.com/avatar/{EMAIL_MD5}.jpg?s={SIZE}&amp;d=identicon</Property>
    <Property Name="sonar.ruby.coverage.reportPaths">coverage/.resultset.json</Property>
    <Property Name="sonaranalyzer-cs.staticResourceName">SonarAnalyzer-7.10.0.7896.zip</Property>
    <Property Name="sonar.notifications.runningDelayBeforeReportingStatus">600</Property>
    <Property Name="sonar.jsp.file.suffixes">.jsp,.jspf,.jspx</Property>
    <Property Name="sonaranalyzer-cs.nuget.packageId">SonarAnalyzer.CSharp</Property>
    <Property Name="sonar.javascript.environments">amd, applescript, atomtest, browser, commonjs, couch, embertest, flow, greasemonkey, jasmine, jest, jquery, meteor, mocha, mongo, nashorn, node, phantomjs, prototypejs, protractor, qunit, rhino, serviceworker, shared-node-browser, shelljs, webextensions, worker, wsh, yui</Property>
    <Property Name="sonar.authenticator.downcase">false</Property>
    <Property Name="sonar.scm.disabled">false</Property>
    <Property Name="sonar.python.coverage.reportPath">coverage-reports/*coverage-*.xml</Property>
    <Property Name="sonar.typescript.exclusions">**/node_modules/**,**/bower_components/**</Property>
    <Property Name="sonar.vbnet.file.suffixes">.vb</Property>
    <Property Name="sonaranalyzer-cs.analyzerId">SonarAnalyzer.CSharp</Property>
    <Property Name="sonar.organizations.anyoneCanCreate">false</Property>
    <Property Name="sonar.technicalDebt.ratingGrid">0.05,0.1,0.2,0.5</Property>
    <Property Name="sonar.technicalDebt.developmentCost">30</Property>
    <Property Name="sonar.lf.enableGravatar">false</Property>
    <Property Name="sonar.preview.excludePlugins">devcockpit,pdfreport,governance,ldap,authaad,authgithub,authbitbucket,googleanalytics</Property>
    <Property Name="sonar.python.file.suffixes">py</Property>
    <Property Name="sonaranalyzer-cs.pluginVersion">7.10.0.7896</Property>
    <Property Name="sonar.cs.file.suffixes">.cs</Property>
    <Property Name="sonar.javascript.file.suffixes">.js,.jsx,.vue</Property>
    <Property Name="sonaranalyzer-vbnet.staticResourceName">SonarAnalyzer-7.10.0.7896.zip</Property>
    <Property Name="sonar.java.file.suffixes">.java,.jav</Property>
    <Property Name="sonar.kotlin.file.suffixes">.kt</Property>
    <Property Name="sonar.php.file.suffixes">php,php3,php4,php5,phtml,inc</Property>
    <Property Name="sonar.xml.file.suffixes">.xml,.xsd,.xsl</Property>
    <Property Name="sonar.dbcleaner.weeksBeforeDeletingAllSnapshots">260</Property>
    <Property Name="sonaranalyzer-vbnet.pluginKey">vbnet</Property>
    <Property Name="sonar.java.collectAnalysisErrors">false</Property>
    <Property Name="sonar.updatecenter.url">https://update.sonarsource.org/update-center.properties</Property>
    <Property Name="sonar.core.id">5C48A169-AXGlcd1c8Zur4WreynZO</Property>
    <Property Name="sonar.core.startTime">2020/4/23 18:36:35</Property>
  </ServerSettings>
  <LocalSettings />
  <AnalyzersSettings>
    <AnalyzerSettings>
      <Language>cs</Language>
      <RuleSetFilePath>C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\conf\SonarQubeRoslyn-cs.ruleset</RuleSetFilePath>
      <TestProjectRuleSetFilePath>C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\conf\SonarQubeRoslyn-cs-test.ruleset</TestProjectRuleSetFilePath>
      <AnalyzerPlugins>
        <AnalyzerPlugin Key="csharp" Version="7.10.0.7896" StaticResourceName="SonarAnalyzer-7.10.0.7896.zip">
          <AssemblyPaths>
            <Path>C:\Users\<USER>\AppData\Local\Temp\.sonarqube\resources\1\Google.Protobuf.dll</Path>
            <Path>C:\Users\<USER>\AppData\Local\Temp\.sonarqube\resources\1\SonarAnalyzer.CSharp.dll</Path>
            <Path>C:\Users\<USER>\AppData\Local\Temp\.sonarqube\resources\1\SonarAnalyzer.dll</Path>
          </AssemblyPaths>
        </AnalyzerPlugin>
        <AnalyzerPlugin Key="vbnet" Version="7.10.0.7896" StaticResourceName="SonarAnalyzer-7.10.0.7896.zip">
          <AssemblyPaths>
            <Path>C:\Users\<USER>\AppData\Local\Temp\.sonarqube\resources\2\Google.Protobuf.dll</Path>
            <Path>C:\Users\<USER>\AppData\Local\Temp\.sonarqube\resources\2\SonarAnalyzer.dll</Path>
            <Path>C:\Users\<USER>\AppData\Local\Temp\.sonarqube\resources\2\SonarAnalyzer.VisualBasic.dll</Path>
          </AssemblyPaths>
        </AnalyzerPlugin>
      </AnalyzerPlugins>
      <AdditionalFilePaths>
        <Path>C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\conf\cs\SonarLint.xml</Path>
      </AdditionalFilePaths>
    </AnalyzerSettings>
    <AnalyzerSettings>
      <Language>vbnet</Language>
      <RuleSetFilePath>C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\conf\SonarQubeRoslyn-vbnet.ruleset</RuleSetFilePath>
      <TestProjectRuleSetFilePath>C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\conf\SonarQubeRoslyn-vbnet-test.ruleset</TestProjectRuleSetFilePath>
      <AnalyzerPlugins>
        <AnalyzerPlugin Key="csharp" Version="7.10.0.7896" StaticResourceName="SonarAnalyzer-7.10.0.7896.zip">
          <AssemblyPaths>
            <Path>C:\Users\<USER>\AppData\Local\Temp\.sonarqube\resources\1\Google.Protobuf.dll</Path>
            <Path>C:\Users\<USER>\AppData\Local\Temp\.sonarqube\resources\1\SonarAnalyzer.CSharp.dll</Path>
            <Path>C:\Users\<USER>\AppData\Local\Temp\.sonarqube\resources\1\SonarAnalyzer.dll</Path>
          </AssemblyPaths>
        </AnalyzerPlugin>
        <AnalyzerPlugin Key="vbnet" Version="7.10.0.7896" StaticResourceName="SonarAnalyzer-7.10.0.7896.zip">
          <AssemblyPaths>
            <Path>C:\Users\<USER>\AppData\Local\Temp\.sonarqube\resources\2\Google.Protobuf.dll</Path>
            <Path>C:\Users\<USER>\AppData\Local\Temp\.sonarqube\resources\2\SonarAnalyzer.dll</Path>
            <Path>C:\Users\<USER>\AppData\Local\Temp\.sonarqube\resources\2\SonarAnalyzer.VisualBasic.dll</Path>
          </AssemblyPaths>
        </AnalyzerPlugin>
      </AnalyzerPlugins>
      <AdditionalFilePaths>
        <Path>C:\Users\<USER>\Documents\POSVueChrome\.sonarqube\conf\vbnet\SonarLint.xml</Path>
      </AdditionalFilePaths>
    </AnalyzerSettings>
  </AnalyzersSettings>
</AnalysisConfig>