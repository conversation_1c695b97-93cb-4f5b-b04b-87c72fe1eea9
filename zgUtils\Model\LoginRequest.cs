﻿//------------------------------------------------------------------------------
// <auto-generated>
//    此代码是根据模板生成的。
//
//    手动更改此文件可能会导致应用程序中发生异常行为。
//    如果重新生成代码，则将覆盖对此文件的手动更改。
// </auto-generated>
//------------------------------------------------------------------------------

namespace zgUtils.Model
{
    using Newtonsoft.Json;
    using System;
    using System.Collections.Generic;

    public class LoginRequest
    {
        public string username { get; set; }
        public int sysSid { get; set; }
        public string employeeNumber { get; set; }
        public string password { get; set; }
        public int? userremember { get; set; }
        public int type { get; set; }
        public bool employeeremember { get; set; } = false;
        public string version { get { return CommonApp.Version; } }
        public string version_db { get; set; }
        public int authorityVersion { get; set; } = 1;

        //public string subName { get { return CommonApp.SubSystemName; } }
        public ModuleUrl moduleUrl { get { return new ModuleUrl(); } }
        public ResponseLogin data { get; set; }
    }

    public class ModuleUrl
    {
        public string userModuleUrl { get { return CommonApp.Config.ServerUrl.userModuleUrl; } }
        public string settingModuleUrl { get { return CommonApp.Config.ServerUrl.settingModuleUrl; } }
        public string productModuleUrl { get { return CommonApp.Config.ServerUrl.productModuleUrl; } }
        public string posModuleUrl { get { return CommonApp.Config.ServerUrl.posModuleUrl; } }
        public string advertModuleUrl { get { return CommonApp.Config.ServerUrl.advertModuleUrl; } }
        public string vipModuleUrl { get { return CommonApp.Config.ServerUrl.vipModuleUrl; } }
        public string nginxModuleUrl { get { return CommonApp.Config.ServerUrl.nginxModuleUrl; } }

    }

    public class LoginLogRequest
    {
        public string sysUid { get { return CommonApp.sysUid; } }
        public string sysSid { get { return CommonApp.sysSid; } }
        public string deviceType { get { return "pc"; } }
        public string deviceId { get { return CommonApp.DeviceId; } }
        public string action { get; set; }
        public string info { get; set; }
        public LoginLogRequest() {
            
        }
        public LoginLogRequest(string _action)
        {

            action = _action;
        }
        public LoginLogRequest(string _action, string _info):this(_action)
        {
            info = _info;
        }
        public string ToJson(object targert)
        {
            return JsonConvert.SerializeObject(targert);
        }

        public string ToJson()
        {
            return JsonConvert.SerializeObject(this);
        }

    }
}
