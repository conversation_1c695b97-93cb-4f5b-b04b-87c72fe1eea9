﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgSyncData.Model.Catering
{
    [Serializable]
    public class SubOrder
    {
		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "createAt")]
		public string CreateAt { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "createBy")]
		public string CreateBy { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "customerId")]
		public int CustomerId { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "discount")]
		public string Discount { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "discountMoney")]
		public decimal DiscountMoney { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "fingerprint")]
		public string Fingerprint { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "id")]
		public int Id { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "info1")]
		public string Info1 { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "info2")]
		public string Info2 { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "isDel")]
		public int IsDel { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "orderFingerprint")]
		public string OrderFingerprint { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "placeOrderType")]
		public int PlaceOrderType { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "remark")]
		public string Remark { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "reviseAt")]
		public string ReviseAt { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "reviseBy")]
		public string ReviseBy { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "serialNo")]
		public string SerialNo { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "status")]
		public int Status { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "subOrderNo")]
		public string SubOrderNo { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "subOrderType")]
		public int SubOrderType { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "syncAt")]
		public string SyncAt { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[JsonProperty(propertyName: "totalMoney")]
		public decimal TotalMoney { get; set; }

		[JsonProperty(propertyName: "orderItems")]
		public List<OrderItem> OrderItems { get; set; }
    }
}
