﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace zgPrinter.Model
{
	public class SocketWrapper : IDisposable
	{
		// Token: 0x0600016C RID: 364 RVA: 0x0000DCD9 File Offset: 0x0000BED9
		internal SocketWrapper()
		{
			this.status = -2;
		}

		// Token: 0x0600016D RID: 365 RVA: 0x0000DCF8 File Offset: 0x0000BEF8
		private void DoEndConnect(IAsyncResult ar)
		{
			try
			{
				Socket socket = ar.AsyncState as Socket;
				bool flag = socket != null;
				if (flag)
				{
					socket.EndConnect(ar);
				}
			}
			catch (Exception exception)
			{
				//LoggerFactory.GetLogger().ErrorAsync("网络打印出错啦！", exception);
			}
		}

		// Token: 0x0600016E RID: 366 RVA: 0x0000DD4C File Offset: 0x0000BF4C
		private int Use()
		{
			int num = Interlocked.CompareExchange(ref this.status, 1, 0);
			bool flag = !this.socket.Connected;
			int result;
			if (flag)
			{
				result = -1;
			}
			else
			{
				switch (num)
				{
					case 0:
						return 1;
					case 2:
						return -1;
					case 3:
						return 2;
				}
				result = 0;
			}
			return result;
		}

		// Token: 0x0600016F RID: 367 RVA: 0x0000DDAC File Offset: 0x0000BFAC
		internal int WaitForUsed()
		{
			int num = this.Use();
			bool flag = num == 0;
			if (flag)
			{
				int num2 = 0;
				object obj = this.sysncState;
				lock (obj)
				{
					do
					{
						Monitor.Wait(this.sysncState, 500);
						num = this.Use();
						bool flag3 = num != 0;
						if (flag3)
						{
							break;
						}
					}
					while (num2++ < 4);
				}
			}
			bool flag4 = num == 1;
			if (flag4)
			{
				this.lastTicks = Environment.TickCount;
			}
			return num;
		}

		// Token: 0x06000170 RID: 368 RVA: 0x0000DE50 File Offset: 0x0000C050
		internal void Notify()
		{
			object obj = this.sysncState;
			lock (obj)
			{
				Monitor.PulseAll(this.sysncState);
			}
		}

		// Token: 0x06000171 RID: 369 RVA: 0x0000DE9C File Offset: 0x0000C09C
		internal void ReleaseSocket(bool systemClosed)
		{
			Interlocked.Exchange(ref this.status, systemClosed ? 3 : 2);
			bool flag = this.socket != null;
			if (flag)
			{
				try
				{
					this.socket.Close();
				}
				catch (Exception exception)
				{
					//LoggerFactory.GetLogger().ErrorAsync("网络打印出错：连线释放出现异常。", exception);
				}
			}
		}

		// Token: 0x06000172 RID: 370 RVA: 0x0000DF04 File Offset: 0x0000C104
		internal bool ConnectForUsed(string address, int port, int millisecondTimeout)
		{
			bool flag = true;
			bool flag2 = Interlocked.CompareExchange(ref this.status, -1, -2) == -2;
			if (flag2)
			{
				try
				{
					this.lastTicks = Environment.TickCount;
					this.socket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
					IAsyncResult asyncResult = this.socket.BeginConnect(address, port, new AsyncCallback(this.DoEndConnect), this.socket);
					bool flag3 = asyncResult.AsyncWaitHandle.WaitOne(millisecondTimeout);
					if (flag3)
					{
						flag = this.socket.Connected;
					}
					else
					{
						flag = false;
						//LoggerFactory.GetLogger().ErrorAsync(string.Format("网络打印出错：连接“{0}:{1}”超时。", address, port));
					}
				}
				catch (Exception exception)
				{
					//LoggerFactory.GetLogger().ErrorAsync("网络打印出错啦！", exception);
				}
				bool flag4 = !flag || Interlocked.CompareExchange(ref this.status, 1, -1) != -1;
				if (flag4)
				{
					this.ReleaseSocket(false);
				}
				else
				{
					this.lastTicks = Environment.TickCount;
				}
			}
			return flag;
		}

		// Token: 0x06000173 RID: 371 RVA: 0x0000E010 File Offset: 0x0000C210
		internal bool CheckFree(int curTicks, int timeoutTicks)
		{
			bool flag = curTicks - this.lastTicks > timeoutTicks || (this.socket != null && !this.socket.Connected);
			bool result;
			if (flag)
			{
				int num = Interlocked.CompareExchange(ref this.status, 2, 0);
				result = (num == 0 || num == 2);
			}
			else
			{
				result = false;
			}
			return result;
		}

		// Token: 0x1700003B RID: 59
		// (get) Token: 0x06000174 RID: 372 RVA: 0x0000E06C File Offset: 0x0000C26C
		public bool IsConnected
		{
			get
			{
				return (this.status & -2) == 0 && this.socket.Connected;
			}
		}

		// Token: 0x1700003C RID: 60
		// (get) Token: 0x06000175 RID: 373 RVA: 0x0000E098 File Offset: 0x0000C298
		public Socket Socket
		{
			get
			{
				return this.socket;
			}
		}

		// Token: 0x06000176 RID: 374 RVA: 0x0000E0B0 File Offset: 0x0000C2B0
		public void Dispose()
		{
			bool flag = Interlocked.CompareExchange(ref this.status, 0, 1) == 1;
			if (flag)
			{
				this.lastTicks = Environment.TickCount;
			}
			this.Notify();
		}

		// Token: 0x0400012D RID: 301
		private Socket socket;

		// Token: 0x0400012E RID: 302
		private int status;

		// Token: 0x0400012F RID: 303
		private int lastTicks;

		// Token: 0x04000130 RID: 304
		private object sysncState = new object();
	}
}
