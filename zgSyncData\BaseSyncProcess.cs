﻿using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using zgSyncData.Model;
using zgUtils.Controls;

namespace zgSyncData
{
    public abstract class BaseSyncProcesser
    {
        public string Authorization { get; set; }

        protected RestClient restClient;

        /// <summary>
        /// 配置名称
        /// </summary>
        public string ProcessName { get; set; }

        /// <summary>
        /// 数据下载地址
        /// </summary>
        public string DownLoadUrl { get; set; }

        /// <summary>
        /// 每次数据下载条数
        /// </summary>
        public int DownloadRecordCount { get; set; } = 500;

        /// <summary>
        /// 数据上传地址
        /// </summary>
        public string UpLoadUrl { get; set; }

        /// <summary>
        /// 每次数据上传条数
        /// </summary>
        public int UploadRecordCount { get; set; } = 5000;

        public string DeviceCode { get; set; }


        public string SysUid { get; set; }

        public string SysSid { get; set; }

        public abstract void DownLoadAsync(int dataCount, string startSyncAt, string endSyncAt);

        public void SetSynchronous(string dbName = "main",int value =2)
        {
            var sql = $"PRAGMA {dbName}.synchronous = {value}";
            SQLiteHelper.ExecuteNonQuery(sql);
        }
    }
}
