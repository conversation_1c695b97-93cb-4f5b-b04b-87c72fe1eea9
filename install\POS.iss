﻿#define MyAppPublisher 'TRECHINA'
#define MyAppSetupName() MyExeName+'.exe'
#define MyAppVersion() GetFileVersion("..\out\"+MyExeName+".exe")
#define MyAppProductVersion GetStringFileInfo("..\out\"+MyExeName+".exe", 'ProductVersion')

[Setup]
PrivilegesRequired=admin
OutputDir={#MyOutputDir}
SourceDir=.
AppName={#ProductName}
AppVerName={#ProductName} {#MyAppProductVersion}
AppVersion={#MyAppVersion}
VersionInfoVersion={#MyAppVersion}
VersionInfoProductTextVersion={#MyAppProductVersion}
OutputBaseFilename={#MyExeName}-{#MyAppProductVersion}
AppPublisher={#MyAppPublisher}
AppCopyright={#MyAppPublisher}
DefaultDirName={#MyDirName}
DefaultGroupName={#ProductName}
UninstallDisplayIcon={app}\{#MyAppSetupName}
Compression=lzma2
SolidCompression=yes
;配置签名
;SignTool=mysigntool
;SignedUninstaller=yes
; downloading and installing dependencies will only work if the memo/ready page is enabled (default and current behaviour)
;DisableReadyPage=no
;DisableReadyMemo=no
;ShowLanguageDialog=yes
; supported languages
;#include "scripts\lang\english.iss"
#include "scripts\lang\chinese.iss"
;#include "scripts\lang\japanese.iss"

[Files]
;#Source: "..\out\{#MyAppSetupName}"; DestDir: "{app}"; Flags: recursesubdirs signonce
Source: "..\out\Programs\Libs\*"; Excludes: "*.json"; DestDir: "{app}\Programs\Libs"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "..\out\Programs\cefLib\*"; DestDir: "{app}\Programs\cefLib"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "..\out\Programs\*.dll"; DestDir: "{app}\Programs\"; Flags: ignoreversion skipifsourcedoesntexist
Source: "..\out\{#MyAppSetupName}"; DestDir: "{app}"; Flags: ignoreversion 
Source: "..\out\{#MyAppSetupName}.config"; DestDir: "{app}"; Flags: ignoreversion 
[InstallDelete]
Name: {app}\*.dat; Type: filesandordirs
Name: {app}\*.exe; Type: filesandordirs
Name: {app}\*.config; Type: filesandordirs
Name: {app}\Programs\*.dll; Type: filesandordirs
Name: {app}\Programs\*.config; Type: filesandordirs
Name: {app}\Programs\cefLib; Type: filesandordirs
Name: {app}\Programs\Libs; Type: filesandordirs

[Icons]
Name: "{commonprograms}\{#ProductName}\{#MyExeName}"; Filename: "{app}\{#MyAppSetupName}"
Name: "{commonprograms}\{#ProductName}\{cm:UninstallProgram,{#ProductName}}"; Filename: "{uninstallexe}"
Name: "{commondesktop}\{#ProductName}"; Filename: "{app}\{#MyAppSetupName}"

[Run]
Filename: "{app}\{#MyAppSetupName}"; Description: "{cm:LaunchProgram,{#MyAppSetupName}}"; Flags: nowait postinstall skipifsilent

[CustomMessages]
DependenciesDir=MyProgramDependencies

#include "scripts\products.iss"

#include "scripts\products\stringversion.iss"
#include "scripts\products\winversion.iss"
#include "scripts\products\msiproduct.iss"
#include "scripts\products\dotnetfxversion.iss"

#include "scripts\products\vcredist2017.iss"
#include "scripts\products\dotnetfx45.iss"


[UninstallDelete]
Name: {app}; Type: filesandordirs

[Code]
function GetDefaultInstallRoot(Param: String): String;
begin
	if ((GetWindowsVersion shr 16) >= $0600) then
        Result := ExpandConstant('{userappdata}')
    else  
        Result := ExpandConstant('{pf}');
end;

function InitializeSetup(): boolean;
begin
  vcredist2017('14');
  dotnetfx45(52);
  Result := true;
end;

const
  CP_UTF8 = 65001;

function WideCharToMultiByte(CodePage: UINT; dwFlags: DWORD;
  lpWideCharStr: string; cchWideChar: Integer; lpMultiByteStr: AnsiString;
  cchMultiByte: Integer; lpDefaultCharFake: Integer;
  lpUsedDefaultCharFake: Integer): Integer;
  external '<EMAIL> stdcall';

function GetStringAsUtf8(S: string): AnsiString;
var
  Len: Integer;
begin
  Len := WideCharToMultiByte(CP_UTF8, 0, S, Length(S), Result, 0, 0, 0);
  SetLength(Result, Len);
  WideCharToMultiByte(CP_UTF8, 0, S, Length(S), Result, Len, 0, 0);
end;

function SaveStringToUTF8FileWithoutBOM(FileName: string; S: string): Boolean;
var
  Utf8: AnsiString;
begin
  Utf8 := GetStringAsUtf8(S);
  Result := SaveStringToFile(FileName, Utf8, true);
end;

function LastPos(SubStr: string; Str: ansistring): Integer;
var
  Idx: Integer; // an index of SubStr in Str
  len: Integer;
begin
  
  Result := 0;
  Idx := Pos(SubStr, Str);
  if Idx = 0 then
    Exit;
  while Idx > 0 do
  begin
    Result := Result + Idx;
	len := length(Str);
	Str := Copy(Str, Idx + 1,len-1);
    Idx := Pos(SubStr, Str);
  end;
end;