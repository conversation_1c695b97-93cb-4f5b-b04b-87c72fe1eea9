﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using zgUtils;
using zgUtils.Controls;
using zgUtils.Model;

namespace zgzn
{
    /// <summary>
    /// 配置控制器
    /// </summary>
    public static class ConfigController
    {
        public const string IniPath = "./Programs/config";
        public static string DefaultConfigURL;
        private static Configuration UnityConfig = ConfigurationManager.OpenExeConfiguration(Application.ExecutablePath);

        public static string Authorization { get; set; }
        /// <summary>
        /// 默认配置信息
        /// </summary>
        public static Dictionary<string, object> dics { get; set; }
        /// <summary>
        /// 装填默认配置
        /// </summary>
        public static void LoadDefaultConfig(Dictionary<string, object> _dics)
        {
            dics = _dics;
            ConfigController.DefaultConfigURL = dics["DefaultConfigURL"].ToString();
            ConfigController.Authorization = dics["Authorization"].ToString();
            if (dics.ContainsKey("IndustryUpdated")) zgUtils.CommonApp.IndustryUpdated = (bool)dics["IndustryUpdated"];
            var LogPath = Environment.CurrentDirectory + "\\Logs";
            if (dics.ContainsKey("LogPath")) LogPath = dics["LogPath"].ToString();
            zgLogging.Log.CreateLogListener(LogPath);

            //zgLogging.Log.Info("装载默认配置信息..."+ Newtonsoft.Json.JsonConvert.SerializeObject(_dics));
            
        }

        public static void ReLoadConfig() {
            UnityConfig = ConfigurationManager.OpenExeConfiguration(Application.ExecutablePath);
        }
        /// <summary>
        /// 读取配置
        /// </summary>
        /// <param name="Key">配置Key</param>
        /// <returns>配置信息</returns>
        public static string GetConfig(string Key)
        {
            zgLogging.Log.Debug("获取配置项Key: {0}", Key);
            string ConfigValue = string.Empty;
            try
            {
                KeyValueConfigurationElement ConfigurationElement = UnityConfig.AppSettings.Settings[Key];
                ConfigValue = ConfigurationElement?.Value ?? string.Empty;
                zgLogging.Log.Debug(">>> ConfigValue[\"{0}\"] = {1}", Key, ConfigValue.Length<=1024?ConfigValue: "0x" + ConfigValue.GetHashCode().ToString("X"));
                return ConfigValue;
            }
            catch (Exception ex)
            {
                zgLogging.Log.Error("读取配置失败 : Key = {0}, Message:{1}",Key, ex.Message);
                return string.Empty;
            }
        }

        /// <summary>
        /// 设置配置
        /// </summary>
        /// <param name="Key">配置Key</param>
        /// <param name="Value">配置Value</param>
        internal static void SaveIndustryConfig(IndustryClass industry)
        {
            try {
                var IndustryName = industry?.className ?? "";
                var token = industry?.token ?? "";
                if (string.IsNullOrEmpty(IndustryName))
                {
                    return;
                }
                Authorization = token;
                CommonApp.Authorization = token;

                IniFile iniFile = new IniFile(IniPath);
                iniFile.Write("industryname", IndustryName);
                iniFile.Write("token", token);
            } catch (Exception ex) {
                zgLogging.Log.Error(ex.Message);
            }
            
        }

        public static void SaveIndustryConfig(string IndustryName)
        {
            SaveIndustryConfig(IndustryClass.GetIndustry(IndustryName));
           
        }
        public static string GetIndustryName()
        {
            string IndustryName = "";
            try
            {
                if (File.Exists(IniPath))
                {
                    IniFile iniFile = new IniFile(IniPath);
                    IndustryName = iniFile.Read("industryname");
                }
                else
                {
                    IndustryName = GetConfig("ProgramFile");
                }
            }
            catch (Exception ex)
            {
                zgLogging.Log.Error(ex.Message);
            }
            
            return IndustryName;
        }
        public static string GetIndustryToken()
        {
            string token = "";
            try
            {
                if (File.Exists(IniPath))
                {
                    IniFile iniFile = new IniFile(IniPath);
                    token = iniFile.Read("token");
                }
                else
                {
                    token = GetConfig("Authorization");
                }
            }
            catch (Exception ex)
            {
                zgLogging.Log.Error(ex.Message);
            }

            return token;
        }



    }
}
