﻿using PrintCore;
using System;
using System.Drawing;

namespace PrintCore
{
    public class PrintHelper
    {
        public static void PrintLabel(PrintLabelModel printdata)
        {
           int Length = 20;
            foreach (var item in printdata.items)
            {
                var printer = PrinterFactory.GetPrinter(printdata.printname, printdata.Width, printdata.Height);
                printer.setLandscape(printdata.Landscape);
                if (printdata.Landscape) printer.NewRow();
                if (string.IsNullOrEmpty(item.name))
                {
                    printer.NewRow(printdata.DefaultSize);
                }
                else
                {
                    string name = item.name;
                    if (name.Length > Length)
                    {
                        name = name.Substring(0, Length);
                    }
                    printer.PrintText(name, fontSize: printdata.DefaultSize, offset: printdata.Offset);
                    if (printer.ContentRowNum(name, offset: printdata.Offset) == 1)
                    {
                        printer.NewRow(printdata.DefaultSize);
                    }
                }
                printer.NewRow(printdata.DefaultSize);
                if (!string.IsNullOrEmpty(item.sale_price))
                {
                    printer.PrintText("商品售价：" + @"￥" + item.sale_price, fontSize: printdata.DefaultSize, offset: printdata.Offset);
                }
                else
                {
                    //printer.NewRow();
                }
                printer.NewRow(printdata.DefaultSize);
                if (!string.IsNullOrEmpty(item.code))
                {
                    Bitmap bitmap = null;
                    try
                    {
                        bitmap = PrintCore.BarcodeHelper.Generate2(item.code, printdata.Image_Width, printdata.Image_Height);
                    }
                    catch 
                    {
                        //Log.WriterExceptionLog(ex.Message);
                        bitmap = PrintCore.BarcodeHelper.Generate2_Format(item.code, printdata.Image_Width, printdata.Image_Height, ZXing.BarcodeFormat.CODE_128);
                    }
                    printer.PrintImage(bitmap, StringAlignment.Center);

                }
                else
                {
                    printer.NewRow(printdata.DefaultSize);
                    printer.NewRow(printdata.DefaultSize);
                }
                printer.NewRow(printdata.DefaultSize);

                if (!string.IsNullOrEmpty(item.commodityDate) && !string.IsNullOrEmpty(item.commodityEndDate))
                {
                    printer.PrintText("生产日期：" + item.commodityDate + "  保质期：" + item.commodityEndDate + "天", fontSize: FontSize.micro, offset: printdata.Offset);
                }
                else
                {
                    if (!string.IsNullOrEmpty(item.commodityDate))
                    {
                        printer.PrintText("生产日期：" + item.commodityDate, fontSize: FontSize.micro, offset: printdata.Offset);
                    }
                    else
                    {
                        printer.PrintText("                    ", fontSize: FontSize.micro, offset: printdata.Offset);
                    }
                    if (!string.IsNullOrEmpty(item.commodityEndDate))
                    {
                        printer.PrintText("保质期：" + item.commodityEndDate + "天", fontSize: FontSize.micro, offset: printdata.Offset);
                    }
                }
                printer.NewRow(printdata.DefaultSize);
                printer.Finish();
            }
           

        }
        public static void PrintLabel_new(PrintLabelModel printdata)
        {
           
            var printer = PrinterFactory.GetPrinter(printdata.printname, printdata.Width, printdata.Height);
            printer.setLandscape(printdata.Landscape);
            foreach (var item in printdata.others)
            {
                if (!string.IsNullOrEmpty(item.qrcode))
                {
                    Bitmap qr_bitmap = null;
                    qr_bitmap = PrintCore.BarcodeHelper.Generate3(item.qrcode, item.qrcode_Width, item.qrcode_Height, item.qrcode_logo);
                    printer.PrintQrcode(qr_bitmap, System.Drawing.StringAlignment.Center);
                }else if (!string.IsNullOrEmpty(item.barcode))
                {
                    Bitmap bitmap2 = null;
                    try
                    {
                        bitmap2 = PrintCore.BarcodeHelper.Generate2(item.barcode, item.barcode_Width, item.barcode_Height);
                    }
                    catch
                    {
                        bitmap2 = PrintCore.BarcodeHelper.Generate2_Format(item.barcode, item.barcode_Width, item.barcode_Height, ZXing.BarcodeFormat.CODE_128);
                    }
                    printer.PrintImage(bitmap2, StringAlignment.Center);
                }
                else
                {
                    if ((item.Text ?? string.Empty).Length > 20)
                    {
                        printer.PrintText(item.Title + item.Text, item.Size ?? printdata.DefaultSize, offset: printdata.Offset);
                    }
                    else
                    {
                        float newoffset = printer.PrintTitle(item.Title, fontSize: printdata.DefaultSize, offset: printdata.Offset);
                        printer.PrintTextHeightUseTitileHeight(item.Text, item.Size ?? printdata.DefaultSize, offset: newoffset, titleFontSize: printdata.DefaultSize);
                    }

                }
                printer.NewRow(printdata.DefaultSize);
            }
            printer.Finish();
           
        }

        /// <summary>
        /// 打印标签，并且条码使用毫米
        /// </summary>
        /// <param name="printdata"></param>
        public static void PrintLabelAndBarcodeInMM(PrintLabelModel printdata)
        {

            var printer = PrinterFactory.GetPrinter(printdata.printname, printdata.Width, printdata.Height);
            printer.setLandscape(printdata.Landscape);
            foreach (var item in printdata.others)
            {
                if (!string.IsNullOrEmpty(item.qrcode))
                {
                    Bitmap qr_bitmap = null;
                    qr_bitmap = BarcodeHelper.Generate3(item.qrcode, item.qrcode_Width, item.qrcode_Height, item.qrcode_logo);
                    printer.PrintQrcode(qr_bitmap, StringAlignment.Center);
                }
                else if (!string.IsNullOrEmpty(item.barcode))
                {
                    Bitmap barcodeBitmap = BarcodeHelper.GenerateBarcodeBitmap(item.barcode, item.barcode_Width, item.barcode_Height);
                    printer.PrintImage(barcodeBitmap, StringAlignment.Center);

                }
                else
                {
                    if ((item.Text ?? string.Empty).Length > 20)
                    {
                        printer.PrintText(item.Title + item.Text, item.Size ?? printdata.DefaultSize, offset: printdata.Offset);
                    }
                    else
                    {
                        float newoffset = printer.PrintTitle(item.Title, fontSize: printdata.DefaultSize, offset: printdata.Offset);
                        printer.PrintTextHeightUseTitileHeight(item.Text, item.Size ?? printdata.DefaultSize, offset: newoffset, titleFontSize: printdata.DefaultSize);
                    }

                }
                printer.NewRow(printdata.DefaultSize);
            }
            printer.Finish();

        }
    }
}
