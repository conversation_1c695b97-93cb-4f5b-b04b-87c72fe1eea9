<?xml version="1.0" encoding="UTF-8"?>
<AnalysisInput>
  <Settings>
    <Setting>
      <Key>sonar.vbnet.roslyn.ignoreIssues</Key>
      <Value>false</Value>
    </Setting>
    <Setting>
      <Key>sonar.vbnet.ignoreHeaderComments</Key>
      <Value>true</Value>
    </Setting>
    <Setting>
      <Key>sonar.vbnet.file.suffixes</Key>
      <Value>.vb</Value>
    </Setting>
  </Settings>
  <Rules>
    <Rule>
      <Key>S1654</Key>
      <Parameters>
        <Parameter>
          <Key>format</Key>
          <Value>^[a-z][a-z0-9]*([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?$</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S2344</Key>
    </Rule>
    <Rule>
      <Key>S2345</Key>
    </Rule>
    <Rule>
      <Key>S2346</Key>
    </Rule>
    <Rule>
      <Key>S2347</Key>
      <Parameters>
        <Parameter>
          <Key>format</Key>
          <Value>^(([a-z][a-z0-9]*)?([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?_)?([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?$</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S112</Key>
    </Rule>
    <Rule>
      <Key>S2068</Key>
      <Parameters>
        <Parameter>
          <Key>credentialWords</Key>
          <Value>password, passwd, pwd</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S2342</Key>
      <Parameters>
        <Parameter>
          <Key>format</Key>
          <Value>^([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?$</Value>
        </Parameter>
        <Parameter>
          <Key>flagsAttributeFormat</Key>
          <Value>^([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?s$</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S1656</Key>
    </Rule>
    <Rule>
      <Key>S2340</Key>
    </Rule>
    <Rule>
      <Key>S4529</Key>
    </Rule>
    <Rule>
      <Key>S2349</Key>
    </Rule>
    <Rule>
      <Key>S1542</Key>
      <Parameters>
        <Parameter>
          <Key>format</Key>
          <Value>^([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?$</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S1940</Key>
    </Rule>
    <Rule>
      <Key>S2355</Key>
    </Rule>
    <Rule>
      <Key>S2358</Key>
    </Rule>
    <Rule>
      <Key>S2352</Key>
    </Rule>
    <Rule>
      <Key>S2077</Key>
    </Rule>
    <Rule>
      <Key>S2359</Key>
    </Rule>
    <Rule>
      <Key>S2757</Key>
    </Rule>
    <Rule>
      <Key>S4817</Key>
    </Rule>
    <Rule>
      <Key>S4818</Key>
    </Rule>
    <Rule>
      <Key>S114</Key>
      <Parameters>
        <Parameter>
          <Key>format</Key>
          <Value>^I([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?$</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S117</Key>
      <Parameters>
        <Parameter>
          <Key>format</Key>
          <Value>^[a-z][a-z0-9]*([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?$</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S3449</Key>
    </Rule>
    <Rule>
      <Key>S3603</Key>
    </Rule>
    <Rule>
      <Key>S1751</Key>
    </Rule>
    <Rule>
      <Key>S1871</Key>
    </Rule>
    <Rule>
      <Key>S3011</Key>
    </Rule>
    <Rule>
      <Key>S3776</Key>
      <Parameters>
        <Parameter>
          <Key>threshold</Key>
          <Value>15</Value>
        </Parameter>
        <Parameter>
          <Key>propertyThreshold</Key>
          <Value>3</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S1075</Key>
    </Rule>
    <Rule>
      <Key>S1197</Key>
    </Rule>
    <Rule>
      <Key>S1479</Key>
      <Parameters>
        <Parameter>
          <Key>maximum</Key>
          <Value>30</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S4507</Key>
    </Rule>
    <Rule>
      <Key>S4586</Key>
    </Rule>
    <Rule>
      <Key>S1643</Key>
    </Rule>
    <Rule>
      <Key>S1764</Key>
    </Rule>
    <Rule>
      <Key>S2178</Key>
    </Rule>
    <Rule>
      <Key>S101</Key>
      <Parameters>
        <Parameter>
          <Key>format</Key>
          <Value>^([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?$</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S1523</Key>
    </Rule>
    <Rule>
      <Key>S1645</Key>
    </Rule>
    <Rule>
      <Key>S2737</Key>
    </Rule>
    <Rule>
      <Key>S3385</Key>
    </Rule>
    <Rule>
      <Key>S1481</Key>
    </Rule>
    <Rule>
      <Key>S3358</Key>
    </Rule>
    <Rule>
      <Key>S3598</Key>
    </Rule>
    <Rule>
      <Key>S4721</Key>
    </Rule>
    <Rule>
      <Key>S5042</Key>
    </Rule>
    <Rule>
      <Key>S1862</Key>
    </Rule>
    <Rule>
      <Key>S2304</Key>
      <Parameters>
        <Parameter>
          <Key>format</Key>
          <Value>^([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?(\.([A-Z]{1,3}[a-z0-9]+)*([A-Z]{2})?)*$</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S2951</Key>
    </Rule>
    <Rule>
      <Key>S1186</Key>
    </Rule>
    <Rule>
      <Key>S2551</Key>
    </Rule>
    <Rule>
      <Key>S3926</Key>
    </Rule>
    <Rule>
      <Key>S3927</Key>
    </Rule>
    <Rule>
      <Key>S4210</Key>
    </Rule>
    <Rule>
      <Key>S1313</Key>
    </Rule>
    <Rule>
      <Key>S2368</Key>
    </Rule>
    <Rule>
      <Key>S3889</Key>
    </Rule>
    <Rule>
      <Key>S3923</Key>
    </Rule>
    <Rule>
      <Key>S4787</Key>
    </Rule>
    <Rule>
      <Key>S2365</Key>
    </Rule>
    <Rule>
      <Key>S2761</Key>
    </Rule>
    <Rule>
      <Key>S3453</Key>
    </Rule>
    <Rule>
      <Key>S4144</Key>
    </Rule>
    <Rule>
      <Key>S3693</Key>
    </Rule>
    <Rule>
      <Key>S4143</Key>
    </Rule>
    <Rule>
      <Key>S4428</Key>
    </Rule>
    <Rule>
      <Key>S4784</Key>
    </Rule>
    <Rule>
      <Key>S4823</Key>
    </Rule>
    <Rule>
      <Key>S4825</Key>
    </Rule>
    <Rule>
      <Key>S4829</Key>
    </Rule>
    <Rule>
      <Key>S1048</Key>
    </Rule>
    <Rule>
      <Key>S4159</Key>
    </Rule>
    <Rule>
      <Key>S4260</Key>
    </Rule>
    <Rule>
      <Key>S4797</Key>
    </Rule>
    <Rule>
      <Key>S2375</Key>
      <Parameters>
        <Parameter>
          <Key>minimumSeriesLength</Key>
          <Value>6</Value>
        </Parameter>
      </Parameters>
    </Rule>
    <Rule>
      <Key>S3464</Key>
    </Rule>
    <Rule>
      <Key>S3466</Key>
    </Rule>
    <Rule>
      <Key>S3981</Key>
    </Rule>
    <Rule>
      <Key>S4277</Key>
    </Rule>
    <Rule>
      <Key>S2255</Key>
    </Rule>
    <Rule>
      <Key>S2376</Key>
    </Rule>
    <Rule>
      <Key>S3903</Key>
    </Rule>
    <Rule>
      <Key>S2372</Key>
    </Rule>
    <Rule>
      <Key>S3869</Key>
    </Rule>
    <Rule>
      <Key>S4790</Key>
    </Rule>
    <Rule>
      <Key>S4834</Key>
    </Rule>
    <Rule>
      <Key>S4275</Key>
    </Rule>
    <Rule>
      <Key>S4792</Key>
    </Rule>
  </Rules>
  <Files>
  </Files>
</AnalysisInput>
