﻿using CefSharp;
using System;

namespace zgpos.Browser
{
    /// <summary>
    /// 文件下载
    /// </summary>
    public class MyDownLoadFile : IDownloadHandler
    {
        public void OnBeforeDownload(IWebBrowser chromiumWebBrowser, IBrowser browser, DownloadItem downloadItem, IBeforeDownloadCallback callback)
        {
            if (!callback.IsDisposed)
            {
                using (callback)
                {
                    callback.Continue(Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory) +
                            @"\" +
                            downloadItem.SuggestedFileName,
                        showDialog: true);
                }
            }
        }

        public void OnDownloadUpdated(IWebBrowser chromiumWebBrowser, IBrowser browser, DownloadItem downloadItem, IDownloadItemCallback callback)
        {
        }

        public bool OnDownloadUpdated(DownloadItem downloadItem)
        {
            return false;
        }

	}
}
