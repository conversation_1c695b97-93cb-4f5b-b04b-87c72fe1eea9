﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using zgUtils;

namespace zgAdvert.Network
{
    public class EXFRequestInfo : RequestInfoBase
    {
        public override string BaseUrl
        {
            get
            {
                return AdvertFactory.AdvertBaseUrl;
            }
        }

        public override string SysUid { get; set; }

        public override string AppKey
        {
            get
            {
                return this.appkey;
            }
            set
            {
                this.appkey = value;
            }
        }

        public override string Version
        {
            get
            {
                return this.version;
            }
        }

        private string appkey = "zgzn";

        private string version = "0.0.1";
    }

}
