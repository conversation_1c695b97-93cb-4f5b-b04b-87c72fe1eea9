﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using zgLogging;
using zgUtils.Model;

namespace zgUtils.Controls
{
   public class FileManager
    {

        /// <summary>
        /// 上传媒体文件
        /// </summary>
        /// <param name="url">上传媒体文件的微信公众平台接口地址</param>
        /// <param name="file">要上传的媒体文件对象</param>
        /// <returns>返回上传的响应结果，详情参看微信公众平台开发者接口文档</returns>
        public static string UploadPost(string url, string file)
        {
            string result = String.Empty;
            try
            {
                WebClient client = new WebClient();
                client.Credentials = CredentialCache.DefaultCredentials;
                byte[] responseArray = client.UploadFile(url, "POST", file);
                result = System.Text.Encoding.Default.GetString(responseArray, 0, responseArray.Length);
                Log.WriterNormalLog("上传result:" + result);
            }
            catch (Exception ex)
            {
                result = "Error:" + ex.Message;
                Log.WriterNormalLog("发送请求出现异常：" + ex.Message);
            }
            finally
            {
                Log.WriterNormalLog("上传MediaId:" + result);
            }
            return result;
        }

        public static void DownloadFile(string Url, string FileName)
        {
            bool Value = false;
            WebResponse response = null;
            Stream stream = null;
            try
            {
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(Url);
                response = request.GetResponse();
                stream = response.GetResponseStream();
                if (!response.ContentType.ToLower().StartsWith("text/"))
                {
                    Value = SaveBinaryFile(response, FileName);
                    Log.WriterNormalLog(Url + "=>" + FileName);
                }
            }
            catch (Exception err)
            {
                Log.WriterExceptionLog(err.Message);
            }
        }
        /// <summary>
        /// Save a binary file to disk.
        /// </summary>
        /// <param name="response">The response used to save the file</param>
        // 将二进制文件保存到磁盘
        private static bool SaveBinaryFile(WebResponse response, string FileName)
        {
            bool Value = true;
            byte[] buffer = new byte[1024];
            try
            {
                if (File.Exists(FileName))
                    File.Delete(FileName);
                Stream outStream = System.IO.File.Create(FileName);
                Stream inStream = response.GetResponseStream();
                int l;
                do
                {
                    l = inStream.Read(buffer, 0, buffer.Length);
                    if (l > 0)
                        outStream.Write(buffer, 0, l);
                }
                while (l > 0);
                outStream.Close();
                inStream.Close();
            }
            catch (Exception err)
            {
                Log.WriterExceptionLog(err.Message);
                Value = false;
            }
            return Value;
        }

        public static void WriteIniData(string iniFilePath, string Value)
        {
            using (FileStream fs = File.Create(iniFilePath))
            {
                Byte[] info = new UTF8Encoding(true).GetBytes(Value);
                // Add some information to the file.
                fs.Write(info, 0, info.Length);
            }
        }

        public static T ReadIniData<T>(string iniFilePath) where T : ConfigBaseInfo
        {
            T result = null;

            string config = File.ReadAllText(iniFilePath);
            if (!string.IsNullOrEmpty(config))
            {
                result = Activator.CreateInstance<T>();
                result.data = config;
            }
            return result;

        }

        /// <summary>
        /// 读取配置
        /// </summary>
        /// <param name="Key">配置Key</param>
        /// <returns>配置信息</returns>
        public static string GetConfig(string Key, string ConfigFilePath)
        {
            Log.WriterNormalLog(string.Format("获取配置项Key: {0}", Key));
            string ConfigValue = string.Empty;
            try
            {
                Configuration UnityConfig = ConfigurationManager.OpenExeConfiguration(CommonApp.ProgramFile);
                KeyValueConfigurationElement ConfigurationElement = UnityConfig.AppSettings.Settings[Key];
                ConfigValue = ConfigurationElement?.Value ?? string.Empty;
                Log.WriterNormalLog(string.Format(">>> ConfigValue[\"{0}\"] = {1}", Key, ConfigValue.Length <= 1024 ? ConfigValue : "0x" + ConfigValue.GetHashCode().ToString("X")));
                return ConfigValue;
            }
            catch (Exception ex)
            {
                Log.WriterNormalLog(string.Format("读取配置失败 : Key = {0}, Message:{1}", Key, ex.Message));
                return string.Empty;
            }
        }
        /// <summary>
        /// 设置配置
        /// </summary>
        /// <param name="Key">配置Key</param>
        /// <param name="Value">配置Value</param>
        public static void SetConfig(string Key, string Value, string ConfigFilePath)
        {
            Log.WriterNormalLog(string.Format("设置配置项Key:{0}, Value:{1}", Key, Value));
            try
            {
                Configuration UnityConfig = ConfigurationManager.OpenExeConfiguration(CommonApp.ProgramFile);
                UnityConfig.AppSettings.Settings.Remove(Key);
                UnityConfig.AppSettings.Settings.Add(Key, Value);
                UnityConfig.Save();
            }
            catch (Exception ex)
            {
                Log.WriterNormalLog(string.Format("设置配置失败.Key:{0}, Value:{1}, Message:{2}", Key, Value, ex.Message));
                //throw ex;
            }
        }
    }
}
