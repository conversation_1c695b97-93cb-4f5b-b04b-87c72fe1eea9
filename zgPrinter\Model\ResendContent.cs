﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace zgPrinter.Model
{
    class ResendContent
    {
        /// <summary>
        /// 打印机IP
        /// </summary>
        public string IP { get; set; }

        /// <summary>
        /// 打印机端口号
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// 打印份数
        /// </summary>
        public int PrintCount { get; set; }

        /// <summary>
        /// 打印内容
        /// </summary>
        public byte[] Buffer { get; set; }

        /// <summary>
        /// 第一次失败时间
        /// </summary>
        public DateTime FirstTime { get; set; }
    }
}
