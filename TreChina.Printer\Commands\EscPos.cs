﻿using ESCPOS.Printer.Enums;
using ESCPOS.Printer.Commands;
using ESCPOS.Printer.Extensions;
using ESCPOS.Printer.Interfaces.Command;

namespace ESCPOS.Printer.Commands
{
    internal class EscPos : IPrintCommand
    {
        public IFontMode FontMode { get; set; }
        public IFontWidth FontWidth { get; set; }
        public IAlignment Alignment { get; set; }
        public IPaperCut PaperCut { get; set; }
        public IDrawer Drawer { get; set; }
        public IQrCode QrCode { get; set; }
        public IBarCode BarCode { get; set; }
        public IInitializePrint InitializePrint { get; set; }
        public IImage Image { get; set; }
        public ILineHeight  LineHeight { get; set; }
        public int ColsNomal => 32;
        public int ColsCondensed => 42;
        public int ColsExpanded => 24;

        public IFontHeight FontHeight { get; set; }
        public int Cols { get; set; }
        public EscPos()
        {
            FontMode = new FontMode();
            FontWidth = new FontWidth();
            FontHeight = new FontHeight();
            Alignment = new Alignment();
            PaperCut = new PaperCut();
            Drawer = new Drawer();
            QrCode = new QrCode();
            BarCode = new BarCode();
            Image = new Image();
            LineHeight = new LineHeight();
            InitializePrint = new InitializePrint();
        }

        public byte[] Separator(char speratorChar= '-')
        {
            
            return FontMode.Condensed(PrinterModeState.Off)
                .AddBytes(new string(speratorChar, Cols))
                .AddBytes(FontMode.Condensed(PrinterModeState.Off))
                .AddCrLF();
        }

        public byte[] AutoTest()
        {
            return new byte[] { 29, 40, 65, 2, 0, 0, 2 };
        }

    }
}

