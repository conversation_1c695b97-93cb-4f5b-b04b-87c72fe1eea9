﻿using CefSharp;
using CefSharp.WinForms;
using System;
using System.IO;
using System.Runtime.CompilerServices;
using zgLogging;
using zgUtils;
using zgUtils.Model;

namespace zgpos
{
    public class CefHelper
    {
        public const string CefLibName = "cefLib"; //cef目录名称
        public const string LibName = "Libs";
        [MethodImpl(MethodImplOptions.NoInlining)]
        public static void InitCef()
        {
            Log.WriterNormalLog("Cef 初始化开始......");
            //For Windows 7 and above, best to include relevant app.manifest entries as well
            Cef.EnableHighDPISupport();
            CefSharpSettings.ConcurrentTaskExecution = true;
            var settings = new CefSettings() {
                BrowserSubprocessPath = Path.Combine(zgzn.UnityModule.ProgramDirectory, CefLibName, "CefSharp.BrowserSubprocess.exe"),
                LocalesDirPath = System.IO.Path.Combine(zgzn.UnityModule.ProgramDirectory, CefLibName, @"locales"),
                ResourcesDirPath = System.IO.Path.Combine(zgzn.UnityModule.ProgramDirectory, CefLibName)
        };
            //Must specify these three paths



#if NETCOREAPP
                        //We are using our current exe as the BrowserSubProcess
                        //Multiple instances will be spawned to handle all the 
                        //Chromium proceses, render, gpu, network, plugin, etc.
                        var subProcessExe = new CefSharp.BrowserSubprocess.BrowserSubprocessExecutable();
                        var result = subProcessExe.Main(args);
                        if (result > 0)
                        {
                            return result;
                        }
#endif

            //var settings = new CefSettings();

            //#if NETCOREAPP
            //            //We use our Applications exe as the BrowserSubProcess, multiple copies
            //            //will be spawned
            //            var exePath = System.Diagnostics.Process.GetCurrentProcess().MainModule.FileName;
            //            settings.BrowserSubprocessPath = exePath;
            //#endif
            //            //settings.Locale = "zh-CN";
            //            //settings.AcceptLanguageList = "zh-CN";
            // Initialize cef with the provided settings
            settings.CefCommandLineArgs.Add("disable-gpu", "1");
            settings.CefCommandLineArgs.Add("disable-gpu-compositing", "1");
            settings.CefCommandLineArgs.Add("enable-begin-frame-scheduling", "1");
            settings.CefCommandLineArgs.Add("disable-gpu-vsync", "1"); //Disable Vsync
                                                                       //Disables the DirectWrite font rendering system on windows.
                                                                       //Possibly useful when experiencing blury fonts.
            settings.CefCommandLineArgs.Add("disable-direct-write", "1");
            settings.CefCommandLineArgs.Add("proxy-auto-detect", "0");
            settings.CefCommandLineArgs.Add("no-proxy-server", "1");
            settings.CefCommandLineArgs.Add("enable-media-stream", "1");
            settings.CefCommandLineArgs.Add("disable-pinch", "1");//禁止触屏双指缩放
            settings.CefCommandLineArgs.Add("allow-file-access-from-files", "allow-file-access-from-files");
            settings.CefCommandLineArgs.Add("disable-web-security", "disable-web-security");
                                                                                                                                                //log日志等级为error
            settings.LogSeverity = LogSeverity.Disable;
            // 注册 "https://www.zgpos.com/"
            settings.RegisterScheme(new CefCustomScheme()
            {
                SchemeName = ConfigBase.Instance.Scheme,
                SchemeHandlerFactory = new zgpos.EventHandlers.ResourceSchemeHandlerFactory(),
                IsFetchEnabled = true,
                IsLocal = true,
                IsCorsEnabled = true,
                IsSecure = true,
                DomainName = ConfigBase.Instance.DomainName,


            });

            
            Cef.Initialize(settings, performDependencyCheck: false, browserProcessHandler: null);
            

        }
        
    }
}
