﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Setting
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
            Win32Utility.SetCueText(this.textBox1, "请输入本番配置信息地址");
            Win32Utility.SetCueText(this.textBox2, "请输入开发配置信息地址");
            Win32Utility.SetCueText(this.textBox3, "请输入Token:Bearer ......");
            Win32Utility.SetCueText(this.textBox4, "请输入品牌（zgzn）");
            Win32Utility.SetCueText(this.textBox6, "请输入品牌名（掌柜智囊）");
            Win32Utility.SetCueText(this.textBox5, "请输入业态（clothing）");
            Win32Utility.SetCueText(this.textBox7, "请输入品牌名（服装）");
        }
        delegate void SendToParent(string txt);
        private void button1_Click(object sender, EventArgs e)
        {
            Thread connThread = new Thread(start);
            //connThread = new Thread(new ThreadStart(ConnServer));

            connThread.IsBackground = true;
            connThread.Start();
        }
        private void ConnServerRes(string str)
        {

            //操作主线程中的控件
            listBox1.Items.Insert(0,str);

        }
        private void start() {
            SendToParent stc = new SendToParent(ConnServerRes);
            
            string dir = System.IO.Path.Combine(Environment.CurrentDirectory, Guid.NewGuid().ToString("N"));
            string sourceFolder = System.IO.Path.Combine(Environment.CurrentDirectory.Replace(@"bin\Debug", ""), "Program");
            Directory.CreateDirectory(dir);
            this.Invoke(stc, new object[] { "创建工程开始......." + dir });
            CopyFolder(sourceFolder, dir);
            string program = System.IO.Path.Combine(dir, "Program");
            //if (!Directory.Exists(program)) {
            //    Directory.CreateDirectory(program);
            //}
            this.Invoke(stc, new object[] { "创建工程结束......." + dir });
            Thread.Sleep(1000);

            this.Invoke(stc, new object[] { "创建工程开始......." + this.textBox4.Text });
            string _program = System.IO.Path.Combine(program, "zgStart", "App.config");
            execFile(_program, @"zgzn", this.textBox4.Text);
            execFile(_program, @"掌柜智囊", this.textBox6.Text);
            _program = System.IO.Path.Combine(program, "zgStart", "zgStart.csproj");
            execFile(_program, @"zgzn", this.textBox4.Text);
            Thread.Sleep(1000);
            this.Invoke(stc, new object[] { "创建工程结束......." + this.textBox4.Text });

            Thread.Sleep(1000);
            this.Invoke(stc, new object[] { "创建工程开始......." + this.textBox5.Text });
            System.Threading.Thread.Sleep(1000);
            _program = System.IO.Path.Combine(program, "Formats", "Program.cs");
            execFile(_program, @"服装版", this.textBox7.Text + "版");
            execFile(_program, @"kwwsv=22}j}q1}kdqjjxl}klqdqj1frp2}j}q0vhwwlqjv", AESHelper.StringEncoding(textBox1.Text));
            execFile(_program, @"kwwsv=22ghy1}kdqjjxl}klqdqj1frp2}j}q0vhwwlqjv", AESHelper.StringEncoding(textBox2.Text));
            string _accesstoken =this.textBox3.Text.StartsWith("Bearer ") ? this.textBox3.Text : "Bearer " + this.textBox3.Text;
            execFile(_program, @"Ehduhu#h|MkeJflRlMLX}X{PlM<1h|M4dZTlRmDvLqQ8f6UoeX8keZXlRlM9]6sxLlzlfJI|gJo3dZ<xVZTlRmDvLqQ4\nQy]JXlRlLzPVLvLpQyep]s]3onLmr{OFMmfpYkgJYnLmr{QmP|QmP{RGP{RWT|OFM}gZMR\Z4oLmrl\5{ygJksepflOFM}h[QWdZTlRmE<1EydhDm5RFkHW{e6W3JiokvDrY<rSIiQ<e0qUD<\qK]3uvWm\Ixli|GMn8kV6FH[y49dIQDOYttbOyot9b4J{jj", AESHelper.StringEncoding(_accesstoken));
            execFile(_program, @"Clothing", this.textBox5.Text);
            _program = System.IO.Path.Combine(program, "Formats", "Clothing.csproj");
            execFile(_program, @"Clothing", this.textBox5.Text);
            File.Move(_program, System.IO.Path.Combine(program, "Formats", this.textBox5.Text+".csproj"));
            _program = System.IO.Path.Combine(program, "Formats", @"Properties\Resources.Designer.cs");
            execFile(_program, @"Clothing", this.textBox5.Text);
            _program = System.IO.Path.Combine(program, "Formats", @"Properties\Settings.Designer.cs");
            execFile(_program, @"Clothing", this.textBox5.Text);
            Thread.Sleep(1000);
            Directory.Move(System.IO.Path.Combine(program, "Formats"), System.IO.Path.Combine(program, this.textBox5.Text));

            this.Invoke(stc, new object[] { "创建工程结束......." + this.textBox5.Text });

            Thread.Sleep(1000);
            this.Invoke(stc, new object[] { "安装包开始......." });
            _program = System.IO.Path.Combine(program, "install", "POS.iss");
            execFile(_program, @"zgzn", this.textBox4.Text);
            execFile(_program, @"clothing", this.textBox5.Text);
            _program = System.IO.Path.Combine(program, "install", "POS_new_product.iss");
            execFile(_program, @"zgzn", this.textBox4.Text);
            execFile(_program, @"掌柜智囊", this.textBox6.Text);
            execFile(_program, @"服装版", this.textBox7.Text+"版");

            _program = System.IO.Path.Combine(program, "install", "POS_new.iss");
            execFile(_program, @"clothing", this.textBox4.Text);
            execFile(_program, @"掌柜智囊", this.textBox6.Text);
            execFile(_program, @"服装版", this.textBox7.Text + "体验版");

            _program = System.IO.Path.Combine(program, "install","out", "ver.xml.init");
            execFile(_program, @"clothing", this.textBox4.Text+"/"+ this.textBox5.Text);
            _program = System.IO.Path.Combine(program, "install", "out", "ver.xml.prod");
            execFile(_program, @"clothing", this.textBox4.Text + "/" + this.textBox5.Text);

            this.Invoke(stc, new object[] { "安装包结束......." });

        }
        /// <summary>
        /// 复制文件夹及文件
        /// </summary>
        /// <param name="sourceFolder">原文件路径</param>
        /// <param name="destFolder">目标文件路径</param>
        /// <returns></returns>
        public int CopyFolder(string sourceFolder, string destFolder)
        {
            try
            {
                string folderName = System.IO.Path.GetFileName(sourceFolder);
                string destfolderdir = System.IO.Path.Combine(destFolder, folderName);
                string[] filenames = System.IO.Directory.GetFileSystemEntries(sourceFolder);
                foreach (string file in filenames)// 遍历所有的文件和目录
                {
                    if (System.IO.Directory.Exists(file))
                    {
                        string currentdir = System.IO.Path.Combine(destfolderdir, System.IO.Path.GetFileName(file));
                        if (!System.IO.Directory.Exists(currentdir))
                        {
                            System.IO.Directory.CreateDirectory(currentdir);
                        }
                        CopyFolder(file, destfolderdir);
                    }
                    else
                    {
                        string srcfileName = System.IO.Path.Combine(destfolderdir, System.IO.Path.GetFileName(file));
                        if (!System.IO.Directory.Exists(destfolderdir))
                        {
                            System.IO.Directory.CreateDirectory(destfolderdir);
                        }
                        System.IO.File.Copy(file, srcfileName);
                    }
                }

                return 1;
            }
            catch (Exception e)
            {

                MessageBox.Show(e.Message);
                return 0;
            }

        }

        void execFile(string file,string oldValue,string newValue)
        {
            int i = 0;
            var eType = EncodingType.GetType(file);
            var lines = File.ReadAllLines(file, eType);
            StreamWriter sw = new StreamWriter(file, false, eType);
            while (i < lines.Length)
            {
                sw.WriteLine(lines[i].Replace(oldValue, newValue));
                i++;
            }
            sw.Close();
        }

        private void Form1_Load(object sender, EventArgs e)
        {

        }

        private void button2_Click(object sender, EventArgs e)
        {
            textBox8.Text = AESHelper.StringEncoding(textBox1.Text);
            textBox9.Text = AESHelper.StringEncoding(textBox2.Text);
            string _accesstoken = this.textBox3.Text.StartsWith("Bearer ") ? this.textBox3.Text : "Bearer " + this.textBox3.Text;
            textBox10.Text = AESHelper.StringEncoding(_accesstoken);
        }

        private void label5_Click(object sender, EventArgs e)
        {
            textBox12.Text = AESHelper.Encrypt(textBox11.Text);
        }
    }
    /// <summary> 
    /// 获取文件的编码格式 
    /// </summary> 
    public class EncodingType
    {
        /// <summary> 
        /// 给定文件的路径，读取文件的二进制数据，判断文件的编码类型 
        /// </summary> 
        /// <param name=“FILE_NAME“>文件路径</param> 
        /// <returns>文件的编码类型</returns> 
        public static System.Text.Encoding GetType(string FILE_NAME)
        {
            FileStream fs = new FileStream(FILE_NAME, FileMode.Open, FileAccess.Read);
            Encoding r = GetType(fs);
            fs.Close();
            return r;
        }

        /// <summary> 
        /// 通过给定的文件流，判断文件的编码类型 
        /// </summary> 
        /// <param name=“fs“>文件流</param> 
        /// <returns>文件的编码类型</returns> 
        public static System.Text.Encoding GetType(FileStream fs)
        {
            byte[] Unicode = new byte[] { 0xFF, 0xFE, 0x41 };
            byte[] UnicodeBIG = new byte[] { 0xFE, 0xFF, 0x00 };
            byte[] UTF8 = new byte[] { 0xEF, 0xBB, 0xBF }; //带BOM 
            Encoding reVal = Encoding.Default;

            BinaryReader r = new BinaryReader(fs, System.Text.Encoding.Default);
            int i;
            int.TryParse(fs.Length.ToString(), out i);
            byte[] ss = r.ReadBytes(i);
            if (IsUTF8Bytes(ss) || (ss[0] == 0xEF && ss[1] == 0xBB && ss[2] == 0xBF))
            {
                reVal = Encoding.UTF8;
            }
            else if (ss[0] == 0xFE && ss[1] == 0xFF && ss[2] == 0x00)
            {
                reVal = Encoding.BigEndianUnicode;
            }
            else if (ss[0] == 0xFF && ss[1] == 0xFE && ss[2] == 0x41)
            {
                reVal = Encoding.Unicode;
            }
            r.Close();
            return reVal;

        }

        /// <summary> 
        /// 判断是否是不带 BOM 的 UTF8 格式 
        /// </summary> 
        /// <param name=“data“></param> 
        /// <returns></returns> 
        private static bool IsUTF8Bytes(byte[] data)
        {
            int charByteCounter = 1; //计算当前正分析的字符应还有的字节数 
            byte curByte; //当前分析的字节. 
            for (int i = 0; i < data.Length; i++)
            {
                curByte = data[i];
                if (charByteCounter == 1)
                {
                    if (curByte >= 0x80)
                    {
                        //判断当前 
                        while (((curByte <<= 1) & 0x80) != 0)
                        {
                            charByteCounter++;
                        }
                        //标记位首位若为非0 则至少以2个1开始 如:110XXXXX...........1111110X 
                        if (charByteCounter == 1 || charByteCounter > 6)
                        {
                            return false;
                        }
                    }
                }
                else
                {
                    //若是UTF-8 此时第一位必须为1 
                    if ((curByte & 0xC0) != 0x80)
                    {
                        return false;
                    }
                    charByteCounter--;
                }
            }
            if (charByteCounter > 1)
            {
                throw new Exception("非预期的byte格式");
            }
            return true;
        }

    }
    public class AESHelper
    {
        /// <summary>
        /// 默认密钥-密钥的长度必须是32
        /// </summary>
        private const string PublicKey = "1234567890123456";

        /// <summary>
        /// 默认向量
        /// </summary>
        private const string Iv = "abcdefghijklmnop";
        /// <summary>  
        /// AES加密  
        /// </summary>  
        /// <param name="str">需要加密字符串</param>  
        /// <returns>加密后字符串</returns>  
        public static String Encrypt(string str)
        {
            return Encrypt(str, PublicKey);
        }

        /// <summary>  
        /// AES解密  
        /// </summary>  
        /// <param name="str">需要解密字符串</param>  
        /// <returns>解密后字符串</returns>  
        public static String Decrypt(string str)
        {
            return Decrypt(str, PublicKey);
        }
        /// <summary>
        /// AES加密
        /// </summary>
        /// <param name="str">需要加密的字符串</param>
        /// <param name="key">32位密钥</param>
        /// <returns>加密后的字符串</returns>
        public static string Encrypt(string str, string key)
        {
            Byte[] keyArray = System.Text.Encoding.UTF8.GetBytes(key);
            Byte[] toEncryptArray = System.Text.Encoding.UTF8.GetBytes(str);
            var rijndael = new System.Security.Cryptography.RijndaelManaged();
            rijndael.Key = keyArray;
            rijndael.Mode = System.Security.Cryptography.CipherMode.ECB;
            rijndael.Padding = System.Security.Cryptography.PaddingMode.PKCS7;
            rijndael.IV = System.Text.Encoding.UTF8.GetBytes(Iv);
            System.Security.Cryptography.ICryptoTransform cTransform = rijndael.CreateEncryptor();
            Byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);
            return Convert.ToBase64String(resultArray, 0, resultArray.Length);
        }
        /// <summary>
        /// AES解密
        /// </summary>
        /// <param name="str">需要解密的字符串</param>
        /// <param name="key">32位密钥</param>
        /// <returns>解密后的字符串</returns>
        public static string Decrypt(string str, string key)
        {
            Byte[] keyArray = System.Text.Encoding.UTF8.GetBytes(key);
            Byte[] toEncryptArray = System.Text.Encoding.UTF8.GetBytes(str);
            var rijndael = new System.Security.Cryptography.RijndaelManaged();
            rijndael.Key = keyArray;
            rijndael.Mode = System.Security.Cryptography.CipherMode.ECB;
            rijndael.Padding = System.Security.Cryptography.PaddingMode.PKCS7;
            rijndael.IV = System.Text.Encoding.UTF8.GetBytes(Iv);
            System.Security.Cryptography.ICryptoTransform cTransform = rijndael.CreateDecryptor();
            Byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);
            return System.Text.Encoding.UTF8.GetString(resultArray);
        }
        public static string StringEncoding(string pwd = PublicKey)
        {
            char[] arrChar = pwd.ToCharArray();
            string strChar = "";
            for (int i = 0; i < arrChar.Length; i++)
            {
                arrChar[i] = Convert.ToChar(arrChar[i] + 3);
                strChar = strChar + arrChar[i].ToString();
            }
            return strChar;
        }
        public static string StringDecoding(string pwd = PublicKey)
        {
            char[] arrChar = pwd.ToCharArray();
            string strChar = "";
            for (int i = 0; i < arrChar.Length; i++)
            {
                arrChar[i] = Convert.ToChar(arrChar[i] - 3);
                strChar = strChar + arrChar[i].ToString();
            }
            return strChar;
        }
    }
}
