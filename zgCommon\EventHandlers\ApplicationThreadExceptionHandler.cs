﻿using System;
using System.Threading;
using zgLogging;

namespace zgpos.EventHandlers
{
    public static class ApplicationThreadExceptionHandler
    {
        public static void DoApplicationThreadException(object sender, ThreadExceptionEventArgs e)
            => Log.WriterNormalLog($"{nameof(ApplicationThreadExceptionHandler)}: {nameof(DoApplicationThreadException)} => {e.Exception.Message}");
    }
}
