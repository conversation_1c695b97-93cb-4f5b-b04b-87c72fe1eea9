﻿using Microsoft.Win32;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Management;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;

namespace zgUtils.Controls
{
    public class SysProp
    {
        private static SysProp instance;

        public static SysProp Instance
        {
            get
            {
                if (SysProp.instance == null)
                {
                    SysProp.instance = new SysProp();
                }
                return instance;
            }
        }
        public SysProp()
        {
            try
            {
                ManagementObjectSearcher PhysicalMemory = new ManagementObjectSearcher("select * from Win32_PhysicalMemory");
                ManagementObjectSearcher Processor = new ManagementObjectSearcher("select * from Win32_Processor");
                ManagementObjectSearcher Os = new ManagementObjectSearcher("select * from Win32_OperatingSystem");
                ManagementObjectSearcher VideoController = new ManagementObjectSearcher("select * from Win32_VideoController");
                ManagementObjectSearcher CompSys = new ManagementObjectSearcher("select * from Win32_ComputerSystem");

                PhysicalMemory_Capacity = String.Format("{0} MB", Convert.ToInt64(GetValue(PhysicalMemory, "Capacity")) / 1024 / 1024);

                ProcessorName = (string)GetValue(Processor, "Name");

                OperatingSystemBit = System.Environment.Is64BitOperatingSystem ? "64位" : "32位";
                Os_Caption = (string)GetValue(Os, "Caption");
                Os_Version = System.Environment.OSVersion.Version.ToString();
                ServicePack = !String.IsNullOrEmpty(System.Environment.OSVersion.ServicePack)
                    ? System.Environment.OSVersion.ServicePack
                    : "无";
                SystemSpecialFolder = System.Environment.GetFolderPath(System.Environment.SpecialFolder.System);
                Video_Caption = (string)GetValue(VideoController, "Caption");
                UserName = System.Environment.UserName;
                UserDomainName = System.Environment.UserDomainName;
                CompSys_Workgroup = (string)GetValue(CompSys, "Workgroup");
                ipAddress = GetPhysicsNetworkCardIP();
                ShowNetworkInterfaceMessage();
            }
            catch (Exception ex) {
                zgLogging.Log.WriterExceptionLog("取得设备信息失败：" + ex.Message);
            }
            
            
        }

        private object GetValue(ManagementObjectSearcher searcher, string propName)
        {
            foreach (ManagementObject mobj in searcher.Get())
                return mobj[propName];
            throw new NotSupportedException();
        }
        /// <summary></summary>  
        /// 显示本机各网卡的详细信息  
        /// <summary></summary>  
        private void ShowNetworkInterfaceMessage()
        {
            NetworkInterface[] fNetworkInterfaces = NetworkInterface.GetAllNetworkInterfaces();
            foreach (NetworkInterface adapter in fNetworkInterfaces)
            {
                MyNetworkInterface myNetworkInterface = new MyNetworkInterface();
                
                #region " 网卡类型 "
                
                string fRegistryKey = "SYSTEM\\CurrentControlSet\\Control\\Network\\{4D36E972-E325-11CE-BFC1-08002BE10318}\\" + adapter.Id + "\\Connection";
                RegistryKey rk = Registry.LocalMachine.OpenSubKey(fRegistryKey, false);
                if (rk != null)
                {
                    // 区分 PnpInstanceID   
                    // 如果前面有 PCI 就是本机的真实网卡  
                    // MediaSubType 为 01 则是常见网卡，02为无线网卡。  
                    string fPnpInstanceID = rk.GetValue("PnpInstanceID", "").ToString();
                    int fMediaSubType = Convert.ToInt32(rk.GetValue("MediaSubType", 0));
                    if (fPnpInstanceID.Length > 3 &&
                        fPnpInstanceID.Substring(0, 3) == "PCI")
                        myNetworkInterface.fCardType = "物理网卡";
                    else if (fMediaSubType == 1)
                        myNetworkInterface.fCardType = "虚拟网卡";
                    else if (fMediaSubType == 2)
                        myNetworkInterface.fCardType = "无线网卡";
                }
                myNetworkInterface.PhysicalAddressStr= adapter.GetPhysicalAddress().ToString(); // MAC 地址  
                myNetworkInterface.Id = adapter.Id;
                myNetworkInterface.Name = adapter.Name;
                myNetworkInterface.Description = adapter.Description;
                myNetworkInterface.NetworkInterfaceType = adapter.NetworkInterfaceType.ToString();
                myNetworkInterface.IsReceiveOnly = adapter.IsReceiveOnly.ToString();
                myNetworkInterface.SupportsMulticast = adapter.SupportsMulticast.ToString();
                myNetworkInterface.Speed = adapter.Speed.ToString();
                IPInterfaceProperties fIPInterfaceProperties = adapter.GetIPProperties();
                UnicastIPAddressInformationCollection UnicastIPAddressInformationCollection = fIPInterfaceProperties.UnicastAddresses;
                foreach (UnicastIPAddressInformation UnicastIPAddressInformation in UnicastIPAddressInformationCollection)
                {
                    if (UnicastIPAddressInformation.Address.AddressFamily == AddressFamily.InterNetwork)
                    {
                        myNetworkInterface.AddressList.Add(UnicastIPAddressInformation.Address.ToString());
                    }
                        
                       
                }
                NetworkInterfaces.Add(myNetworkInterface);
                #endregion
            }
            
        }

        /// <summary>
        /// 获得本机真实物理网卡IP
        /// </summary>
        /// <returns></returns>
        private IList<string> GetPhysicsNetworkCardIP()
        {
            var networkCardIPs = new List<string>();

            NetworkInterface[] fNetworkInterfaces = NetworkInterface.GetAllNetworkInterfaces();
            foreach (NetworkInterface adapter in fNetworkInterfaces)
            {
                string fRegistryKey = "SYSTEM\\CurrentControlSet\\Control\\Network\\{4D36E972-E325-11CE-BFC1-08002BE10318}\\" + adapter.Id + "\\Connection";
                RegistryKey rk = Registry.LocalMachine.OpenSubKey(fRegistryKey, false);
                if (rk != null)
                {
                    // 区分 PnpInstanceID   
                    // 如果前面有 PCI 就是本机的真实网卡  
                    string fPnpInstanceID = rk.GetValue("PnpInstanceID", "").ToString();
                    int fMediaSubType = Convert.ToInt32(rk.GetValue("MediaSubType", 0));
                    if (fPnpInstanceID.Length > 3 && fPnpInstanceID.Substring(0, 3) == "PCI")
                    {
                        IPInterfaceProperties fIPInterfaceProperties = adapter.GetIPProperties();
                        UnicastIPAddressInformationCollection UnicastIPAddressInformationCollection = fIPInterfaceProperties.UnicastAddresses;
                        foreach (UnicastIPAddressInformation UnicastIPAddressInformation in UnicastIPAddressInformationCollection)
                        {
                            if (UnicastIPAddressInformation.Address.AddressFamily == AddressFamily.InterNetwork)
                            {
                                networkCardIPs.Add(UnicastIPAddressInformation.Address.ToString()); //Ip 地址
                            }
                        }
                    }
                }
            }

            return networkCardIPs;
        }
        IList<string> ipAddress { get; set; }
        public List<MyNetworkInterface> NetworkInterfaces { get; set; } = new List<MyNetworkInterface>();
        
        /// <summary>
        /// 物理内存
        /// </summary>
        public string PhysicalMemory_Capacity { get; set; }

        /// <summary>
        /// 处理器
        /// </summary>
        public string ProcessorName { get; set; }

        /// <summary>
        /// 处理器架构
        /// </summary>
        public string OperatingSystemBit { get; set; }

        /// <summary>
        /// window名称
        /// </summary>
        public string Os_Caption { get; set; }

        /// <summary>
        /// window版本
        /// </summary>
        public string Os_Version { get; set; }

        /// <summary>
        ///
        /// </summary>
        public string ServicePack { get; set; }

        /// <summary>
        /// 系统目录
        /// </summary>
        public string SystemSpecialFolder { get; set; }

        /// <summary>
        /// 显卡名称
        /// </summary>
        public string Video_Caption { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 计算机名称
        /// </summary>
        public string UserDomainName { get; set; }

        /// <summary>
        /// 工作组
        /// </summary>
        public string CompSys_Workgroup { get; set; }

        public class MyNetworkInterface
        {
            public string Id { get; set; }
            public string Name { get; set; }
            public string Description { get; set; }
            public string SupportsMulticast { get; set; }
            public string NetworkInterfaceType { get; set; }
            public string IsReceiveOnly { get; set; }
            public string Speed { get; set; }
            public string fCardType { get; set; } = "未知网卡";
            public string PhysicalAddressStr { get; set; }
            public List<string> AddressList { get; set; } = new List<string>();


            //System.Console.WriteLine("Id .................. : {0}", adapter.Id); // 获取网络适配器的标识符  
            //System.Console.WriteLine("Name ................ : {0}", adapter.Name); // 获取网络适配器的名称  
            //System.Console.WriteLine("Description ......... : {0}", adapter.Description); // 获取接口的描述  
            //System.Console.WriteLine("Interface type ...... : {0}", adapter.NetworkInterfaceType); // 获取接口类型  
            //System.Console.WriteLine("Is receive only...... : {0}", adapter.IsReceiveOnly); // 获取 Boolean 值，该值指示网络接口是否设置为仅接收数据包。  
            //System.Console.WriteLine("Multicast............ : {0}", adapter.SupportsMulticast); // 获取 Boolean 值，该值指示是否启用网络接口以接收多路广播数据包。  
            //System.Console.WriteLine("Speed ............... : {0}", adapter.Speed); // 网络接口的速度  
        }
    }
}
