﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using zgUtils.Controls;
using zgUtils.Security;

namespace zgAdvert
{
    public class ConfigManager
    {
        public static string Advert_BaseUrl { get; set; } = "http://47.95.193.177:7004";
        public static string SystemName { get; set; } = "zgzn";
        public static string SubSystemName { get; set; } = "clothing";
        public static readonly string Scheme = "https";
        public static readonly string DomainName = "www.zgpos.com";
        public static string BaseDirectory { get; set; } = AppDomain.CurrentDomain.BaseDirectory;
        public static string FileDir { get { return Path.Combine(BaseDirectory, SubSystemName, "advert"); } }
        public static string CacheDir
        {
            get
            {
                return Path.Combine(FileDir, "cache");
            }
        }
        public static string DeviceId { get; set; }
        public static string Advert_LocalUrl { get; set; } = ConfigManager.Scheme + @"://" + ConfigManager.DomainName + @"/advert/cache/";
        //public static object IsActivate { get; set; }
        public static string Authorization { get; set; } = "Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1aWQiOjAsInBhcnRpdGlvbklkIjowLCJjcmVhdGVkIjoxNjI2NjY5NjUyMTE3LCJzdWJOYW1lIjoiY2xvdGhpbmciLCJzeXNTaWQiOjAsImV4cCI6NDc4MjM0MzI1Mn0.MgQafj8iqySd4li6nASkqD3bRbn-qsDFRbuLX65y9iLWOEallo4buv1trvvNOjGMbBsP1fGDZz3aG0EfR99-hA";
        //public static readonly string LocalDir = "local";
        //public static readonly string LogoDir = Path.Combine(LocalDir, "logo");
        //public static readonly string LocalPosterDir = Scheme + @"://" + DomainName + @"/advert/";


        //public static string DeviceCode { get; set; }


        public static void Init()
		{
			try
			{
				ConfigManager.Authorization= "Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1aWQiOjEsInBhcnRpdGlvbklkIjoyLCJjcmVhdGVkIjoxNjI1OTA2MjU2NTg0LCJzdWJOYW1lIjoiY2xvdGhpbmciLCJzeXNVaWQiOiIxNzcwNTM1MTkxNCIsInN5c1NpZCI6MSwiZXhwIjoxNjI3MjAyMjU2fQ.cL9WRZyM0fabzLNNQul7_SgO6KOAT-qz7P9TSOLIFwJRs0ONVDx_xxFxJ59uPAUulvx7a0OkMixVUs0ibDwgBg";
				//ConfigManager.BaseUrl = "http://47.95.193.177:7004";//string.Format("https://{0}.zhangguizhinang.com", "advert")
				
				if (!Directory.Exists(ConfigManager.FileDir))
				{
					Directory.CreateDirectory(ConfigManager.FileDir);
				}
				if (!Directory.Exists(ConfigManager.CacheDir))
				{
					Directory.CreateDirectory(ConfigManager.CacheDir);
				}
				
			}
			catch (Exception ex)
			{
				Log.WriterExceptionLog(ex.Message);
			}
			
		}
		
	}
}
